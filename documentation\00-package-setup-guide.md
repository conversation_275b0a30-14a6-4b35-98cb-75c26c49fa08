# Package Setup Guide - Criminal Management System

## Overview
This document outlines the packages that have been installed and configured for the Criminal Management System, focusing on Spatie packages and other essential dependencies.

## ✅ Installed Packages

### Core Spatie Packages
1. **spatie/laravel-permission** (^6.20) - Role-based access control
2. **spatie/laravel-medialibrary** (^11.13) - File and media management
3. **spatie/laravel-activitylog** (^4.10) - Activity logging and audit trails
4. **spatie/laravel-backup** (^9.3) - Database and file backups
5. **spatie/laravel-query-builder** (^6.3) - API query building
6. **spatie/laravel-data** (^4.17) - Data transfer objects
7. **spatie/laravel-sluggable** (^3.7) - URL-friendly slugs

### Additional Essential Packages
8. **intervention/image** - Image processing and manipulation
9. **maatwebsite/excel** - Excel import/export functionality
10. **barryvdh/laravel-dompdf** - PDF generation
11. **predis/predis** (^3.0) - Redis client for caching
12. **pusher/pusher-php-server** - Real-time notifications
13. **league/flysystem-aws-s3-v3** - AWS S3 file storage
14. **aws/aws-sdk-php** - AWS services integration

### Existing Laravel Packages
- **Laravel 11** - Core framework
- **Laravel Jetstream** (^5.3) - Authentication scaffolding
- **Laravel Sanctum** (^4.0) - API authentication
- **Filament** (^3.3) - Admin panel (already configured)
- **Livewire** (^3.0) - Dynamic frontend components

## 📋 Published Configurations

### Migrations Published
- ✅ `create_permission_tables.php` - Roles and permissions
- ✅ `create_media_table.php` - Media library
- ✅ `create_activity_log_table.php` - Activity logging
- ✅ `add_event_column_to_activity_log_table.php` - Activity log enhancement
- ✅ `add_batch_uuid_column_to_activity_log_table.php` - Batch tracking

### Configuration Files Published
- ✅ `config/permission.php` - Permission settings
- ✅ `config/media-library.php` - Media library settings
- ✅ `config/activitylog.php` - Activity log settings
- ✅ `config/backup.php` - Backup configuration

## 🔧 Package Purposes for Criminal Management System

### 1. Spatie Laravel Permission
**Purpose**: Role-based access control for different user types
- **Police Officers**: Basic case access
- **Detectives**: Investigation management
- **Supervisors**: Case oversight and approval
- **Administrators**: System management
- **Court Officials**: Court case access

**Key Features**:
- Role assignment and management
- Permission-based access control
- Guard-based permissions for different user types

### 2. Spatie Media Library
**Purpose**: Manage evidence files, criminal photos, and documents
- **Evidence Photos**: Crime scene images, evidence documentation
- **Criminal Mugshots**: Biometric photos and identification images
- **Document Storage**: Case files, court documents, reports
- **Digital Evidence**: Video files, audio recordings

**Key Features**:
- File upload and storage
- Image conversions and thumbnails
- File organization with collections
- Secure file access control

### 3. Spatie Activity Log
**Purpose**: Audit trail for all system activities
- **Case Modifications**: Track changes to case files
- **Evidence Handling**: Log evidence access and modifications
- **User Actions**: Monitor user activities for security
- **System Changes**: Track administrative changes

**Key Features**:
- Automatic activity logging
- Custom event logging
- User action tracking
- Audit trail reports

### 4. Spatie Backup
**Purpose**: Automated system backups for data protection
- **Database Backups**: Regular database snapshots
- **File Backups**: Evidence and document backups
- **Scheduled Backups**: Automated backup scheduling
- **Cloud Storage**: Backup to AWS S3 or other cloud services

### 5. Spatie Query Builder
**Purpose**: Advanced search and filtering for criminal data
- **Criminal Search**: Complex criminal profile searches
- **Case Filtering**: Advanced case filtering and sorting
- **Evidence Search**: Evidence tracking and retrieval
- **Report Generation**: Dynamic report building

### 6. Intervention Image
**Purpose**: Image processing for evidence and criminal photos
- **Photo Resizing**: Optimize images for web display
- **Thumbnail Generation**: Create thumbnails for quick viewing
- **Image Watermarking**: Add security watermarks to evidence photos
- **Format Conversion**: Convert between image formats

### 7. Maatwebsite Excel
**Purpose**: Data import/export functionality
- **Criminal Data Import**: Bulk import criminal records
- **Case Reports**: Export case data to Excel
- **Evidence Lists**: Generate evidence inventory reports
- **Statistical Reports**: Export crime statistics

## 🚀 Next Steps for Implementation

### 1. Database Setup
```bash
# Fix database collation issue first
# Update config/database.php to use utf8mb4_unicode_ci instead of utf8mb4_0900_ai_ci

# Run migrations
php artisan migrate
```

### 2. Configure Permissions
```bash
# Create basic roles and permissions
php artisan make:seeder RolePermissionSeeder
```

### 3. Configure Media Library
```bash
# Set up file storage for evidence and documents
# Configure disk storage in config/filesystems.php
```

### 4. Set Up Activity Logging
```bash
# Configure models to use activity logging
# Add LogsActivity trait to relevant models
```

### 5. Configure Backup
```bash
# Set up backup destinations
# Configure backup schedule
```

## 🔐 Security Considerations

### File Storage Security
- Secure file upload validation
- Virus scanning for uploaded files
- Access control for sensitive documents
- Encrypted storage for biometric data

### Activity Logging
- Log all evidence access
- Track case modifications
- Monitor user login/logout
- Alert on suspicious activities

### Permission Management
- Principle of least privilege
- Regular permission audits
- Role-based data access
- Court order compliance