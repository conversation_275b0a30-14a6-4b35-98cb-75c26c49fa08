<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;

class UserController extends Controller
{
    /**
     * Display a listing of all users.
     */
    public function index(Request $request)
    {
        // Start with base query
        $query = User::with('roles');

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('badge_number', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Apply department filter
        if ($request->filled('department')) {
            $query->where('department', $request->department);
        }

        // Get users with pagination
        $users = $query->orderBy('created_at', 'desc')->paginate(10);

        // Append query parameters to pagination links
        $users->appends($request->query());

        return view('users.index', compact('users'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        return view('users.create');
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'badge_number' => 'required|string|max:255|unique:users',
            'department' => 'required|string|max:255',
            'role' => 'required|string|max:255',
            'phone' => 'nullable|string|max:255',
        ]);

        // In a real application, you would create the user here
        // User::create($validatedData);

        return redirect()->route('users.index')->with('success', 'User created successfully!');
    }

    /**
     * Display the specified user.
     */
    public function show(string $id)
    {
        return view('users.show', compact('id'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(string $id)
    {
        return view('users.edit', compact('id'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
            'badge_number' => 'required|string|max:255|unique:users,badge_number,' . $id,
            'department' => 'required|string|max:255',
            'role' => 'required|string|max:255',
            'phone' => 'nullable|string|max:255',
        ]);

        return redirect()->route('users.index')->with('success', 'User updated successfully!');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(string $id)
    {
        return redirect()->route('users.index')->with('success', 'User deleted successfully!');
    }

    /**
     * Display active users.
     */
    public function active()
    {
        return view('users.active');
    }

    /**
     * Display inactive users.
     */
    public function inactive()
    {
        return view('users.inactive');
    }

    /**
     * Activate a user.
     */
    public function activate(string $id)
    {
        return redirect()->back()->with('success', 'User activated successfully!');
    }

    /**
     * Deactivate a user.
     */
    public function deactivate(string $id)
    {
        return redirect()->back()->with('success', 'User deactivated successfully!');
    }

    /**
     * Reset user password.
     */
    public function resetPassword(string $id)
    {
        return redirect()->back()->with('success', 'Password reset email sent successfully!');
    }
}
