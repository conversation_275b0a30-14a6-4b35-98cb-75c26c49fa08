<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class UserController extends Controller
{
    /**
     * Display a listing of all users.
     */
    public function index(Request $request)
    {
        // Start with base query
        $query = User::with('roles');

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('badge_number', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Apply department filter
        if ($request->filled('department')) {
            $query->where('department', $request->department);
        }

        // Get users with pagination
        $users = $query->orderBy('created_at', 'desc')->paginate(10);

        // Append query parameters to pagination links
        $users->appends($request->query());

        return view('users.index', compact('users'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        return view('users.create');
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'badge_number' => 'required|string|max:255|unique:users',
            'department' => 'required|string|max:255',
            'role' => 'required|string|max:255',
            'phone' => 'nullable|string|max:255',
        ]);

        // In a real application, you would create the user here
        // User::create($validatedData);

        return redirect()->route('users.index')->with('success', 'User created successfully!');
    }

    /**
     * Display the specified user.
     */
    public function show(string $id)
    {
        $user = User::with('roles')->findOrFail($id);
        return view('users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(string $id)
    {
        return view('users.edit', compact('id'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
            'badge_number' => 'required|string|max:255|unique:users,badge_number,' . $id,
            'department' => 'required|string|max:255',
            'role' => 'required|string|max:255',
            'phone' => 'nullable|string|max:255',
        ]);

        return redirect()->route('users.index')->with('success', 'User updated successfully!');
    }


    /**
     * Display active users.
     */
    public function active()
    {
        return view('users.active');
    }

    /**
     * Display inactive users.
     */
    public function inactive()
    {
        return view('users.inactive');
    }

    /**
     * Activate a user.
     */
    public function activate(string $id)
    {
        return redirect()->back()->with('success', 'User activated successfully!');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(string $id)
    {
        try {
            $user = User::findOrFail($id);

            // Prevent deletion of current user
            if ($user->id === auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot delete your own account.'
                ], 400);
            }

            $user->delete();

            return response()->json([
                'success' => true,
                'message' => 'User deleted successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting user: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset user password
     */
    public function resetPassword(string $id)
    {
        try {
            $user = User::findOrFail($id);

            // Generate a temporary password
            $tempPassword = 'temp' . rand(1000, 9999);
            $user->password = bcrypt($tempPassword);
            $user->save();

            // In a real application, you would send an email with the new password

            return response()->json([
                'success' => true,
                'message' => "Password reset successfully! Temporary password: {$tempPassword}",
                'temp_password' => $tempPassword
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error resetting password: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle user status (activate/deactivate)
     */
    public function toggleStatus(string $id)
    {
        try {
            $user = User::findOrFail($id);

            // Prevent deactivating current user
            if ($user->id === auth()->id() && $user->status === 'active') {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot deactivate your own account.'
                ], 400);
            }

            $newStatus = $user->status === 'active' ? 'inactive' : 'active';
            $user->status = $newStatus;
            $user->save();

            $action = $newStatus === 'active' ? 'activated' : 'deactivated';

            return response()->json([
                'success' => true,
                'message' => "User {$action} successfully!",
                'new_status' => $newStatus
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating user status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk actions for multiple users
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete,reset_password',
            'user_ids' => 'required|array|min:1',
            'user_ids.*' => 'exists:users,id'
        ]);

        try {
            $userIds = $request->user_ids;
            $action = $request->action;
            $currentUserId = auth()->id();

            // Remove current user from bulk actions for safety
            $userIds = array_filter($userIds, function($id) use ($currentUserId) {
                return $id != $currentUserId;
            });

            if (empty($userIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot perform bulk actions on your own account.'
                ], 400);
            }

            $users = User::whereIn('id', $userIds)->get();
            $count = $users->count();

            switch ($action) {
                case 'activate':
                    User::whereIn('id', $userIds)->update(['status' => 'active']);
                    $message = "{$count} user(s) activated successfully!";
                    break;

                case 'deactivate':
                    User::whereIn('id', $userIds)->update(['status' => 'inactive']);
                    $message = "{$count} user(s) deactivated successfully!";
                    break;

                case 'delete':
                    User::whereIn('id', $userIds)->delete();
                    $message = "{$count} user(s) deleted successfully!";
                    break;

                case 'reset_password':
                    foreach ($users as $user) {
                        $tempPassword = 'temp' . rand(1000, 9999);
                        $user->password = bcrypt($tempPassword);
                        $user->save();
                    }
                    $message = "{$count} user password(s) reset successfully!";
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error performing bulk action: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the user's profile information.
     */
    public function updateProfileInformation(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . auth()->id(),
            'phone' => 'nullable|string|max:20',
            'badge_number' => 'nullable|string|max:20|unique:users,badge_number,' . auth()->id(),
            'department' => 'nullable|string|max:255',
        ]);

        $user = auth()->user();
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'badge_number' => $request->badge_number,
            'department' => $request->department,
        ]);

        return back()->with('status', 'Profile information updated successfully!');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if (!Hash::check($request->current_password, auth()->user()->password)) {
            return back()->withErrors(['current_password' => 'The current password is incorrect.']);
        }

        auth()->user()->update([
            'password' => Hash::make($request->password),
        ]);

        return back()->with('status', 'Password updated successfully!');
    }

    /**
     * Delete the user's account.
     */
    public function deleteUser(Request $request)
    {
        $request->validate([
            'password' => 'required|string',
        ]);

        if (!Hash::check($request->password, auth()->user()->password)) {
            return back()->withErrors(['password' => 'The password is incorrect.']);
        }

        $user = auth()->user();

        // Log out the user
        auth()->logout();

        // Delete the user
        $user->delete();

        return redirect('/')->with('status', 'Your account has been deleted successfully.');
    }
}
