<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;

class UserController extends Controller
{
    /**
     * Display a listing of all users.
     */
    public function index()
    {
        // Mock user data with pagination
        // In a real application, this would be: User::paginate(10)
        $users = collect([
            (object)[
                'id' => 1,
                'name' => 'Inspector <PERSON><PERSON>',
                'email' => '<EMAIL>',
                'badge_number' => 'PB001',
                'role' => 'inspector',
                'department' => 'Criminal Investigation',
                'phone' => '+265 999 123 456',
                'status' => 'active',
                'last_login' => '2024-12-15 08:30:00',
                'is_online' => true,
                'created_at' => '2024-01-15',
            ],
            (object)[
                'id' => 2,
                'name' => 'Sergeant <PERSON><PERSON>',
                'email' => '<EMAIL>',
                'badge_number' => 'PB002',
                'role' => 'sergeant',
                'department' => 'Traffic Police',
                'phone' => '+265 999 234 567',
                'status' => 'active',
                'last_login' => '2024-12-15 07:45:00',
                'is_online' => false,
                'created_at' => '2024-02-20',
            ],
            (object)[
                'id' => 3,
                'name' => 'Constable Thokozani Phiri',
                'email' => '<EMAIL>',
                'badge_number' => 'PB003',
                'role' => 'constable',
                'department' => 'Community Policing',
                'phone' => '+265 999 345 678',
                'status' => 'active',
                'last_login' => '2024-12-14 16:20:00',
                'is_online' => false,
                'created_at' => '2024-03-10',
            ],
            (object)[
                'id' => 4,
                'name' => 'Constable Mwawi Kumwenda',
                'email' => '<EMAIL>',
                'badge_number' => 'PB004',
                'role' => 'constable',
                'department' => 'Administration',
                'phone' => '+265 999 456 789',
                'status' => 'suspended',
                'last_login' => '2024-12-10 15:15:00',
                'is_online' => false,
                'created_at' => '2024-04-05',
            ],
            (object)[
                'id' => 5,
                'name' => 'Sergeant John Nyirenda',
                'email' => '<EMAIL>',
                'badge_number' => 'PB005',
                'role' => 'sergeant',
                'department' => 'Forensics',
                'phone' => '+265 999 567 890',
                'status' => 'inactive',
                'last_login' => '2024-12-05 11:30:00',
                'is_online' => false,
                'created_at' => '2024-05-12',
            ],
            (object)[
                'id' => 6,
                'name' => 'Constable Lucy Mbewe',
                'email' => '<EMAIL>',
                'badge_number' => 'PB006',
                'role' => 'constable',
                'department' => 'Community Policing',
                'phone' => '+265 999 678 901',
                'status' => 'pending',
                'last_login' => null,
                'is_online' => false,
                'created_at' => '2024-12-01',
            ],
        ]);

        // Create a manual paginator for demonstration
        $currentPage = request()->get('page', 1);
        $perPage = 5;
        $total = $users->count();
        $items = $users->forPage($currentPage, $perPage);

        $users = new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );

        return view('users.index', compact('users'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        return view('users.create');
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'badge_number' => 'required|string|max:255|unique:users',
            'department' => 'required|string|max:255',
            'role' => 'required|string|max:255',
            'phone' => 'nullable|string|max:255',
        ]);

        // In a real application, you would create the user here
        // User::create($validatedData);

        return redirect()->route('users.index')->with('success', 'User created successfully!');
    }

    /**
     * Display the specified user.
     */
    public function show(string $id)
    {
        return view('users.show', compact('id'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(string $id)
    {
        return view('users.edit', compact('id'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
            'badge_number' => 'required|string|max:255|unique:users,badge_number,' . $id,
            'department' => 'required|string|max:255',
            'role' => 'required|string|max:255',
            'phone' => 'nullable|string|max:255',
        ]);

        return redirect()->route('users.index')->with('success', 'User updated successfully!');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(string $id)
    {
        return redirect()->route('users.index')->with('success', 'User deleted successfully!');
    }

    /**
     * Display active users.
     */
    public function active()
    {
        return view('users.active');
    }

    /**
     * Display inactive users.
     */
    public function inactive()
    {
        return view('users.inactive');
    }

    /**
     * Activate a user.
     */
    public function activate(string $id)
    {
        return redirect()->back()->with('success', 'User activated successfully!');
    }

    /**
     * Deactivate a user.
     */
    public function deactivate(string $id)
    {
        return redirect()->back()->with('success', 'User deactivated successfully!');
    }

    /**
     * Reset user password.
     */
    public function resetPassword(string $id)
    {
        return redirect()->back()->with('success', 'Password reset email sent successfully!');
    }
}
