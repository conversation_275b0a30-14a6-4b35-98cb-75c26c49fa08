@extends('layouts.app')

@section('title', 'Create Role - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Create New Role</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'Role Management', 'url' => route('roles.index')],
                ['title' => 'Create Role']
            ]" />
        </div>
        <div>
            <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Roles
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('roles.store') }}">
        @csrf
        
        <!-- Role Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="shield" class="icon-sm me-2"></i>
                    Role Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">Role Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required
                               placeholder="e.g., detective" value="{{ old('name') }}">
                        <small class="text-muted">Lowercase, no spaces. Used internally by the system.</small>
                        @error('name')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="display_name" class="form-label">Display Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="display_name" name="display_name" required
                               placeholder="e.g., Detective" value="{{ old('display_name') }}">
                        <small class="text-muted">Human-readable name shown to users.</small>
                        @error('display_name')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Describe the role and its responsibilities...">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="key" class="icon-sm me-2"></i>
                    Role Permissions
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Case Management</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="view_cases" name="permissions[]" value="view_cases">
                            <label class="form-check-label" for="view_cases">
                                View Cases
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="create_cases" name="permissions[]" value="create_cases">
                            <label class="form-check-label" for="create_cases">
                                Create Cases
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="edit_cases" name="permissions[]" value="edit_cases">
                            <label class="form-check-label" for="edit_cases">
                                Edit Cases
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="delete_cases" name="permissions[]" value="delete_cases">
                            <label class="form-check-label" for="delete_cases">
                                Delete Cases
                            </label>
                        </div>
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="assign_cases" name="permissions[]" value="assign_cases">
                            <label class="form-check-label" for="assign_cases">
                                Assign Cases
                            </label>
                        </div>

                        <h6 class="text-info">Evidence Management</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="view_evidence" name="permissions[]" value="view_evidence">
                            <label class="form-check-label" for="view_evidence">
                                View Evidence
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="add_evidence" name="permissions[]" value="add_evidence">
                            <label class="form-check-label" for="add_evidence">
                                Add Evidence
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="edit_evidence" name="permissions[]" value="edit_evidence">
                            <label class="form-check-label" for="edit_evidence">
                                Edit Evidence
                            </label>
                        </div>
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="chain_custody" name="permissions[]" value="chain_custody">
                            <label class="form-check-label" for="chain_custody">
                                Manage Chain of Custody
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">Criminal Profiles</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="view_criminals" name="permissions[]" value="view_criminals">
                            <label class="form-check-label" for="view_criminals">
                                View Criminal Profiles
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="create_criminals" name="permissions[]" value="create_criminals">
                            <label class="form-check-label" for="create_criminals">
                                Create Profiles
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="edit_criminals" name="permissions[]" value="edit_criminals">
                            <label class="form-check-label" for="edit_criminals">
                                Edit Profiles
                            </label>
                        </div>
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="delete_criminals" name="permissions[]" value="delete_criminals">
                            <label class="form-check-label" for="delete_criminals">
                                Delete Profiles
                            </label>
                        </div>

                        <h6 class="text-success">Reports & Analytics</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="view_reports" name="permissions[]" value="view_reports">
                            <label class="form-check-label" for="view_reports">
                                View Reports
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="generate_reports" name="permissions[]" value="generate_reports">
                            <label class="form-check-label" for="generate_reports">
                                Generate Reports
                            </label>
                        </div>
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="system_reports" name="permissions[]" value="system_reports">
                            <label class="form-check-label" for="system_reports">
                                System Reports
                            </label>
                        </div>

                        <h6 class="text-danger">System Administration</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="manage_users" name="permissions[]" value="manage_users">
                            <label class="form-check-label" for="manage_users">
                                Manage Users
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="manage_roles" name="permissions[]" value="manage_roles">
                            <label class="form-check-label" for="manage_roles">
                                Manage Roles
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="system_settings" name="permissions[]" value="system_settings">
                            <label class="form-check-label" for="system_settings">
                                System Settings
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="manage_backups" name="permissions[]" value="manage_backups">
                            <label class="form-check-label" for="manage_backups">
                                Backup Management
                            </label>
                        </div>
                    </div>
                </div>

                @error('permissions')
                    <div class="text-danger small">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <!-- Quick Permission Sets -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="zap" class="icon-sm me-2"></i>
                    Quick Permission Sets
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="setPermissions('admin')">
                            <i data-feather="shield" class="icon-xs me-2"></i>
                            Administrator
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button type="button" class="btn btn-outline-info w-100" onclick="setPermissions('inspector')">
                            <i data-feather="user-check" class="icon-xs me-2"></i>
                            Inspector
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button type="button" class="btn btn-outline-success w-100" onclick="setPermissions('officer')">
                            <i data-feather="user" class="icon-xs me-2"></i>
                            Officer
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button type="button" class="btn btn-outline-secondary w-100" onclick="setPermissions('viewer')">
                            <i data-feather="eye" class="icon-xs me-2"></i>
                            Viewer Only
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-end gap-2 mb-4">
            <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary">
                <i data-feather="x" class="icon-xs me-2"></i>
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i data-feather="save" class="icon-xs me-2"></i>
                Create Role
            </button>
        </div>
    </form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});

function setPermissions(roleType) {
    // Clear all checkboxes first
    document.querySelectorAll('input[name="permissions[]"]').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Define permission sets
    const permissionSets = {
        admin: [
            'view_cases', 'create_cases', 'edit_cases', 'delete_cases', 'assign_cases',
            'view_evidence', 'add_evidence', 'edit_evidence', 'chain_custody',
            'view_criminals', 'create_criminals', 'edit_criminals', 'delete_criminals',
            'view_reports', 'generate_reports', 'system_reports',
            'manage_users', 'manage_roles', 'system_settings', 'manage_backups'
        ],
        inspector: [
            'view_cases', 'create_cases', 'edit_cases', 'assign_cases',
            'view_evidence', 'add_evidence', 'edit_evidence', 'chain_custody',
            'view_criminals', 'create_criminals', 'edit_criminals',
            'view_reports', 'generate_reports'
        ],
        officer: [
            'view_cases', 'create_cases', 'edit_cases',
            'view_evidence', 'add_evidence',
            'view_criminals', 'create_criminals',
            'view_reports'
        ],
        viewer: [
            'view_cases', 'view_evidence', 'view_criminals', 'view_reports'
        ]
    };

    // Check the appropriate permissions
    if (permissionSets[roleType]) {
        permissionSets[roleType].forEach(permission => {
            const checkbox = document.getElementById(permission);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }
}
</script>
@endpush
