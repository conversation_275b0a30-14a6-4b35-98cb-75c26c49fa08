<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class CriminalBiometric extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'criminal_id',
        'biometric_type',
        'file_path',
        'file_hash',
        'metadata',
        'collected_date',
        'collected_by',
        'verified',
        'description',
        'notes',
    ];

    protected $casts = [
        'metadata' => 'array',
        'collected_date' => 'date',
        'verified' => 'boolean',
    ];

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['biometric_type', 'verified'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the criminal that owns the biometric data.
     */
    public function criminal()
    {
        return $this->belongsTo(Criminal::class);
    }

    /**
     * Get the user who collected the biometric data.
     */
    public function collector()
    {
        return $this->belongsTo(User::class, 'collected_by');
    }

    /**
     * Scope for verified biometrics.
     */
    public function scopeVerified($query)
    {
        return $query->where('verified', true);
    }

    /**
     * Scope for specific biometric type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('biometric_type', $type);
    }
}
