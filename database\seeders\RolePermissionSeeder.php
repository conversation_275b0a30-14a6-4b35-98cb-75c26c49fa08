<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions for Criminal Management System
        $this->createPermissions();

        // Create roles and assign permissions
        $this->createRoles();

        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    /**
     * Create all permissions for the Criminal Management System
     */
    private function createPermissions(): void
    {
        $permissions = [
            // Dashboard Permissions
            'view-dashboard',
            'view-analytics',
            'view-crime-statistics',
            'export-dashboard-data',

            // Criminal Profile Management Permissions
            'view-criminals',
            'create-criminals',
            'edit-criminals',
            'delete-criminals',
            'view-criminal-details',
            'manage-criminal-biometrics',
            'upload-criminal-photos',
            'view-criminal-history',
            'export-criminal-data',

            // Case Management Permissions
            'view-cases',
            'create-cases',
            'edit-cases',
            'delete-cases',
            'assign-cases',
            'close-cases',
            'reopen-cases',
            'view-case-details',
            'manage-case-status',
            'export-case-data',

            // Evidence Management Permissions
            'view-evidence',
            'create-evidence',
            'edit-evidence',
            'delete-evidence',
            'upload-evidence',
            'manage-chain-of-custody',
            'transfer-evidence',
            'dispose-evidence',
            'view-forensic-analysis',
            'manage-forensic-analysis',
            'export-evidence-data',

            // Personal Belongings Management Permissions
            'view-belongings',
            'create-belongings',
            'edit-belongings',
            'delete-belongings',
            'release-belongings',
            'dispose-belongings',
            'manage-belongings-inventory',
            'export-belongings-data',

            // Court Case Management Permissions
            'view-court-cases',
            'create-court-cases',
            'edit-court-cases',
            'delete-court-cases',
            'schedule-hearings',
            'manage-court-outcomes',
            'manage-bail',
            'manage-parole',
            'view-court-calendar',
            'export-court-data',

            // Document Management Permissions
            'view-documents',
            'upload-documents',
            'edit-documents',
            'delete-documents',
            'share-documents',
            'manage-document-categories',
            'view-document-history',
            'export-documents',

            // User Management Permissions
            'view-users',
            'create-users',
            'edit-users',
            'delete-users',
            'manage-user-roles',
            'manage-user-permissions',
            'view-user-activity',
            'reset-user-passwords',

            // System Administration Permissions
            'manage-system-settings',
            'view-audit-logs',
            'manage-backups',
            'view-system-reports',
            'manage-system-maintenance',
            'configure-system',

            // Reporting and Analytics Permissions
            'view-reports',
            'create-reports',
            'export-reports',
            'view-crime-analytics',
            'view-performance-metrics',
            'generate-statistical-reports',

            // Security and Compliance Permissions
            'view-security-logs',
            'manage-access-control',
            'view-compliance-reports',
            'manage-data-retention',
            'audit-system-access',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }
    }

    /**
     * Create roles for the Criminal Management System
     */
    private function createRoles(): void
    {
        $roles = [
            'Super Administrator' => 'Full system access and administration',
            'System Administrator' => 'System configuration and user management',
            'Police Chief' => 'Department oversight and high-level access',
            'Detective Supervisor' => 'Investigation oversight and case management',
            'Detective' => 'Investigation and case management',
            'Police Officer' => 'Basic case access and evidence handling',
            'Evidence Custodian' => 'Evidence and belongings management',
            'Court Clerk' => 'Court case management and scheduling',
            'Data Entry Clerk' => 'Basic data entry and document management',
            'Forensic Analyst' => 'Forensic analysis and evidence examination',
            'Prosecutor' => 'Case review and court proceedings',
            'Judge' => 'Court case review and decision making',
            'Auditor' => 'System audit and compliance review',
            'Guest' => 'Limited read-only access',
        ];

        foreach ($roles as $roleName => $description) {
            Role::firstOrCreate(['name' => $roleName]);
        }
    }

    /**
     * Assign permissions to roles based on Criminal Justice hierarchy
     */
    private function assignPermissionsToRoles(): void
    {
        // Super Administrator - All permissions
        $superAdmin = Role::findByName('Super Administrator');
        $superAdmin->givePermissionTo(Permission::all());

        // System Administrator - System management and user administration
        $systemAdmin = Role::findByName('System Administrator');
        $systemAdmin->givePermissionTo([
            'view-dashboard', 'view-analytics', 'view-crime-statistics',
            'view-users', 'create-users', 'edit-users', 'delete-users',
            'manage-user-roles', 'manage-user-permissions', 'view-user-activity',
            'reset-user-passwords', 'manage-system-settings', 'view-audit-logs',
            'manage-backups', 'view-system-reports', 'manage-system-maintenance',
            'configure-system', 'view-security-logs', 'manage-access-control',
            'view-compliance-reports', 'manage-data-retention', 'audit-system-access',
        ]);

        // Police Chief - High-level oversight
        $policeChief = Role::findByName('Police Chief');
        $policeChief->givePermissionTo([
            'view-dashboard', 'view-analytics', 'view-crime-statistics', 'export-dashboard-data',
            'view-criminals', 'view-criminal-details', 'view-criminal-history', 'export-criminal-data',
            'view-cases', 'view-case-details', 'assign-cases', 'close-cases', 'export-case-data',
            'view-evidence', 'view-forensic-analysis', 'export-evidence-data',
            'view-court-cases', 'view-court-calendar', 'export-court-data',
            'view-documents', 'view-document-history', 'export-documents',
            'view-reports', 'create-reports', 'export-reports', 'view-crime-analytics',
            'view-performance-metrics', 'generate-statistical-reports',
            'view-user-activity', 'view-audit-logs', 'view-security-logs',
        ]);

        // Detective Supervisor - Investigation oversight
        $detectiveSupervisor = Role::findByName('Detective Supervisor');
        $detectiveSupervisor->givePermissionTo([
            'view-dashboard', 'view-analytics', 'view-crime-statistics',
            'view-criminals', 'create-criminals', 'edit-criminals', 'view-criminal-details',
            'manage-criminal-biometrics', 'upload-criminal-photos', 'view-criminal-history',
            'view-cases', 'create-cases', 'edit-cases', 'assign-cases', 'close-cases',
            'reopen-cases', 'view-case-details', 'manage-case-status',
            'view-evidence', 'create-evidence', 'edit-evidence', 'upload-evidence',
            'manage-chain-of-custody', 'transfer-evidence', 'view-forensic-analysis',
            'manage-forensic-analysis', 'view-court-cases', 'create-court-cases',
            'edit-court-cases', 'schedule-hearings', 'view-documents', 'upload-documents',
            'edit-documents', 'share-documents', 'view-reports', 'create-reports',
            'export-reports', 'view-crime-analytics',
        ]);

        // Detective - Investigation and case management
        $detective = Role::findByName('Detective');
        $detective->givePermissionTo([
            'view-dashboard', 'view-crime-statistics',
            'view-criminals', 'create-criminals', 'edit-criminals', 'view-criminal-details',
            'manage-criminal-biometrics', 'upload-criminal-photos', 'view-criminal-history',
            'view-cases', 'create-cases', 'edit-cases', 'view-case-details', 'manage-case-status',
            'view-evidence', 'create-evidence', 'edit-evidence', 'upload-evidence',
            'manage-chain-of-custody', 'view-forensic-analysis',
            'view-court-cases', 'create-court-cases', 'edit-court-cases',
            'view-documents', 'upload-documents', 'edit-documents', 'share-documents',
            'view-reports', 'create-reports',
        ]);

        // Police Officer - Basic operations
        $policeOfficer = Role::findByName('Police Officer');
        $policeOfficer->givePermissionTo([
            'view-dashboard',
            'view-criminals', 'create-criminals', 'view-criminal-details', 'upload-criminal-photos',
            'view-cases', 'create-cases', 'view-case-details',
            'view-evidence', 'create-evidence', 'upload-evidence', 'manage-chain-of-custody',
            'view-belongings', 'create-belongings', 'edit-belongings',
            'view-documents', 'upload-documents',
        ]);

        // Evidence Custodian - Evidence and belongings management
        $evidenceCustodian = Role::findByName('Evidence Custodian');
        $evidenceCustodian->givePermissionTo([
            'view-dashboard',
            'view-evidence', 'create-evidence', 'edit-evidence', 'upload-evidence',
            'manage-chain-of-custody', 'transfer-evidence', 'dispose-evidence',
            'view-belongings', 'create-belongings', 'edit-belongings', 'release-belongings',
            'dispose-belongings', 'manage-belongings-inventory',
            'view-documents', 'upload-documents', 'edit-documents',
        ]);

        // Court Clerk - Court case management
        $courtClerk = Role::findByName('Court Clerk');
        $courtClerk->givePermissionTo([
            'view-dashboard',
            'view-criminals', 'view-criminal-details',
            'view-cases', 'view-case-details',
            'view-court-cases', 'create-court-cases', 'edit-court-cases', 'schedule-hearings',
            'manage-court-outcomes', 'manage-bail', 'manage-parole', 'view-court-calendar',
            'view-documents', 'upload-documents', 'edit-documents',
        ]);

        // Data Entry Clerk - Basic data entry
        $dataEntryClerk = Role::findByName('Data Entry Clerk');
        $dataEntryClerk->givePermissionTo([
            'view-dashboard',
            'view-criminals', 'create-criminals', 'edit-criminals',
            'view-cases', 'create-cases', 'edit-cases',
            'view-documents', 'upload-documents', 'edit-documents',
        ]);

        // Forensic Analyst - Forensic analysis
        $forensicAnalyst = Role::findByName('Forensic Analyst');
        $forensicAnalyst->givePermissionTo([
            'view-dashboard',
            'view-criminals', 'view-criminal-details', 'manage-criminal-biometrics',
            'view-cases', 'view-case-details',
            'view-evidence', 'edit-evidence', 'view-forensic-analysis', 'manage-forensic-analysis',
            'view-documents', 'upload-documents', 'edit-documents',
        ]);

        // Prosecutor - Case review and court proceedings
        $prosecutor = Role::findByName('Prosecutor');
        $prosecutor->givePermissionTo([
            'view-dashboard', 'view-crime-statistics',
            'view-criminals', 'view-criminal-details', 'view-criminal-history',
            'view-cases', 'view-case-details', 'manage-case-status',
            'view-evidence', 'view-forensic-analysis',
            'view-court-cases', 'edit-court-cases', 'manage-court-outcomes', 'manage-bail',
            'view-documents', 'view-document-history',
            'view-reports', 'create-reports',
        ]);

        // Judge - Court case review and decisions
        $judge = Role::findByName('Judge');
        $judge->givePermissionTo([
            'view-dashboard',
            'view-criminals', 'view-criminal-details', 'view-criminal-history',
            'view-cases', 'view-case-details',
            'view-evidence', 'view-forensic-analysis',
            'view-court-cases', 'manage-court-outcomes', 'manage-bail', 'manage-parole',
            'view-documents', 'view-document-history',
        ]);

        // Auditor - System audit and compliance
        $auditor = Role::findByName('Auditor');
        $auditor->givePermissionTo([
            'view-dashboard', 'view-analytics', 'view-crime-statistics',
            'view-criminals', 'view-criminal-details', 'view-criminal-history',
            'view-cases', 'view-case-details',
            'view-evidence', 'view-forensic-analysis',
            'view-court-cases', 'view-court-calendar',
            'view-documents', 'view-document-history',
            'view-users', 'view-user-activity',
            'view-audit-logs', 'view-system-reports', 'view-security-logs',
            'view-compliance-reports', 'audit-system-access',
            'view-reports', 'create-reports', 'export-reports', 'view-crime-analytics',
            'view-performance-metrics', 'generate-statistical-reports',
        ]);

        // Guest - Limited read-only access
        $guest = Role::findByName('Guest');
        $guest->givePermissionTo([
            'view-dashboard',
            'view-criminals', 'view-criminal-details',
            'view-cases', 'view-case-details',
            'view-documents',
        ]);
    }
}
