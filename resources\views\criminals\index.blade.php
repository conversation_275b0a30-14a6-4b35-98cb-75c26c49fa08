@extends('layouts.app')

@section('title', 'Criminal Profiles  - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Criminal Profiles</h1>
            <x-breadcrumb :items="[
                ['title' => 'Criminal Profiles']
            ]" />
        </div>
        <div>
            <a href="{{ route('criminals.create') }}" class="btn btn-primary">
                <i data-feather="user-plus" class="icon-xs me-2"></i>
                Add New Criminal
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('criminals.index') }}">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ request('search') }}" 
                                       placeholder="Name, Criminal Number, or National ID">
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="Active" {{ request('status') == 'Active' ? 'selected' : '' }}>Active</option>
                                    <option value="Inactive" {{ request('status') == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                                    <option value="Deceased" {{ request('status') == 'Deceased' ? 'selected' : '' }}>Deceased</option>
                                    <option value="Deported" {{ request('status') == 'Deported' ? 'selected' : '' }}>Deported</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="risk_level" class="form-label">Risk Level</label>
                                <select class="form-select" id="risk_level" name="risk_level">
                                    <option value="">All Levels</option>
                                    <option value="Low" {{ request('risk_level') == 'Low' ? 'selected' : '' }}>Low</option>
                                    <option value="Medium" {{ request('risk_level') == 'Medium' ? 'selected' : '' }}>Medium</option>
                                    <option value="High" {{ request('risk_level') == 'High' ? 'selected' : '' }}>High</option>
                                    <option value="Critical" {{ request('risk_level') == 'Critical' ? 'selected' : '' }}>Critical</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="district" class="form-label">District</label>
                                <select class="form-select" id="district" name="district">
                                    <option value="">All Districts</option>
                                    <option value="Lilongwe" {{ request('district') == 'Lilongwe' ? 'selected' : '' }}>Lilongwe</option>
                                    <option value="Blantyre" {{ request('district') == 'Blantyre' ? 'selected' : '' }}>Blantyre</option>
                                    <option value="Mzuzu" {{ request('district') == 'Mzuzu' ? 'selected' : '' }}>Mzuzu</option>
                                    <option value="Zomba" {{ request('district') == 'Zomba' ? 'selected' : '' }}>Zomba</option>
                                    <option value="Kasungu" {{ request('district') == 'Kasungu' ? 'selected' : '' }}>Kasungu</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i data-feather="search" class="icon-xs me-2"></i>
                                        Search
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="wanted_only" name="wanted_only" value="1" 
                                           {{ request('wanted_only') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="wanted_only">
                                        Show wanted criminals only
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">{{ $criminals->total() }}</h4>
                    <p class="mb-0">Total Results</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning">{{ $criminals->where('is_wanted', true)->count() }}</h4>
                    <p class="mb-0">Wanted</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-danger">{{ $criminals->whereIn('risk_level', ['High', 'Critical'])->count() }}</h4>
                    <p class="mb-0">High Risk</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-success">{{ $criminals->where('status', 'Active')->count() }}</h4>
                    <p class="mb-0">Active</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Criminals List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Criminal Records</h4>
                </div>
                <div class="card-body">
                    @if($criminals->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Photo</th>
                                        <th>Criminal Number</th>
                                        <th>Name</th>
                                        <th>Age</th>
                                        <th>District</th>
                                        <th>Status</th>
                                        <th>Risk Level</th>
                                        <th>Wanted</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($criminals as $criminal)
                                    <tr>
                                        <td>
                                            @if($criminal->getFirstMediaUrl('mugshots'))
                                                <img src="{{ $criminal->getFirstMediaUrl('mugshots', 'thumb') }}" 
                                                     alt="Mugshot" class="rounded-circle" width="40" height="40">
                                            @else
                                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <i data-feather="user" class="icon-sm text-muted"></i>
                                                </div>
                                            @endif
                                        </td>
                                        <td>
                                            <strong>{{ $criminal->criminal_number }}</strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ $criminal->full_name }}</strong>
                                                @if($criminal->alias)
                                                    <br><small class="text-muted">Alias: {{ $criminal->alias }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>{{ $criminal->age ?? 'N/A' }}</td>
                                        <td>{{ $criminal->district ?? 'N/A' }}</td>
                                        <td>
                                            <span class="badge bg-{{ $criminal->status == 'Active' ? 'success' : 'secondary' }}">
                                                {{ $criminal->status }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 
                                                $criminal->risk_level == 'Critical' ? 'danger' : 
                                                ($criminal->risk_level == 'High' ? 'warning' : 
                                                ($criminal->risk_level == 'Medium' ? 'info' : 'success')) 
                                            }}">
                                                {{ $criminal->risk_level }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($criminal->is_wanted)
                                                <span class="badge bg-danger">
                                                    <i data-feather="alert-triangle" class="icon-xs me-1"></i>
                                                    Wanted
                                                </span>
                                            @else
                                                <span class="text-muted">No</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                        type="button" data-bs-toggle="dropdown">
                                                    Actions
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('criminals.show', $criminal) }}">
                                                            <i data-feather="eye" class="icon-xs me-2"></i>
                                                            View Profile
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('criminals.edit', $criminal) }}">
                                                            <i data-feather="edit" class="icon-xs me-2"></i>
                                                            Edit
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <form method="POST" action="{{ route('criminals.destroy', $criminal) }}" 
                                                              onsubmit="return confirm('Are you sure you want to delete this criminal record?')">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="dropdown-item text-danger">
                                                                <i data-feather="trash-2" class="icon-xs me-2"></i>
                                                                Delete
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>
                                Showing {{ $criminals->firstItem() }} to {{ $criminals->lastItem() }} 
                                of {{ $criminals->total() }} results
                            </div>
                            <div>
                                {{ $criminals->links() }}
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i data-feather="users" class="icon-xl text-muted mb-3"></i>
                            <h5 class="text-muted">No criminal records found</h5>
                            <p class="text-muted">Try adjusting your search criteria or add a new criminal profile.</p>
                            <a href="{{ route('criminals.create') }}" class="btn btn-primary">
                                <i data-feather="user-plus" class="icon-xs me-2"></i>
                                Add New Criminal
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
@endpush
