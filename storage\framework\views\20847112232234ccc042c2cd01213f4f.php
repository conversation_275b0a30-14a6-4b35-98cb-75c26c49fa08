<?php $__env->startSection('title', 'Role Management - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Role Management</h1>
            <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => ['items' => [
                ['title' => 'System'],
                ['title' => 'Role Management']
            ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                ['title' => 'System'],
                ['title' => 'Role Management']
            ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
        </div>
        <div class="d-flex gap-2">
            <a href="<?php echo e(route('roles.create')); ?>" class="btn btn-primary">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Create Role
            </a>
        </div>
    </div>

    <!-- Role Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary"><?php echo e($roles->total()); ?></h4>
                    <p class="mb-0">Total Roles</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <?php
                        $systemRoleNames = ['Super Administrator', 'Administrator', 'Inspector', 'Officer', 'Forensic Analyst', 'Court Liaison', 'Viewer'];
                        $systemRolesCount = \Spatie\Permission\Models\Role::whereIn('name', $systemRoleNames)->count();
                    ?>
                    <h4 class="text-success"><?php echo e($systemRolesCount); ?></h4>
                    <p class="mb-0">System Roles</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <?php
                        $totalUsers = \App\Models\User::count();
                    ?>
                    <h4 class="text-info"><?php echo e($totalUsers); ?></h4>
                    <p class="mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <?php
                        $totalPermissions = \Spatie\Permission\Models\Permission::count();
                    ?>
                    <h4 class="text-warning"><?php echo e($totalPermissions); ?></h4>
                    <p class="mb-0">Permissions</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Roles List -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4 class="mb-0">System Roles</h4>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i data-feather="download" class="icon-xs me-2"></i>
                            Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#!" onclick="exportRoles('excel')">
                                <i data-feather="file-text" class="icon-xs me-2"></i>Export to Excel
                            </a></li>
                            <li><a class="dropdown-item" href="#!" onclick="exportRoles('pdf')">
                                <i data-feather="file" class="icon-xs me-2"></i>Export to PDF
                            </a></li>
                        </ul>
                    </div>
                    <a href="<?php echo e(route('roles.permissions')); ?>" class="btn btn-sm btn-outline-secondary">
                        <i data-feather="key" class="icon-xs me-2"></i>
                        Permissions
                    </a>
                    <a href="<?php echo e(route('roles.assignments')); ?>" class="btn btn-sm btn-outline-primary">
                        <i data-feather="users" class="icon-xs me-2"></i>
                        Assignments
                    </a>
                    <a href="<?php echo e(route('roles.create')); ?>" class="btn btn-sm btn-primary">
                        <i data-feather="plus" class="icon-xs me-2"></i>
                        Create Role
                    </a>
                </div>
            </div>

            <!-- Quick Filters -->
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary active" onclick="filterRoles('all')">
                    All Roles <span class="badge bg-primary ms-1">8</span>
                </button>
                <button class="btn btn-sm btn-outline-success" onclick="filterRoles('system')">
                    System Roles <span class="badge bg-success ms-1">6</span>
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="filterRoles('custom')">
                    Custom Roles <span class="badge bg-info ms-1">2</span>
                </button>
                <button class="btn btn-sm btn-outline-warning" onclick="filterRoles('inactive')">
                    Inactive <span class="badge bg-warning ms-1">0</span>
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllRoles">
                                </div>
                            </th>
                            <th>
                                <a href="#!" class="text-decoration-none text-dark d-flex align-items-center">
                                    Role Name
                                    <i data-feather="chevron-up" class="icon-xs ms-1"></i>
                                </a>
                            </th>
                            <th>Display Name</th>
                            <th>Users</th>
                            <th>Permissions</th>
                            <th>Type</th>
                            <th>Created</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input role-checkbox" type="checkbox" value="<?php echo e($role->id); ?>"
                                           <?php echo e($role->type === 'system' ? 'disabled' : ''); ?>>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php
                                        $avatarColors = [
                                            'super_admin' => 'bg-danger',
                                            'admin' => 'bg-warning',
                                            'inspector' => 'bg-primary',
                                            'officer' => 'bg-success',
                                            'forensic_analyst' => 'bg-info',
                                            'court_liaison' => 'bg-secondary',
                                            'viewer' => 'bg-light text-dark',
                                            'detective' => 'bg-dark'
                                        ];
                                        $avatarColor = $avatarColors[$role->name] ?? 'bg-primary';
                                    ?>
                                    <div>
                                        <strong><?php echo e(ucwords(str_replace('_', ' ', $role->name))); ?></strong>
                                        <br><small class="text-muted"><?php echo e($role->name); ?> role</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php
                                    $badgeColors = [
                                        'super_admin' => 'bg-danger',
                                        'admin' => 'bg-warning',
                                        'inspector' => 'bg-primary',
                                        'officer' => 'bg-success',
                                        'forensic_analyst' => 'bg-info',
                                        'court_liaison' => 'bg-secondary',
                                        'viewer' => 'bg-light text-dark',
                                        'detective' => 'bg-dark'
                                    ];
                                    $badgeColor = $badgeColors[$role->name] ?? 'bg-primary';
                                ?>
                                <span class="badge <?php echo e($badgeColor); ?>"><?php echo e(ucwords(str_replace('_', ' ', $role->name))); ?></span>
                            </td>
                            <td>
                                <a href="#!" class="text-decoration-none">
                                    <span class="badge bg-primary"><?php echo e($role->users_count); ?> users</span>
                                </a>
                            </td>
                            <td>
                                <?php if($role->name === 'super_admin'): ?>
                                    <span class="badge bg-success">All Permissions</span>
                                <?php else: ?>
                                    <span class="badge bg-info"><?php echo e($role->permissions_count); ?> permissions</span>
                                <?php endif; ?>
                                <br><small class="text-muted"><?php echo e($role->permissions_count); ?> permissions</small>
                            </td>
                            <td>
                                <?php
                                    $isSystemRole = in_array($role->name, ['Super Administrator', 'Administrator', 'Inspector', 'Officer', 'Forensic Analyst', 'Court Liaison', 'Viewer']);
                                ?>
                                <span class="badge <?php echo e($isSystemRole ? 'bg-secondary' : 'bg-primary'); ?>">
                                    <?php echo e($isSystemRole ? 'System' : 'Custom'); ?>

                                </span>
                            </td>
                            <td>
                                <?php echo e($role->created_at->format('d/m/Y')); ?>

                                <br><small class="text-muted"><?php echo e($isSystemRole ? 'System role' : 'Custom role'); ?></small>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="<?php echo e(route('roles.show', $role->id)); ?>">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="<?php echo e(route('roles.edit', $role->id)); ?>">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit Role
                                        </a></li>
                                        <?php if(!$isSystemRole): ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#!" onclick="deleteRole(<?php echo e($role->id); ?>)">
                                            <i data-feather="trash-2" class="icon-xs me-2"></i>Delete Role
                                        </a></li>
                                        <?php else: ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><span class="dropdown-item-text text-muted">Cannot delete system role</span></li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i data-feather="shield" class="icon-lg mb-2"></i>
                                    <p>No roles found.</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>


                    </tbody>
                </table>
            </div>
        </div>

                <!-- Pagination -->
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    Showing <strong><?php echo e($roles->firstItem()); ?></strong> to <strong><?php echo e($roles->lastItem()); ?></strong> of <strong><?php echo e($roles->total()); ?></strong> roles
                </div>
                <nav aria-label="User pagination">
                    <?php echo e($roles->links('pagination::bootstrap-4')); ?>

                </nav>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();

    // Select All functionality
    document.getElementById('selectAllRoles').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.role-checkbox:not([disabled])');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
});

function exportRoles(format) {
    alert(`Exporting roles to ${format.toUpperCase()} format...`);
}

function filterRoles(type) {
    // Remove active class from all buttons
    document.querySelectorAll('[onclick^="filterRoles"]').forEach(btn => {
        btn.classList.remove('active');
    });

    // Add active class to clicked button
    event.target.classList.add('active');

    // In a real application, this would filter the table
    alert(`Filtering roles by: ${type}`);
}

function bulkRoleAction(action) {
    const selectedRoles = document.querySelectorAll('.role-checkbox:checked:not([disabled])');
    if (selectedRoles.length === 0) {
        alert('Please select roles to perform bulk action.');
        return;
    }

    const actionText = action.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
    if (confirm(`Are you sure you want to ${actionText} ${selectedRoles.length} selected role(s)?`)) {
        alert(`${actionText} action performed on ${selectedRoles.length} role(s).`);
    }
}

function deleteRole(roleId) {
    if (confirm('Are you sure you want to delete this role? This action cannot be undone.')) {
        // In a real application, this would submit a DELETE request
        alert(`Role ${roleId} would be deleted.`);
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/roles/index.blade.php ENDPATH**/ ?>