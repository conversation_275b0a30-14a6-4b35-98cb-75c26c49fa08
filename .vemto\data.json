{"tables": {"projects": {"item_1": {"uuid": "0ba0fab7-3ba8-4fde-a394-52cbf7e1e3a4", "id": 1, "createdAt": "2025-07-09 20:22:38", "updatedAt": "2025-07-09 20:37:31", "settings": {"cssFramework": "bootstrap", "uiStarterKit": "jetstream", "usesLivewire": true, "usesInertia": false, "usesVue": false, "usesReact": false, "usesSvelte": false, "isFreshLaravelProject": true, "schemaReaderMode": "migration", "schemaReaderDbDriver": "ma<PERSON>b", "schemaReaderDbHost": "127.0.0.1", "schemaReaderDbPort": "3306", "schemaReaderDbUsername": "root", "schemaReaderDbPassword": null, "schemaReaderDbDatabase": "vemto_schema_reader", "laravelVersion": "11.0"}, "canShowSchemaSourceChangesAlert": false, "codeGenerationSettings": {"models": true, "factories": true, "seeders": true, "policies": true, "requests": true, "controllers": true, "routes": true, "views": true, "uiComponents": true, "livewireLayout": true, "translationsOnViews": true, "translationsFormat": "underscore"}, "languages": ["en"], "defaultLanguage": "en", "translations": {"en": {"navigation.home": "Home", "navigation.apps": "Apps", "crud.users.itemTitle": "User", "crud.users.collectionTitle": "Users", "crud.users.inputs.name.label": "Name", "crud.users.inputs.name.placeholder": "Name", "crud.users.inputs.email.label": "Email", "crud.users.inputs.email.placeholder": "Email", "crud.users.inputs.password.label": "Password", "crud.users.inputs.password.placeholder": "Password", "crud.users.inputs.created_by.label": "Created by", "crud.users.inputs.created_by.placeholder": "Created by", "crud.users.inputs.updated_by.label": "Updated by", "crud.users.inputs.updated_by.placeholder": "Updated by", "navigation.users": "Users", "crud.users.filament.name.helper_text": "", "crud.users.filament.name.label": "", "crud.users.filament.name.description": "", "crud.users.filament.email.helper_text": "", "crud.users.filament.email.label": "", "crud.users.filament.email.description": "", "crud.users.filament.password.helper_text": "", "crud.users.filament.password.label": "", "crud.users.filament.password.description": "", "crud.users.filament.created_by.helper_text": "", "crud.users.filament.created_by.label": "", "crud.users.filament.created_by.description": "", "crud.users.filament.updated_by.helper_text": "", "crud.users.filament.updated_by.label": "", "crud.users.filament.updated_by.description": ""}}, "vthemeKeys": {"default.delay": "opacity-75", "table": "min-w-full divide-y divide-gray-200", "table.container": "overflow-hidden border rounded-lg", "table.head": "bg-gray-50", "table.head.row": "text-gray-500", "table.head.col": "px-5 py-3 text-xs font-medium text-left uppercase", "table.body": "bg-white divide-y divide-gray-200 dark:divide-gray-700", "table.body.row": "text-gray-800", "table.body.col": "px-5 py-4 text-sm whitespace-nowrap", "ui.button": "inline-flex items-center justify-center px-4 py-2 text-sm font-medium tracking-wide text-white transition-colors duration-200 rounded-md bg-indigo-500 hover:bg-indigo-600 focus:ring-2 focus:ring-offset-2 focus:ring-gray-900 focus:shadow-outline focus:outline-none", "ui.input": "inline-flex h-10 px-3 py-2 text-sm bg-white border rounded-md border-gray-300 ring-offset-background placeholder:text-gray-500 focus:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:cursor-not-allowed disabled:opacity-50", "ui.input.checkbox": "", "ui.label": "block mb-1.5 font-medium text-sm text-gray-700", "ui.input.error": "text-sm text-red-600", "ui.breadcrumbs.container": "flex justify-between py-1", "ui.breadcrumbs.list": "inline-flex items-center mb-3 space-x-1 text-xs text-neutral-500 [&_.active-breadcrumb]:text-neutral-600 [&_.active-breadcrumb]:font-medium sm:mb-0", "ui.breadcrumbs.list.item": "", "ui.breadcrumbs.list.item.link": "inline-flex items-center py-1 font-normal hover:text-neutral-900 focus:outline-none", "ui.breadcrumbs.separator": "w-5 h-5 text-gray-400/70", "crud.index.container": "max-w-7xl mx-auto py-10 sm:px-6 lg:px-8 space-y-4", "crud.table.head.col.extra": "cursor-pointer", "crud.table.head.action-col": "px-5 py-3 text-xs font-medium text-right uppercase", "crud.table.body.col.extra": "max-w-xs truncate", "crud.table.body.action-col": "px-5 py-4 text-sm font-medium text-right whitespace-nowrap space-x-2", "crud.action-link": "text-indigo-600 hover:text-indigo-700 cursor-pointer", "crud.action-link-danger": "text-red-600 hover:text-red-700 cursor-pointer", "crud.index.tools": "flex justify-between align-top py-4", "crud.pagination.container": "mt-2", "crud.container": "max-w-7xl mx-auto py-10 sm:px-6 lg:px-8 space-y-4", "crud.header": "w-full text-gray-500 text-lg font-semibold py-4 uppercase", "crud.form": "w-full mb-0", "crud.form.container": "overflow-hidden border rounded-lg bg-white", "crud.form.inputs.container": "p-6 space-y-3", "crud.form.input.group": "w-full", "crud.form.input.radio.group": "flex flex-wrap flex-col gap-2", "crud.form.input.radio.box": "inline-flex justify-start gap-2", "ui.input.radio": "!w-4 !h-4 text-indigo-600 transition duration-150 ease-in-out border-gray-300 rounded-md focus:ring-indigo-500", "crud.form.input.extra": "w-full", "crud.form.input.image": "object-cover rounded border border-gray-200 w-36 h-36 cursor-pointer bg-gray-50 hover:opacity-75 transition duration-100 ease-in-out", "crud.form.input.image.button": "p-0.5 px-1 text-xs border border-gray-200 rounded hover:bg-gray-200", "crud.form.input.file.name": "text-sm text-gray-500 p-1 rounded-sm bg-gray-50 border border-gray-200 mb-1", "crud.form.buttons-container": "flex justify-between mt-4 border-t border-gray-50 p-4"}, "vthemeCdn": "https://cdn.tailwindcss.com", "currentRenderedFilesPaths": ["/lang/en//navigation.php", "/lang/en//crud.php", "resources/views/components/ui//toast.blade.php", "resources/views/components/ui//action/danger.blade.php", "resources/views/components/ui//action/index.blade.php", "resources/views/components/ui//breadcrumbs/index.blade.php", "resources/views/components/ui//breadcrumbs/link.blade.php", "resources/views/components/ui//breadcrumbs/separator.blade.php", "resources/views/components/ui//button/danger.blade.php", "resources/views/components/ui//button/index.blade.php", "resources/views/components/ui//card/body.blade.php", "resources/views/components/ui//card/header.blade.php", "resources/views/components/ui//card/index.blade.php", "resources/views/components/ui//card/title.blade.php", "resources/views/components/ui//container/table.blade.php", "resources/views/components/ui//input/checkbox.blade.php", "resources/views/components/ui//input/color.blade.php", "resources/views/components/ui//input/date.blade.php", "resources/views/components/ui//input/date-time.blade.php", "resources/views/components/ui//input/email.blade.php", "resources/views/components/ui//input/error.blade.php", "resources/views/components/ui//input/file.blade.php", "resources/views/components/ui//input/hidden.blade.php", "resources/views/components/ui//input/image.blade.php", "resources/views/components/ui//input/index.blade.php", "resources/views/components/ui//input/number.blade.php", "resources/views/components/ui//input/password.blade.php", "resources/views/components/ui//input/radio.blade.php", "resources/views/components/ui//input/select.blade.php", "resources/views/components/ui//input/text.blade.php", "resources/views/components/ui//input/textarea.blade.php", "resources/views/components/ui//input/time.blade.php", "resources/views/components/ui//input/url.blade.php", "resources/views/components/ui//label/index.blade.php", "resources/views/components/ui//modal/confirm.blade.php", "resources/views/components/ui//modal/index.blade.php", "resources/views/components/ui//table/action-column.blade.php", "resources/views/components/ui//table/action-header.blade.php", "resources/views/components/ui//table/column.blade.php", "resources/views/components/ui//table/header.blade.php", "resources/views/components/ui//table/image.blade.php", "resources/views/components/ui//table/index.blade.php", "resources/views/components/ui//table/row.blade.php", "resources/views/partials/app-menu.blade.php", "resources/views/partials/app-responsive-menu.blade.php", "routes/app.php", "routes/app-api.php", "bootstrap/app.php", "database/factories/MembershipFactory.php", "database/seeders/MembershipSeeder.php", "app/Policies/MembershipPolicy.php", "database/factories/TeamFactory.php", "database/seeders/TeamSeeder.php", "app/Policies/TeamPolicy.php", "database/factories/TeamInvitationFactory.php", "database/seeders/TeamInvitationSeeder.php", "app/Policies/TeamInvitationPolicy.php", "database/factories/UserFactory.php", "database/seeders/UserSeeder.php", "app/Policies/UserPolicy.php", "resources/views/livewire/dashboard/users/index.blade.php", "app/Livewire/Dashboard/UserIndex.php", "resources/views/livewire/dashboard/users/create.blade.php", "app/Livewire/Dashboard/UserCreate.php", "resources/views/livewire/dashboard/users/edit.blade.php", "app/Livewire/Dashboard/UserEdit.php", "app/Livewire/Dashboard/Users/<USER>/CreateForm.php", "app/Livewire/Dashboard/Users/<USER>/UpdateForm.php", "app/Http/Controllers/Api/AuthController.php", "app/Http/Requests/UserStoreRequest.php", "app/Http/Requests/UserUpdateRequest.php", "app/Http/Resources/UserCollection.php", "app/Http/Resources/UserResource.php", "app/Http/Controllers/Api/UserController.php", "tests/Feature/Api/UserTest.php", "app/Filament/Resources/Panel/UserResource.php", "app/Filament/Resources/Panel/UserResource/Pages/CreateUser.php", "app/Filament/Resources/Panel/UserResource/Pages/ViewUser.php", "app/Filament/Resources/Panel/UserResource/Pages/EditUser.php", "app/Filament/Resources/Panel/UserResource/Pages/ListUsers.php", "database/seeders/DatabaseSeeder.php", "resources/views/components/layouts/app.blade.php"], "connectionFinished": true, "canIgnoreNextSchemaSourceChanges": false, "filesQueueStatus": "idle", "lastReadSchemaDataHash": "814cec06a96ac4ad8f9e830ca9865592", "currentZoom": 70}, "__tableData": {"count": 1, "lastPrimaryKey": 1, "index": {"1": {"hasMany": {"tables.projectId": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], "models.projectId": [1, 2, 3, 4], "relationships.projectId": [1], "app_sections.projectId": [1, 2, 3, 4], "schema_sections.projectId": [1, 2, 3], "navs.projectId": [1, 2, 3], "renderable_files.projectId": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], "cruds.projectId": [1, 2, 3], "routes.projectId": [1, 2, 3, 5, 6, 7, 8, 9], "pages.projectId": []}}}, "additionalIndexes": {}, "items": [], "relations": []}}, "tables": {"item_1": {"projectId": 1, "name": "users", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000000_create_users_table.php", "relativePath": "/database/migrations/0001_01_01_000000_create_users_table.php", "migrationName": "0001_01_01_000000_create_users_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000000", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "users", "creatorMethod": "id"}, {"type": "string", "name": "name", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "email", "length": 255, "table": "users", "creatorMethod": "string", "unique": true}, {"type": "timestamp", "name": "email_verified_at", "precision": 0, "table": "users", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "password", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "remember_token", "length": 100, "table": "users", "creatorMethod": "rememberToken", "nullable": true}, {"type": "bigInteger", "name": "current_team_id", "autoIncrement": false, "unsigned": true, "table": "users", "creatorMethod": "foreignId", "nullable": true}, {"type": "string", "name": "profile_photo_path", "length": 2048, "table": "users", "creatorMethod": "string", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "string", "name": "email", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "token", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string"}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "password_reset_tokens", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "id", "length": 255, "table": "sessions", "creatorMethod": "string", "primary": true}, {"type": "bigInteger", "name": "user_id", "autoIncrement": false, "unsigned": true, "table": "sessions", "creatorMethod": "foreignId", "nullable": true, "index": true}, {"type": "string", "name": "ip_address", "length": 45, "table": "sessions", "creatorMethod": "string", "nullable": true}, {"type": "text", "name": "user_agent", "table": "sessions", "creatorMethod": "text", "nullable": true}, {"type": "longText", "name": "payload", "table": "sessions", "creatorMethod": "longText"}, {"type": "integer", "name": "last_activity", "autoIncrement": false, "unsigned": false, "table": "sessions", "creatorMethod": "integer", "index": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "users"}, {"name": "create", "table": "password_reset_tokens"}, {"name": "create", "table": "sessions"}], "createdTables": ["users", "password_reset_tokens", "sessions"], "renamedTables": [], "droppedTables": []}, {"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_181041_add_two_factor_columns_to_users_table.php", "relativePath": "/database/migrations/2025_07_09_181041_add_two_factor_columns_to_users_table.php", "migrationName": "2025_07_09_181041_add_two_factor_columns_to_users_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_181041", "addedColumns": [{"type": "text", "name": "two_factor_secret", "table": "users", "creatorMethod": "text", "after": "password", "nullable": true}, {"type": "text", "name": "two_factor_recovery_codes", "table": "users", "creatorMethod": "text", "after": "two_factor_secret", "nullable": true}, {"type": "timestamp", "name": "two_factor_confirmed_at", "precision": 0, "table": "users", "creatorMethod": "timestamp", "after": "two_factor_recovery_codes", "nullable": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "users"}], "createdTables": [], "renamedTables": [], "droppedTables": []}, {"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_183255_001_update_users_table.php", "relativePath": "/database/migrations/2025_07_09_183255_001_update_users_table.php", "migrationName": "2025_07_09_183255_001_update_users_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_183255", "addedColumns": [{"type": "bigInteger", "name": "created_by", "autoIncrement": false, "unsigned": false, "table": "users", "creatorMethod": "bigInteger", "nullable": true, "after": "profile_photo_path"}, {"type": "bigInteger", "name": "updated_by", "autoIncrement": false, "unsigned": false, "table": "users", "creatorMethod": "bigInteger", "nullable": true, "after": "created_by"}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "users"}], "createdTables": [], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "users", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000000_create_users_table.php", "relativePath": "/database/migrations/0001_01_01_000000_create_users_table.php", "migrationName": "0001_01_01_000000_create_users_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000000", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "users", "creatorMethod": "id"}, {"type": "string", "name": "name", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "email", "length": 255, "table": "users", "creatorMethod": "string", "unique": true}, {"type": "timestamp", "name": "email_verified_at", "precision": 0, "table": "users", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "password", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "remember_token", "length": 100, "table": "users", "creatorMethod": "rememberToken", "nullable": true}, {"type": "bigInteger", "name": "current_team_id", "autoIncrement": false, "unsigned": true, "table": "users", "creatorMethod": "foreignId", "nullable": true}, {"type": "string", "name": "profile_photo_path", "length": 2048, "table": "users", "creatorMethod": "string", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "string", "name": "email", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "token", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string"}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "password_reset_tokens", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "id", "length": 255, "table": "sessions", "creatorMethod": "string", "primary": true}, {"type": "bigInteger", "name": "user_id", "autoIncrement": false, "unsigned": true, "table": "sessions", "creatorMethod": "foreignId", "nullable": true, "index": true}, {"type": "string", "name": "ip_address", "length": 45, "table": "sessions", "creatorMethod": "string", "nullable": true}, {"type": "text", "name": "user_agent", "table": "sessions", "creatorMethod": "text", "nullable": true}, {"type": "longText", "name": "payload", "table": "sessions", "creatorMethod": "longText"}, {"type": "integer", "name": "last_activity", "autoIncrement": false, "unsigned": false, "table": "sessions", "creatorMethod": "integer", "index": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "users"}, {"name": "create", "table": "password_reset_tokens"}, {"name": "create", "table": "sessions"}], "createdTables": ["users", "password_reset_tokens", "sessions"], "renamedTables": [], "droppedTables": []}, {"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_181041_add_two_factor_columns_to_users_table.php", "relativePath": "/database/migrations/2025_07_09_181041_add_two_factor_columns_to_users_table.php", "migrationName": "2025_07_09_181041_add_two_factor_columns_to_users_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_181041", "addedColumns": [{"type": "text", "name": "two_factor_secret", "table": "users", "creatorMethod": "text", "after": "password", "nullable": true}, {"type": "text", "name": "two_factor_recovery_codes", "table": "users", "creatorMethod": "text", "after": "two_factor_secret", "nullable": true}, {"type": "timestamp", "name": "two_factor_confirmed_at", "precision": 0, "table": "users", "creatorMethod": "timestamp", "after": "two_factor_recovery_codes", "nullable": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "users"}], "createdTables": [], "renamedTables": [], "droppedTables": []}, {"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_183255_001_update_users_table.php", "relativePath": "/database/migrations/2025_07_09_183255_001_update_users_table.php", "migrationName": "2025_07_09_183255_001_update_users_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_183255", "addedColumns": [{"type": "bigInteger", "name": "created_by", "autoIncrement": false, "unsigned": false, "table": "users", "creatorMethod": "bigInteger", "nullable": true, "after": "profile_photo_path"}, {"type": "bigInteger", "name": "updated_by", "autoIncrement": false, "unsigned": false, "table": "users", "creatorMethod": "bigInteger", "nullable": true, "after": "created_by"}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "users"}], "createdTables": [], "renamedTables": [], "droppedTables": []}]}, "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": "-717.429", "positionY": "-307", "sectionId": 1, "removed": false}, "__tableData": {"count": 12, "lastPrimaryKey": 12, "index": {"1": {"hasMany": {"columns.tableId": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 81, 82], "models.tableId": [4], "cruds.tableId": [1, 2, 3]}}, "2": {"hasMany": {"columns.tableId": [14, 15, 16]}}, "3": {"hasMany": {"columns.tableId": [17, 18, 19, 20, 21, 22]}}, "4": {"hasMany": {"columns.tableId": [23, 24, 25]}}, "5": {"hasMany": {"columns.tableId": [26, 27, 28]}}, "6": {"hasMany": {"columns.tableId": [29, 30, 31, 32, 33, 34, 35]}}, "7": {"hasMany": {"columns.tableId": [36, 37, 38, 39, 40, 41, 42, 43, 44, 45]}}, "8": {"hasMany": {"columns.tableId": [46, 47, 48, 49, 50, 51, 52]}}, "9": {"hasMany": {"columns.tableId": [53, 54, 55, 56, 57, 58, 59, 60, 61, 62], "indices.tableId": [1]}}, "10": {"hasMany": {"columns.tableId": [63, 64, 65, 66, 67, 68], "models.tableId": [2]}}, "11": {"hasMany": {"columns.tableId": [69, 70, 71, 72, 73, 74], "indices.tableId": [2], "models.tableId": [1]}}, "12": {"hasMany": {"columns.tableId": [75, 76, 77, 78, 79, 80], "indices.tableId": [3, 4], "indices.onTableId": [3], "models.tableId": [3]}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"projectId": 1, "name": "password_reset_tokens", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000000_create_users_table.php", "relativePath": "/database/migrations/0001_01_01_000000_create_users_table.php", "migrationName": "0001_01_01_000000_create_users_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000000", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "users", "creatorMethod": "id"}, {"type": "string", "name": "name", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "email", "length": 255, "table": "users", "creatorMethod": "string", "unique": true}, {"type": "timestamp", "name": "email_verified_at", "precision": 0, "table": "users", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "password", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "remember_token", "length": 100, "table": "users", "creatorMethod": "rememberToken", "nullable": true}, {"type": "bigInteger", "name": "current_team_id", "autoIncrement": false, "unsigned": true, "table": "users", "creatorMethod": "foreignId", "nullable": true}, {"type": "string", "name": "profile_photo_path", "length": 2048, "table": "users", "creatorMethod": "string", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "string", "name": "email", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "token", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string"}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "password_reset_tokens", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "id", "length": 255, "table": "sessions", "creatorMethod": "string", "primary": true}, {"type": "bigInteger", "name": "user_id", "autoIncrement": false, "unsigned": true, "table": "sessions", "creatorMethod": "foreignId", "nullable": true, "index": true}, {"type": "string", "name": "ip_address", "length": 45, "table": "sessions", "creatorMethod": "string", "nullable": true}, {"type": "text", "name": "user_agent", "table": "sessions", "creatorMethod": "text", "nullable": true}, {"type": "longText", "name": "payload", "table": "sessions", "creatorMethod": "longText"}, {"type": "integer", "name": "last_activity", "autoIncrement": false, "unsigned": false, "table": "sessions", "creatorMethod": "integer", "index": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "users"}, {"name": "create", "table": "password_reset_tokens"}, {"name": "create", "table": "sessions"}], "createdTables": ["users", "password_reset_tokens", "sessions"], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "password_reset_tokens", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000000_create_users_table.php", "relativePath": "/database/migrations/0001_01_01_000000_create_users_table.php", "migrationName": "0001_01_01_000000_create_users_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000000", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "users", "creatorMethod": "id"}, {"type": "string", "name": "name", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "email", "length": 255, "table": "users", "creatorMethod": "string", "unique": true}, {"type": "timestamp", "name": "email_verified_at", "precision": 0, "table": "users", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "password", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "remember_token", "length": 100, "table": "users", "creatorMethod": "rememberToken", "nullable": true}, {"type": "bigInteger", "name": "current_team_id", "autoIncrement": false, "unsigned": true, "table": "users", "creatorMethod": "foreignId", "nullable": true}, {"type": "string", "name": "profile_photo_path", "length": 2048, "table": "users", "creatorMethod": "string", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "string", "name": "email", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "token", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string"}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "password_reset_tokens", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "id", "length": 255, "table": "sessions", "creatorMethod": "string", "primary": true}, {"type": "bigInteger", "name": "user_id", "autoIncrement": false, "unsigned": true, "table": "sessions", "creatorMethod": "foreignId", "nullable": true, "index": true}, {"type": "string", "name": "ip_address", "length": 45, "table": "sessions", "creatorMethod": "string", "nullable": true}, {"type": "text", "name": "user_agent", "table": "sessions", "creatorMethod": "text", "nullable": true}, {"type": "longText", "name": "payload", "table": "sessions", "creatorMethod": "longText"}, {"type": "integer", "name": "last_activity", "autoIncrement": false, "unsigned": false, "table": "sessions", "creatorMethod": "integer", "index": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "users"}, {"name": "create", "table": "password_reset_tokens"}, {"name": "create", "table": "sessions"}], "createdTables": ["users", "password_reset_tokens", "sessions"], "renamedTables": [], "droppedTables": []}]}, "id": 2, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": 0, "positionY": 0, "sectionId": 2, "removed": false}, "item_3": {"projectId": 1, "name": "sessions", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000000_create_users_table.php", "relativePath": "/database/migrations/0001_01_01_000000_create_users_table.php", "migrationName": "0001_01_01_000000_create_users_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000000", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "users", "creatorMethod": "id"}, {"type": "string", "name": "name", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "email", "length": 255, "table": "users", "creatorMethod": "string", "unique": true}, {"type": "timestamp", "name": "email_verified_at", "precision": 0, "table": "users", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "password", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "remember_token", "length": 100, "table": "users", "creatorMethod": "rememberToken", "nullable": true}, {"type": "bigInteger", "name": "current_team_id", "autoIncrement": false, "unsigned": true, "table": "users", "creatorMethod": "foreignId", "nullable": true}, {"type": "string", "name": "profile_photo_path", "length": 2048, "table": "users", "creatorMethod": "string", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "string", "name": "email", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "token", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string"}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "password_reset_tokens", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "id", "length": 255, "table": "sessions", "creatorMethod": "string", "primary": true}, {"type": "bigInteger", "name": "user_id", "autoIncrement": false, "unsigned": true, "table": "sessions", "creatorMethod": "foreignId", "nullable": true, "index": true}, {"type": "string", "name": "ip_address", "length": 45, "table": "sessions", "creatorMethod": "string", "nullable": true}, {"type": "text", "name": "user_agent", "table": "sessions", "creatorMethod": "text", "nullable": true}, {"type": "longText", "name": "payload", "table": "sessions", "creatorMethod": "longText"}, {"type": "integer", "name": "last_activity", "autoIncrement": false, "unsigned": false, "table": "sessions", "creatorMethod": "integer", "index": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "users"}, {"name": "create", "table": "password_reset_tokens"}, {"name": "create", "table": "sessions"}], "createdTables": ["users", "password_reset_tokens", "sessions"], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "sessions", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000000_create_users_table.php", "relativePath": "/database/migrations/0001_01_01_000000_create_users_table.php", "migrationName": "0001_01_01_000000_create_users_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000000", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "users", "creatorMethod": "id"}, {"type": "string", "name": "name", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "email", "length": 255, "table": "users", "creatorMethod": "string", "unique": true}, {"type": "timestamp", "name": "email_verified_at", "precision": 0, "table": "users", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "password", "length": 255, "table": "users", "creatorMethod": "string"}, {"type": "string", "name": "remember_token", "length": 100, "table": "users", "creatorMethod": "rememberToken", "nullable": true}, {"type": "bigInteger", "name": "current_team_id", "autoIncrement": false, "unsigned": true, "table": "users", "creatorMethod": "foreignId", "nullable": true}, {"type": "string", "name": "profile_photo_path", "length": 2048, "table": "users", "creatorMethod": "string", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "users", "creatorMethod": "timestamps", "nullable": true}, {"type": "string", "name": "email", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "token", "length": 255, "table": "password_reset_tokens", "creatorMethod": "string"}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "password_reset_tokens", "creatorMethod": "timestamp", "nullable": true}, {"type": "string", "name": "id", "length": 255, "table": "sessions", "creatorMethod": "string", "primary": true}, {"type": "bigInteger", "name": "user_id", "autoIncrement": false, "unsigned": true, "table": "sessions", "creatorMethod": "foreignId", "nullable": true, "index": true}, {"type": "string", "name": "ip_address", "length": 45, "table": "sessions", "creatorMethod": "string", "nullable": true}, {"type": "text", "name": "user_agent", "table": "sessions", "creatorMethod": "text", "nullable": true}, {"type": "longText", "name": "payload", "table": "sessions", "creatorMethod": "longText"}, {"type": "integer", "name": "last_activity", "autoIncrement": false, "unsigned": false, "table": "sessions", "creatorMethod": "integer", "index": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "users"}, {"name": "create", "table": "password_reset_tokens"}, {"name": "create", "table": "sessions"}], "createdTables": ["users", "password_reset_tokens", "sessions"], "renamedTables": [], "droppedTables": []}]}, "id": 3, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": 0, "positionY": 0, "sectionId": 2, "removed": false}, "item_4": {"projectId": 1, "name": "cache", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000001_create_cache_table.php", "relativePath": "/database/migrations/0001_01_01_000001_create_cache_table.php", "migrationName": "0001_01_01_000001_create_cache_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000001", "addedColumns": [{"type": "string", "name": "key", "length": 255, "table": "cache", "creatorMethod": "string", "primary": true}, {"type": "mediumText", "name": "value", "table": "cache", "creatorMethod": "mediumText"}, {"type": "integer", "name": "expiration", "autoIncrement": false, "unsigned": false, "table": "cache", "creatorMethod": "integer"}, {"type": "string", "name": "key", "length": 255, "table": "cache_locks", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "owner", "length": 255, "table": "cache_locks", "creatorMethod": "string"}, {"type": "integer", "name": "expiration", "autoIncrement": false, "unsigned": false, "table": "cache_locks", "creatorMethod": "integer"}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "cache"}, {"name": "create", "table": "cache_locks"}], "createdTables": ["cache", "cache_locks"], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "cache", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000001_create_cache_table.php", "relativePath": "/database/migrations/0001_01_01_000001_create_cache_table.php", "migrationName": "0001_01_01_000001_create_cache_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000001", "addedColumns": [{"type": "string", "name": "key", "length": 255, "table": "cache", "creatorMethod": "string", "primary": true}, {"type": "mediumText", "name": "value", "table": "cache", "creatorMethod": "mediumText"}, {"type": "integer", "name": "expiration", "autoIncrement": false, "unsigned": false, "table": "cache", "creatorMethod": "integer"}, {"type": "string", "name": "key", "length": 255, "table": "cache_locks", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "owner", "length": 255, "table": "cache_locks", "creatorMethod": "string"}, {"type": "integer", "name": "expiration", "autoIncrement": false, "unsigned": false, "table": "cache_locks", "creatorMethod": "integer"}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "cache"}, {"name": "create", "table": "cache_locks"}], "createdTables": ["cache", "cache_locks"], "renamedTables": [], "droppedTables": []}]}, "id": 4, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": 0, "positionY": 0, "sectionId": 2, "removed": false}, "item_5": {"projectId": 1, "name": "cache_locks", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000001_create_cache_table.php", "relativePath": "/database/migrations/0001_01_01_000001_create_cache_table.php", "migrationName": "0001_01_01_000001_create_cache_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000001", "addedColumns": [{"type": "string", "name": "key", "length": 255, "table": "cache", "creatorMethod": "string", "primary": true}, {"type": "mediumText", "name": "value", "table": "cache", "creatorMethod": "mediumText"}, {"type": "integer", "name": "expiration", "autoIncrement": false, "unsigned": false, "table": "cache", "creatorMethod": "integer"}, {"type": "string", "name": "key", "length": 255, "table": "cache_locks", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "owner", "length": 255, "table": "cache_locks", "creatorMethod": "string"}, {"type": "integer", "name": "expiration", "autoIncrement": false, "unsigned": false, "table": "cache_locks", "creatorMethod": "integer"}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "cache"}, {"name": "create", "table": "cache_locks"}], "createdTables": ["cache", "cache_locks"], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "cache_locks", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000001_create_cache_table.php", "relativePath": "/database/migrations/0001_01_01_000001_create_cache_table.php", "migrationName": "0001_01_01_000001_create_cache_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000001", "addedColumns": [{"type": "string", "name": "key", "length": 255, "table": "cache", "creatorMethod": "string", "primary": true}, {"type": "mediumText", "name": "value", "table": "cache", "creatorMethod": "mediumText"}, {"type": "integer", "name": "expiration", "autoIncrement": false, "unsigned": false, "table": "cache", "creatorMethod": "integer"}, {"type": "string", "name": "key", "length": 255, "table": "cache_locks", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "owner", "length": 255, "table": "cache_locks", "creatorMethod": "string"}, {"type": "integer", "name": "expiration", "autoIncrement": false, "unsigned": false, "table": "cache_locks", "creatorMethod": "integer"}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "cache"}, {"name": "create", "table": "cache_locks"}], "createdTables": ["cache", "cache_locks"], "renamedTables": [], "droppedTables": []}]}, "id": 5, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": 0, "positionY": 0, "sectionId": 2, "removed": false}, "item_6": {"projectId": 1, "name": "jobs", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000002_create_jobs_table.php", "relativePath": "/database/migrations/0001_01_01_000002_create_jobs_table.php", "migrationName": "0001_01_01_000002_create_jobs_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000002", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "jobs", "creatorMethod": "id"}, {"type": "string", "name": "queue", "length": 255, "table": "jobs", "creatorMethod": "string", "index": true}, {"type": "longText", "name": "payload", "table": "jobs", "creatorMethod": "longText"}, {"type": "tinyInteger", "name": "attempts", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedTinyInteger"}, {"type": "integer", "name": "reserved_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger", "nullable": true}, {"type": "integer", "name": "available_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "string", "name": "id", "length": 255, "table": "job_batches", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "name", "length": 255, "table": "job_batches", "creatorMethod": "string"}, {"type": "integer", "name": "total_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "pending_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "failed_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "longText", "name": "failed_job_ids", "table": "job_batches", "creatorMethod": "longText"}, {"type": "mediumText", "name": "options", "table": "job_batches", "creatorMethod": "mediumText", "nullable": true}, {"type": "integer", "name": "cancelled_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "finished_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "failed_jobs", "creatorMethod": "id"}, {"type": "string", "name": "uuid", "length": 255, "table": "failed_jobs", "creatorMethod": "string", "unique": true}, {"type": "text", "name": "connection", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "text", "name": "queue", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "longText", "name": "payload", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "longText", "name": "exception", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "timestamp", "name": "failed_at", "precision": 0, "table": "failed_jobs", "creatorMethod": "timestamp", "useCurrent": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "jobs"}, {"name": "create", "table": "job_batches"}, {"name": "create", "table": "failed_jobs"}], "createdTables": ["jobs", "job_batches", "failed_jobs"], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "jobs", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000002_create_jobs_table.php", "relativePath": "/database/migrations/0001_01_01_000002_create_jobs_table.php", "migrationName": "0001_01_01_000002_create_jobs_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000002", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "jobs", "creatorMethod": "id"}, {"type": "string", "name": "queue", "length": 255, "table": "jobs", "creatorMethod": "string", "index": true}, {"type": "longText", "name": "payload", "table": "jobs", "creatorMethod": "longText"}, {"type": "tinyInteger", "name": "attempts", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedTinyInteger"}, {"type": "integer", "name": "reserved_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger", "nullable": true}, {"type": "integer", "name": "available_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "string", "name": "id", "length": 255, "table": "job_batches", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "name", "length": 255, "table": "job_batches", "creatorMethod": "string"}, {"type": "integer", "name": "total_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "pending_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "failed_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "longText", "name": "failed_job_ids", "table": "job_batches", "creatorMethod": "longText"}, {"type": "mediumText", "name": "options", "table": "job_batches", "creatorMethod": "mediumText", "nullable": true}, {"type": "integer", "name": "cancelled_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "finished_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "failed_jobs", "creatorMethod": "id"}, {"type": "string", "name": "uuid", "length": 255, "table": "failed_jobs", "creatorMethod": "string", "unique": true}, {"type": "text", "name": "connection", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "text", "name": "queue", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "longText", "name": "payload", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "longText", "name": "exception", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "timestamp", "name": "failed_at", "precision": 0, "table": "failed_jobs", "creatorMethod": "timestamp", "useCurrent": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "jobs"}, {"name": "create", "table": "job_batches"}, {"name": "create", "table": "failed_jobs"}], "createdTables": ["jobs", "job_batches", "failed_jobs"], "renamedTables": [], "droppedTables": []}]}, "id": 6, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": 0, "positionY": 0, "sectionId": 2, "removed": false}, "item_7": {"projectId": 1, "name": "job_batches", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000002_create_jobs_table.php", "relativePath": "/database/migrations/0001_01_01_000002_create_jobs_table.php", "migrationName": "0001_01_01_000002_create_jobs_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000002", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "jobs", "creatorMethod": "id"}, {"type": "string", "name": "queue", "length": 255, "table": "jobs", "creatorMethod": "string", "index": true}, {"type": "longText", "name": "payload", "table": "jobs", "creatorMethod": "longText"}, {"type": "tinyInteger", "name": "attempts", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedTinyInteger"}, {"type": "integer", "name": "reserved_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger", "nullable": true}, {"type": "integer", "name": "available_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "string", "name": "id", "length": 255, "table": "job_batches", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "name", "length": 255, "table": "job_batches", "creatorMethod": "string"}, {"type": "integer", "name": "total_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "pending_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "failed_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "longText", "name": "failed_job_ids", "table": "job_batches", "creatorMethod": "longText"}, {"type": "mediumText", "name": "options", "table": "job_batches", "creatorMethod": "mediumText", "nullable": true}, {"type": "integer", "name": "cancelled_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "finished_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "failed_jobs", "creatorMethod": "id"}, {"type": "string", "name": "uuid", "length": 255, "table": "failed_jobs", "creatorMethod": "string", "unique": true}, {"type": "text", "name": "connection", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "text", "name": "queue", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "longText", "name": "payload", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "longText", "name": "exception", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "timestamp", "name": "failed_at", "precision": 0, "table": "failed_jobs", "creatorMethod": "timestamp", "useCurrent": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "jobs"}, {"name": "create", "table": "job_batches"}, {"name": "create", "table": "failed_jobs"}], "createdTables": ["jobs", "job_batches", "failed_jobs"], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "job_batches", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000002_create_jobs_table.php", "relativePath": "/database/migrations/0001_01_01_000002_create_jobs_table.php", "migrationName": "0001_01_01_000002_create_jobs_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000002", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "jobs", "creatorMethod": "id"}, {"type": "string", "name": "queue", "length": 255, "table": "jobs", "creatorMethod": "string", "index": true}, {"type": "longText", "name": "payload", "table": "jobs", "creatorMethod": "longText"}, {"type": "tinyInteger", "name": "attempts", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedTinyInteger"}, {"type": "integer", "name": "reserved_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger", "nullable": true}, {"type": "integer", "name": "available_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "string", "name": "id", "length": 255, "table": "job_batches", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "name", "length": 255, "table": "job_batches", "creatorMethod": "string"}, {"type": "integer", "name": "total_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "pending_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "failed_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "longText", "name": "failed_job_ids", "table": "job_batches", "creatorMethod": "longText"}, {"type": "mediumText", "name": "options", "table": "job_batches", "creatorMethod": "mediumText", "nullable": true}, {"type": "integer", "name": "cancelled_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "finished_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "failed_jobs", "creatorMethod": "id"}, {"type": "string", "name": "uuid", "length": 255, "table": "failed_jobs", "creatorMethod": "string", "unique": true}, {"type": "text", "name": "connection", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "text", "name": "queue", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "longText", "name": "payload", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "longText", "name": "exception", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "timestamp", "name": "failed_at", "precision": 0, "table": "failed_jobs", "creatorMethod": "timestamp", "useCurrent": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "jobs"}, {"name": "create", "table": "job_batches"}, {"name": "create", "table": "failed_jobs"}], "createdTables": ["jobs", "job_batches", "failed_jobs"], "renamedTables": [], "droppedTables": []}]}, "id": 7, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": 0, "positionY": 0, "sectionId": 2, "removed": false}, "item_8": {"projectId": 1, "name": "failed_jobs", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000002_create_jobs_table.php", "relativePath": "/database/migrations/0001_01_01_000002_create_jobs_table.php", "migrationName": "0001_01_01_000002_create_jobs_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000002", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "jobs", "creatorMethod": "id"}, {"type": "string", "name": "queue", "length": 255, "table": "jobs", "creatorMethod": "string", "index": true}, {"type": "longText", "name": "payload", "table": "jobs", "creatorMethod": "longText"}, {"type": "tinyInteger", "name": "attempts", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedTinyInteger"}, {"type": "integer", "name": "reserved_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger", "nullable": true}, {"type": "integer", "name": "available_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "string", "name": "id", "length": 255, "table": "job_batches", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "name", "length": 255, "table": "job_batches", "creatorMethod": "string"}, {"type": "integer", "name": "total_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "pending_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "failed_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "longText", "name": "failed_job_ids", "table": "job_batches", "creatorMethod": "longText"}, {"type": "mediumText", "name": "options", "table": "job_batches", "creatorMethod": "mediumText", "nullable": true}, {"type": "integer", "name": "cancelled_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "finished_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "failed_jobs", "creatorMethod": "id"}, {"type": "string", "name": "uuid", "length": 255, "table": "failed_jobs", "creatorMethod": "string", "unique": true}, {"type": "text", "name": "connection", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "text", "name": "queue", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "longText", "name": "payload", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "longText", "name": "exception", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "timestamp", "name": "failed_at", "precision": 0, "table": "failed_jobs", "creatorMethod": "timestamp", "useCurrent": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "jobs"}, {"name": "create", "table": "job_batches"}, {"name": "create", "table": "failed_jobs"}], "createdTables": ["jobs", "job_batches", "failed_jobs"], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "failed_jobs", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/0001_01_01_000002_create_jobs_table.php", "relativePath": "/database/migrations/0001_01_01_000002_create_jobs_table.php", "migrationName": "0001_01_01_000002_create_jobs_table", "datePrefix": "0001_01_01", "fullPrefix": "0001_01_01_000002", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "jobs", "creatorMethod": "id"}, {"type": "string", "name": "queue", "length": 255, "table": "jobs", "creatorMethod": "string", "index": true}, {"type": "longText", "name": "payload", "table": "jobs", "creatorMethod": "longText"}, {"type": "tinyInteger", "name": "attempts", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedTinyInteger"}, {"type": "integer", "name": "reserved_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger", "nullable": true}, {"type": "integer", "name": "available_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": true, "table": "jobs", "creatorMethod": "unsignedInteger"}, {"type": "string", "name": "id", "length": 255, "table": "job_batches", "creatorMethod": "string", "primary": true}, {"type": "string", "name": "name", "length": 255, "table": "job_batches", "creatorMethod": "string"}, {"type": "integer", "name": "total_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "pending_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "failed_jobs", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "longText", "name": "failed_job_ids", "table": "job_batches", "creatorMethod": "longText"}, {"type": "mediumText", "name": "options", "table": "job_batches", "creatorMethod": "mediumText", "nullable": true}, {"type": "integer", "name": "cancelled_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "integer", "name": "created_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer"}, {"type": "integer", "name": "finished_at", "autoIncrement": false, "unsigned": false, "table": "job_batches", "creatorMethod": "integer", "nullable": true}, {"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "failed_jobs", "creatorMethod": "id"}, {"type": "string", "name": "uuid", "length": 255, "table": "failed_jobs", "creatorMethod": "string", "unique": true}, {"type": "text", "name": "connection", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "text", "name": "queue", "table": "failed_jobs", "creatorMethod": "text"}, {"type": "longText", "name": "payload", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "longText", "name": "exception", "table": "failed_jobs", "creatorMethod": "longText"}, {"type": "timestamp", "name": "failed_at", "precision": 0, "table": "failed_jobs", "creatorMethod": "timestamp", "useCurrent": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "jobs"}, {"name": "create", "table": "job_batches"}, {"name": "create", "table": "failed_jobs"}], "createdTables": ["jobs", "job_batches", "failed_jobs"], "renamedTables": [], "droppedTables": []}]}, "id": 8, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": 0, "positionY": 0, "sectionId": 2, "removed": false}, "item_9": {"projectId": 1, "name": "personal_access_tokens", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_181144_create_personal_access_tokens_table.php", "relativePath": "/database/migrations/2025_07_09_181144_create_personal_access_tokens_table.php", "migrationName": "2025_07_09_181144_create_personal_access_tokens_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_181144", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "personal_access_tokens", "creatorMethod": "id"}, {"type": "string", "name": "tokenable_type", "length": 255, "table": "personal_access_tokens", "creatorMethod": "morphs"}, {"type": "bigInteger", "name": "tokenable_id", "autoIncrement": false, "unsigned": true, "table": "personal_access_tokens", "creatorMethod": "morphs"}, {"type": "text", "name": "name", "table": "personal_access_tokens", "creatorMethod": "text"}, {"type": "string", "name": "token", "length": 64, "table": "personal_access_tokens", "creatorMethod": "string", "unique": true}, {"type": "text", "name": "abilities", "table": "personal_access_tokens", "creatorMethod": "text", "nullable": true}, {"type": "timestamp", "name": "last_used_at", "precision": 0, "table": "personal_access_tokens", "creatorMethod": "timestamp", "nullable": true}, {"type": "timestamp", "name": "expires_at", "precision": 0, "table": "personal_access_tokens", "creatorMethod": "timestamp", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "personal_access_tokens", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "personal_access_tokens", "creatorMethod": "timestamps", "nullable": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "personal_access_tokens"}, {"name": "index", "index": "personal_access_tokens_tokenable_type_tokenable_id_index", "columns": ["tokenable_type", "tokenable_id"], "algorithm": null, "table": "personal_access_tokens"}], "createdTables": ["personal_access_tokens"], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "personal_access_tokens", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_181144_create_personal_access_tokens_table.php", "relativePath": "/database/migrations/2025_07_09_181144_create_personal_access_tokens_table.php", "migrationName": "2025_07_09_181144_create_personal_access_tokens_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_181144", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "personal_access_tokens", "creatorMethod": "id"}, {"type": "string", "name": "tokenable_type", "length": 255, "table": "personal_access_tokens", "creatorMethod": "morphs"}, {"type": "bigInteger", "name": "tokenable_id", "autoIncrement": false, "unsigned": true, "table": "personal_access_tokens", "creatorMethod": "morphs"}, {"type": "text", "name": "name", "table": "personal_access_tokens", "creatorMethod": "text"}, {"type": "string", "name": "token", "length": 64, "table": "personal_access_tokens", "creatorMethod": "string", "unique": true}, {"type": "text", "name": "abilities", "table": "personal_access_tokens", "creatorMethod": "text", "nullable": true}, {"type": "timestamp", "name": "last_used_at", "precision": 0, "table": "personal_access_tokens", "creatorMethod": "timestamp", "nullable": true}, {"type": "timestamp", "name": "expires_at", "precision": 0, "table": "personal_access_tokens", "creatorMethod": "timestamp", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "personal_access_tokens", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "personal_access_tokens", "creatorMethod": "timestamps", "nullable": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "personal_access_tokens"}, {"name": "index", "index": "personal_access_tokens_tokenable_type_tokenable_id_index", "columns": ["tokenable_type", "tokenable_id"], "algorithm": null, "table": "personal_access_tokens"}], "createdTables": ["personal_access_tokens"], "renamedTables": [], "droppedTables": []}]}, "id": 9, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": 0, "positionY": 0, "sectionId": 2, "removed": false}, "item_10": {"projectId": 1, "name": "teams", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_181144_create_teams_table.php", "relativePath": "/database/migrations/2025_07_09_181144_create_teams_table.php", "migrationName": "2025_07_09_181144_create_teams_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_181144", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "teams", "creatorMethod": "id"}, {"type": "bigInteger", "name": "user_id", "autoIncrement": false, "unsigned": true, "table": "teams", "creatorMethod": "foreignId", "index": true}, {"type": "string", "name": "name", "length": 255, "table": "teams", "creatorMethod": "string"}, {"type": "boolean", "name": "personal_team", "table": "teams", "creatorMethod": "boolean"}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "teams", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "teams", "creatorMethod": "timestamps", "nullable": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "teams"}], "createdTables": ["teams"], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "teams", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_181144_create_teams_table.php", "relativePath": "/database/migrations/2025_07_09_181144_create_teams_table.php", "migrationName": "2025_07_09_181144_create_teams_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_181144", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "teams", "creatorMethod": "id"}, {"type": "bigInteger", "name": "user_id", "autoIncrement": false, "unsigned": true, "table": "teams", "creatorMethod": "foreignId", "index": true}, {"type": "string", "name": "name", "length": 255, "table": "teams", "creatorMethod": "string"}, {"type": "boolean", "name": "personal_team", "table": "teams", "creatorMethod": "boolean"}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "teams", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "teams", "creatorMethod": "timestamps", "nullable": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "teams"}], "createdTables": ["teams"], "renamedTables": [], "droppedTables": []}]}, "id": 10, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": 0, "positionY": 0, "sectionId": 3, "removed": false}, "item_11": {"projectId": 1, "name": "team_user", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_181145_create_team_user_table.php", "relativePath": "/database/migrations/2025_07_09_181145_create_team_user_table.php", "migrationName": "2025_07_09_181145_create_team_user_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_181145", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "team_user", "creatorMethod": "id"}, {"type": "bigInteger", "name": "team_id", "autoIncrement": false, "unsigned": true, "table": "team_user", "creatorMethod": "foreignId"}, {"type": "bigInteger", "name": "user_id", "autoIncrement": false, "unsigned": true, "table": "team_user", "creatorMethod": "foreignId"}, {"type": "string", "name": "role", "length": 255, "table": "team_user", "creatorMethod": "string", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "team_user", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "team_user", "creatorMethod": "timestamps", "nullable": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "team_user"}, {"name": "unique", "index": "team_user_team_id_user_id_unique", "columns": ["team_id", "user_id"], "algorithm": null, "table": "team_user"}], "createdTables": ["team_user"], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "team_user", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_181145_create_team_user_table.php", "relativePath": "/database/migrations/2025_07_09_181145_create_team_user_table.php", "migrationName": "2025_07_09_181145_create_team_user_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_181145", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "team_user", "creatorMethod": "id"}, {"type": "bigInteger", "name": "team_id", "autoIncrement": false, "unsigned": true, "table": "team_user", "creatorMethod": "foreignId"}, {"type": "bigInteger", "name": "user_id", "autoIncrement": false, "unsigned": true, "table": "team_user", "creatorMethod": "foreignId"}, {"type": "string", "name": "role", "length": 255, "table": "team_user", "creatorMethod": "string", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "team_user", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "team_user", "creatorMethod": "timestamps", "nullable": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "team_user"}, {"name": "unique", "index": "team_user_team_id_user_id_unique", "columns": ["team_id", "user_id"], "algorithm": null, "table": "team_user"}], "createdTables": ["team_user"], "renamedTables": [], "droppedTables": []}]}, "id": 11, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": 0, "positionY": 0, "sectionId": 3, "removed": false}, "item_12": {"projectId": 1, "name": "team_invitations", "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_181146_create_team_invitations_table.php", "relativePath": "/database/migrations/2025_07_09_181146_create_team_invitations_table.php", "migrationName": "2025_07_09_181146_create_team_invitations_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_181146", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "team_invitations", "creatorMethod": "id"}, {"type": "bigInteger", "name": "team_id", "autoIncrement": false, "unsigned": true, "table": "team_invitations", "creatorMethod": "foreignId"}, {"type": "string", "name": "email", "length": 255, "table": "team_invitations", "creatorMethod": "string"}, {"type": "string", "name": "role", "length": 255, "table": "team_invitations", "creatorMethod": "string", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "team_invitations", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "team_invitations", "creatorMethod": "timestamps", "nullable": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "team_invitations"}, {"name": "foreign", "index": "team_invitations_team_id_foreign", "columns": ["team_id"], "algorithm": null, "references": "id", "on": "team_invitations", "onDelete": "cascade", "table": "team_invitations"}, {"name": "unique", "index": "team_invitations_team_id_email_unique", "columns": ["team_id", "email"], "algorithm": null, "table": "team_invitations"}], "createdTables": ["team_invitations"], "renamedTables": [], "droppedTables": []}], "oldNames": [], "createdFromInterface": false, "labelColumnId": null, "schemaState": {"name": "team_invitations", "oldNames": [], "migrations": [{"migration": "C:\\xampp\\htdocs\\samy\\Criminal/database/migrations/2025_07_09_181146_create_team_invitations_table.php", "relativePath": "/database/migrations/2025_07_09_181146_create_team_invitations_table.php", "migrationName": "2025_07_09_181146_create_team_invitations_table", "datePrefix": "2025_07_09", "fullPrefix": "2025_07_09_181146", "addedColumns": [{"type": "bigInteger", "name": "id", "autoIncrement": true, "unsigned": true, "table": "team_invitations", "creatorMethod": "id"}, {"type": "bigInteger", "name": "team_id", "autoIncrement": false, "unsigned": true, "table": "team_invitations", "creatorMethod": "foreignId"}, {"type": "string", "name": "email", "length": 255, "table": "team_invitations", "creatorMethod": "string"}, {"type": "string", "name": "role", "length": 255, "table": "team_invitations", "creatorMethod": "string", "nullable": true}, {"type": "timestamp", "name": "created_at", "precision": 0, "table": "team_invitations", "creatorMethod": "timestamps", "nullable": true}, {"type": "timestamp", "name": "updated_at", "precision": 0, "table": "team_invitations", "creatorMethod": "timestamps", "nullable": true}], "changedColumns": [], "droppedColumns": [], "renamedColumns": [], "commands": [{"name": "create", "table": "team_invitations"}, {"name": "foreign", "index": "team_invitations_team_id_foreign", "columns": ["team_id"], "algorithm": null, "references": "id", "on": "team_invitations", "onDelete": "cascade", "table": "team_invitations"}, {"name": "unique", "index": "team_invitations_team_id_email_unique", "columns": ["team_id", "email"], "algorithm": null, "table": "team_invitations"}], "createdTables": ["team_invitations"], "renamedTables": [], "droppedTables": []}]}, "id": 12, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "positionX": 0, "positionY": 0, "sectionId": 3, "removed": false}}, "columns": {"item_1": {"name": "id", "type": "bigInteger", "index": false, "length": null, "unique": false, "tableId": 1, "removed": false, "schemaState": {"name": "id", "unsigned": true, "autoIncrement": true, "type": "bigInteger", "options": []}, "nullable": false, "unsigned": true, "default": "", "autoIncrement": true, "order": 0, "options": [], "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "__tableData": {"count": 82, "lastPrimaryKey": 82, "index": {"1": {"hasMany": {}}, "2": {"hasMany": {"fillable_model_column.columnId": [5], "cruds.defaultSearchColumnId": [1, 2, 3], "inputs.columnId": [1, 6, 11]}}, "3": {"hasMany": {"fillable_model_column.columnId": [6], "inputs.columnId": [2, 7, 12]}}, "4": {"hasMany": {"casts_model_column.columnId": [2]}}, "5": {"hasMany": {"fillable_model_column.columnId": [7], "hidden_model_column.columnId": [1], "casts_model_column.columnId": [3], "inputs.columnId": [3, 8, 13]}}, "6": {"hasMany": {"hidden_model_column.columnId": [4]}}, "7": {"hasMany": {"hidden_model_column.columnId": [3]}}, "8": {"hasMany": {}}, "9": {"hasMany": {"hidden_model_column.columnId": [2]}}, "10": {"hasMany": {}}, "11": {"hasMany": {}}, "12": {"hasMany": {}}, "13": {"hasMany": {"cruds.defaultSortColumnId": [1, 2, 3]}}, "14": {"hasMany": {}}, "15": {"hasMany": {}}, "16": {"hasMany": {}}, "17": {"hasMany": {}}, "18": {"hasMany": {}}, "19": {"hasMany": {}}, "20": {"hasMany": {}}, "21": {"hasMany": {}}, "22": {"hasMany": {}}, "23": {"hasMany": {}}, "24": {"hasMany": {}}, "25": {"hasMany": {}}, "26": {"hasMany": {}}, "27": {"hasMany": {}}, "28": {"hasMany": {}}, "29": {"hasMany": {}}, "30": {"hasMany": {}}, "31": {"hasMany": {}}, "32": {"hasMany": {}}, "33": {"hasMany": {}}, "34": {"hasMany": {}}, "35": {"hasMany": {}}, "36": {"hasMany": {}}, "37": {"hasMany": {}}, "38": {"hasMany": {}}, "39": {"hasMany": {}}, "40": {"hasMany": {}}, "41": {"hasMany": {}}, "42": {"hasMany": {}}, "43": {"hasMany": {}}, "44": {"hasMany": {}}, "45": {"hasMany": {}}, "46": {"hasMany": {}}, "47": {"hasMany": {}}, "48": {"hasMany": {}}, "49": {"hasMany": {}}, "50": {"hasMany": {}}, "51": {"hasMany": {}}, "52": {"hasMany": {}}, "53": {"hasMany": {}}, "54": {"hasMany": {"index_column.columnId": [1]}}, "55": {"hasMany": {"index_column.columnId": [2]}}, "56": {"hasMany": {}}, "57": {"hasMany": {}}, "58": {"hasMany": {}}, "59": {"hasMany": {}}, "60": {"hasMany": {}}, "61": {"hasMany": {}}, "62": {"hasMany": {}}, "63": {"hasMany": {"relationships.parentKeyId": [1]}}, "64": {"hasMany": {}}, "65": {"hasMany": {"fillable_model_column.columnId": [1]}}, "66": {"hasMany": {"fillable_model_column.columnId": [2], "casts_model_column.columnId": [1]}}, "67": {"hasMany": {}}, "68": {"hasMany": {}}, "69": {"hasMany": {}}, "70": {"hasMany": {"index_column.columnId": [3]}}, "71": {"hasMany": {"index_column.columnId": [4]}}, "72": {"hasMany": {}}, "73": {"hasMany": {}}, "74": {"hasMany": {}}, "75": {"hasMany": {"indices.referencesColumnId": [3]}}, "76": {"hasMany": {"index_column.columnId": [5, 6], "relationships.foreignKeyId": [1]}}, "77": {"hasMany": {"index_column.columnId": [7], "fillable_model_column.columnId": [3]}}, "78": {"hasMany": {"fillable_model_column.columnId": [4]}}, "79": {"hasMany": {}}, "80": {"hasMany": {}}, "81": {"hasMany": {"inputs.columnId": [4, 9, 14]}}, "82": {"hasMany": {"inputs.columnId": [5, 10, 15]}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"name": "name", "type": "string", "length": 255, "tableId": 1, "removed": false, "schemaState": {"name": "name", "length": 255, "type": "string", "options": []}, "faker": "fake()->name()", "order": 1, "options": [], "id": 2, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_3": {"name": "email", "type": "string", "length": 255, "unique": true, "tableId": 1, "removed": false, "schemaState": {"name": "email", "length": 255, "type": "string", "unique": true, "options": []}, "faker": "fake()->unique()->safeEmail()", "order": 2, "options": [], "id": 3, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_4": {"name": "email_verified_at", "type": "timestamp", "tableId": 1, "removed": false, "schemaState": {"name": "email_verified_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "now()", "order": 3, "options": [], "id": 4, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_5": {"name": "password", "type": "string", "length": 255, "tableId": 1, "removed": false, "schemaState": {"name": "password", "length": 255, "type": "string", "options": []}, "faker": "\\Hash::make('password')", "order": 4, "options": [], "id": 5, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_6": {"name": "two_factor_secret", "type": "text", "tableId": 1, "removed": false, "schemaState": {"name": "two_factor_secret", "nullable": true, "type": "text", "options": []}, "nullable": true, "faker": "fake()->text()", "order": 5, "options": [], "id": 6, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_7": {"name": "two_factor_recovery_codes", "type": "text", "tableId": 1, "removed": false, "schemaState": {"name": "two_factor_recovery_codes", "nullable": true, "type": "text", "options": []}, "nullable": true, "faker": "fake()->text()", "order": 6, "options": [], "id": 7, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_8": {"name": "two_factor_confirmed_at", "type": "timestamp", "tableId": 1, "removed": false, "schemaState": {"name": "two_factor_confirmed_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "order": 7, "options": [], "id": 8, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_9": {"name": "remember_token", "type": "string", "length": 100, "tableId": 1, "removed": false, "schemaState": {"name": "remember_token", "length": 100, "nullable": true, "type": "string", "options": []}, "nullable": true, "faker": "Str::random(10)", "order": 8, "options": [], "id": 9, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_10": {"name": "current_team_id", "type": "bigInteger", "tableId": 1, "removed": false, "schemaState": {"name": "current_team_id", "nullable": true, "unsigned": true, "autoIncrement": false, "type": "bigInteger", "options": []}, "nullable": true, "unsigned": true, "autoIncrement": false, "order": 9, "options": [], "id": 10, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_11": {"name": "profile_photo_path", "type": "string", "length": 2048, "tableId": 1, "removed": false, "schemaState": {"name": "profile_photo_path", "length": 2048, "nullable": true, "type": "string", "options": []}, "nullable": true, "order": 10, "options": [], "id": 11, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_12": {"name": "created_at", "type": "timestamp", "tableId": 1, "removed": false, "schemaState": {"name": "created_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 13, "options": [], "id": 12, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_13": {"name": "updated_at", "type": "timestamp", "tableId": 1, "removed": false, "schemaState": {"name": "updated_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 14, "options": [], "id": 13, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_14": {"name": "email", "type": "string", "index": false, "length": 255, "unique": false, "tableId": 2, "removed": false, "schemaState": {"name": "email", "length": 255, "type": "string", "options": []}, "nullable": false, "unsigned": false, "default": "", "autoIncrement": false, "faker": "fake()->unique()->safeEmail()", "order": 0, "options": [], "id": 14, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_15": {"name": "token", "type": "string", "length": 255, "tableId": 2, "removed": false, "schemaState": {"name": "token", "length": 255, "type": "string", "options": []}, "faker": "fake()->text({LENGTH})", "order": 1, "options": [], "id": 15, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_16": {"name": "created_at", "type": "timestamp", "tableId": 2, "removed": false, "schemaState": {"name": "created_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 2, "options": [], "id": 16, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_17": {"name": "id", "type": "string", "index": false, "length": 255, "unique": false, "tableId": 3, "removed": false, "schemaState": {"name": "id", "length": 255, "type": "string", "options": []}, "nullable": false, "unsigned": false, "default": "", "autoIncrement": false, "order": 0, "options": [], "id": 17, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_18": {"name": "user_id", "type": "bigInteger", "index": true, "tableId": 3, "removed": false, "schemaState": {"name": "user_id", "nullable": true, "unsigned": true, "autoIncrement": false, "type": "bigInteger", "index": true, "options": []}, "nullable": true, "unsigned": true, "autoIncrement": false, "faker": "fake()->randomNumber()", "order": 1, "options": [], "id": 18, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_19": {"name": "ip_address", "type": "string", "length": 45, "tableId": 3, "removed": false, "schemaState": {"name": "ip_address", "length": 45, "nullable": true, "type": "string", "options": []}, "nullable": true, "faker": "fake()->ipv4()", "order": 2, "options": [], "id": 19, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_20": {"name": "user_agent", "type": "text", "tableId": 3, "removed": false, "schemaState": {"name": "user_agent", "nullable": true, "type": "text", "options": []}, "nullable": true, "faker": "fake()->userAgent()", "order": 3, "options": [], "id": 20, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_21": {"name": "payload", "type": "longText", "tableId": 3, "removed": false, "schemaState": {"name": "payload", "type": "longText", "options": []}, "faker": "fake()->text()", "order": 4, "options": [], "id": 21, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_22": {"name": "last_activity", "type": "integer", "index": true, "tableId": 3, "removed": false, "schemaState": {"name": "last_activity", "unsigned": false, "autoIncrement": false, "type": "integer", "index": true, "options": []}, "unsigned": false, "autoIncrement": false, "faker": "fake()->randomNumber(0)", "order": 5, "options": [], "id": 22, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_23": {"name": "key", "type": "string", "index": false, "length": 255, "unique": false, "tableId": 4, "removed": false, "schemaState": {"name": "key", "length": 255, "type": "string", "options": []}, "nullable": false, "unsigned": false, "default": "", "autoIncrement": false, "faker": "fake()->text({LENGTH})", "order": 0, "options": [], "id": 23, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_24": {"name": "value", "type": "mediumText", "tableId": 4, "removed": false, "schemaState": {"name": "value", "type": "mediumText", "options": []}, "faker": "fake()->text()", "order": 1, "options": [], "id": 24, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_25": {"name": "expiration", "type": "integer", "tableId": 4, "removed": false, "schemaState": {"name": "expiration", "unsigned": false, "autoIncrement": false, "type": "integer", "options": []}, "unsigned": false, "autoIncrement": false, "faker": "fake()->randomNumber(0)", "order": 2, "options": [], "id": 25, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_26": {"name": "key", "type": "string", "index": false, "length": 255, "unique": false, "tableId": 5, "removed": false, "schemaState": {"name": "key", "length": 255, "type": "string", "options": []}, "nullable": false, "unsigned": false, "default": "", "autoIncrement": false, "faker": "fake()->text({LENGTH})", "order": 0, "options": [], "id": 26, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_27": {"name": "owner", "type": "string", "length": 255, "tableId": 5, "removed": false, "schemaState": {"name": "owner", "length": 255, "type": "string", "options": []}, "faker": "fake()->text({LENGTH})", "order": 1, "options": [], "id": 27, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_28": {"name": "expiration", "type": "integer", "tableId": 5, "removed": false, "schemaState": {"name": "expiration", "unsigned": false, "autoIncrement": false, "type": "integer", "options": []}, "unsigned": false, "autoIncrement": false, "faker": "fake()->randomNumber(0)", "order": 2, "options": [], "id": 28, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_29": {"name": "id", "type": "bigInteger", "index": false, "length": null, "unique": false, "tableId": 6, "removed": false, "schemaState": {"name": "id", "unsigned": true, "autoIncrement": true, "type": "bigInteger", "options": []}, "nullable": false, "unsigned": true, "default": "", "autoIncrement": true, "order": 0, "options": [], "id": 29, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_30": {"name": "queue", "type": "string", "index": true, "length": 255, "tableId": 6, "removed": false, "schemaState": {"name": "queue", "length": 255, "type": "string", "index": true, "options": []}, "faker": "fake()->text({LENGTH})", "order": 1, "options": [], "id": 30, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_31": {"name": "payload", "type": "longText", "tableId": 6, "removed": false, "schemaState": {"name": "payload", "type": "longText", "options": []}, "faker": "fake()->text()", "order": 2, "options": [], "id": 31, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_32": {"name": "attempts", "type": "tinyInteger", "tableId": 6, "removed": false, "schemaState": {"name": "attempts", "unsigned": true, "autoIncrement": false, "type": "tinyInteger", "options": []}, "unsigned": true, "autoIncrement": false, "faker": "fake()->numberBetween(0, 127)", "order": 3, "options": [], "id": 32, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_33": {"name": "reserved_at", "type": "integer", "tableId": 6, "removed": false, "schemaState": {"name": "reserved_at", "nullable": true, "unsigned": true, "autoIncrement": false, "type": "integer", "options": []}, "nullable": true, "unsigned": true, "autoIncrement": false, "faker": "fake()->randomNumber(0)", "order": 4, "options": [], "id": 33, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_34": {"name": "available_at", "type": "integer", "tableId": 6, "removed": false, "schemaState": {"name": "available_at", "unsigned": true, "autoIncrement": false, "type": "integer", "options": []}, "unsigned": true, "autoIncrement": false, "faker": "fake()->randomNumber(0)", "order": 5, "options": [], "id": 34, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_35": {"name": "created_at", "type": "integer", "tableId": 6, "removed": false, "schemaState": {"name": "created_at", "unsigned": true, "autoIncrement": false, "type": "integer", "options": []}, "unsigned": true, "autoIncrement": false, "faker": "fake()->dateTime()", "order": 6, "options": [], "id": 35, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_36": {"name": "id", "type": "string", "index": false, "length": 255, "unique": false, "tableId": 7, "removed": false, "schemaState": {"name": "id", "length": 255, "type": "string", "options": []}, "nullable": false, "unsigned": false, "default": "", "autoIncrement": false, "order": 0, "options": [], "id": 36, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_37": {"name": "name", "type": "string", "length": 255, "tableId": 7, "removed": false, "schemaState": {"name": "name", "length": 255, "type": "string", "options": []}, "faker": "fake()->name()", "order": 1, "options": [], "id": 37, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_38": {"name": "total_jobs", "type": "integer", "tableId": 7, "removed": false, "schemaState": {"name": "total_jobs", "unsigned": false, "autoIncrement": false, "type": "integer", "options": []}, "unsigned": false, "autoIncrement": false, "faker": "fake()->randomNumber(0)", "order": 2, "options": [], "id": 38, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_39": {"name": "pending_jobs", "type": "integer", "tableId": 7, "removed": false, "schemaState": {"name": "pending_jobs", "unsigned": false, "autoIncrement": false, "type": "integer", "options": []}, "unsigned": false, "autoIncrement": false, "faker": "fake()->randomNumber(0)", "order": 3, "options": [], "id": 39, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_40": {"name": "failed_jobs", "type": "integer", "tableId": 7, "removed": false, "schemaState": {"name": "failed_jobs", "unsigned": false, "autoIncrement": false, "type": "integer", "options": []}, "unsigned": false, "autoIncrement": false, "faker": "fake()->randomNumber(0)", "order": 4, "options": [], "id": 40, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_41": {"name": "failed_job_ids", "type": "longText", "tableId": 7, "removed": false, "schemaState": {"name": "failed_job_ids", "type": "longText", "options": []}, "faker": "fake()->text()", "order": 5, "options": [], "id": 41, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_42": {"name": "options", "type": "mediumText", "tableId": 7, "removed": false, "schemaState": {"name": "options", "nullable": true, "type": "mediumText", "options": []}, "nullable": true, "faker": "[]", "order": 6, "options": [], "id": 42, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_43": {"name": "cancelled_at", "type": "integer", "tableId": 7, "removed": false, "schemaState": {"name": "cancelled_at", "nullable": true, "unsigned": false, "autoIncrement": false, "type": "integer", "options": []}, "nullable": true, "unsigned": false, "autoIncrement": false, "faker": "fake()->randomNumber(0)", "order": 7, "options": [], "id": 43, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_44": {"name": "created_at", "type": "integer", "tableId": 7, "removed": false, "schemaState": {"name": "created_at", "unsigned": false, "autoIncrement": false, "type": "integer", "options": []}, "unsigned": false, "autoIncrement": false, "faker": "fake()->dateTime()", "order": 8, "options": [], "id": 44, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_45": {"name": "finished_at", "type": "integer", "tableId": 7, "removed": false, "schemaState": {"name": "finished_at", "nullable": true, "unsigned": false, "autoIncrement": false, "type": "integer", "options": []}, "nullable": true, "unsigned": false, "autoIncrement": false, "faker": "fake()->randomNumber(0)", "order": 9, "options": [], "id": 45, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_46": {"name": "id", "type": "bigInteger", "index": false, "length": null, "unique": false, "tableId": 8, "removed": false, "schemaState": {"name": "id", "unsigned": true, "autoIncrement": true, "type": "bigInteger", "options": []}, "nullable": false, "unsigned": true, "default": "", "autoIncrement": true, "order": 0, "options": [], "id": 46, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_47": {"name": "uuid", "type": "string", "length": 255, "unique": true, "tableId": 8, "removed": false, "schemaState": {"name": "uuid", "length": 255, "type": "string", "unique": true, "options": []}, "faker": "fake()->uuid()", "order": 1, "options": [], "id": 47, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_48": {"name": "connection", "type": "text", "tableId": 8, "removed": false, "schemaState": {"name": "connection", "type": "text", "options": []}, "faker": "fake()->text()", "order": 2, "options": [], "id": 48, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_49": {"name": "queue", "type": "text", "tableId": 8, "removed": false, "schemaState": {"name": "queue", "type": "text", "options": []}, "faker": "fake()->text()", "order": 3, "options": [], "id": 49, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_50": {"name": "payload", "type": "longText", "tableId": 8, "removed": false, "schemaState": {"name": "payload", "type": "longText", "options": []}, "faker": "fake()->text()", "order": 4, "options": [], "id": 50, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_51": {"name": "exception", "type": "longText", "tableId": 8, "removed": false, "schemaState": {"name": "exception", "type": "longText", "options": []}, "faker": "fake()->text()", "order": 5, "options": [], "id": 51, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_52": {"name": "failed_at", "type": "timestamp", "tableId": 8, "removed": false, "schemaState": {"name": "failed_at", "type": "timestamp", "options": []}, "faker": "fake()->dateTime()", "order": 6, "options": [], "id": 52, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_53": {"name": "id", "type": "bigInteger", "index": false, "length": null, "unique": false, "tableId": 9, "removed": false, "schemaState": {"name": "id", "unsigned": true, "autoIncrement": true, "type": "bigInteger", "options": []}, "nullable": false, "unsigned": true, "default": "", "autoIncrement": true, "order": 0, "options": [], "id": 53, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_54": {"name": "tokenable_type", "type": "string", "index": true, "length": 255, "tableId": 9, "removed": false, "schemaState": {"name": "tokenable_type", "length": 255, "type": "string", "index": true, "options": []}, "faker": "fake()->text({LENGTH})", "order": 1, "options": [], "id": 54, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_55": {"name": "tokenable_id", "type": "bigInteger", "index": true, "tableId": 9, "removed": false, "schemaState": {"name": "tokenable_id", "unsigned": true, "autoIncrement": false, "type": "bigInteger", "index": true, "options": []}, "unsigned": true, "autoIncrement": false, "faker": "fake()->randomNumber()", "order": 2, "options": [], "id": 55, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_56": {"name": "name", "type": "text", "tableId": 9, "removed": false, "schemaState": {"name": "name", "type": "text", "options": []}, "faker": "fake()->name()", "order": 3, "options": [], "id": 56, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_57": {"name": "token", "type": "string", "length": 64, "unique": true, "tableId": 9, "removed": false, "schemaState": {"name": "token", "length": 64, "type": "string", "unique": true, "options": []}, "faker": "fake()->text({LENGTH})", "order": 4, "options": [], "id": 57, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_58": {"name": "abilities", "type": "text", "tableId": 9, "removed": false, "schemaState": {"name": "abilities", "nullable": true, "type": "text", "options": []}, "nullable": true, "faker": "fake()->text()", "order": 5, "options": [], "id": 58, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_59": {"name": "last_used_at", "type": "timestamp", "tableId": 9, "removed": false, "schemaState": {"name": "last_used_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 6, "options": [], "id": 59, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_60": {"name": "expires_at", "type": "timestamp", "tableId": 9, "removed": false, "schemaState": {"name": "expires_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 7, "options": [], "id": 60, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_61": {"name": "created_at", "type": "timestamp", "tableId": 9, "removed": false, "schemaState": {"name": "created_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 8, "options": [], "id": 61, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_62": {"name": "updated_at", "type": "timestamp", "tableId": 9, "removed": false, "schemaState": {"name": "updated_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 9, "options": [], "id": 62, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_63": {"name": "id", "type": "bigInteger", "index": false, "length": null, "unique": false, "tableId": 10, "removed": false, "schemaState": {"name": "id", "unsigned": true, "autoIncrement": true, "type": "bigInteger", "options": []}, "nullable": false, "unsigned": true, "default": "", "autoIncrement": true, "order": 0, "options": [], "id": 63, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_64": {"name": "user_id", "type": "bigInteger", "index": true, "tableId": 10, "removed": false, "schemaState": {"name": "user_id", "unsigned": true, "autoIncrement": false, "type": "bigInteger", "index": true, "options": []}, "unsigned": true, "autoIncrement": false, "faker": "fake()->randomNumber()", "order": 1, "options": [], "id": 64, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_65": {"name": "name", "type": "string", "length": 255, "tableId": 10, "removed": false, "schemaState": {"name": "name", "length": 255, "type": "string", "options": []}, "faker": "fake()->name()", "order": 2, "options": [], "id": 65, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_66": {"name": "personal_team", "type": "boolean", "tableId": 10, "removed": false, "schemaState": {"name": "personal_team", "type": "boolean", "options": []}, "faker": "fake()->boolean()", "order": 3, "options": [], "id": 66, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_67": {"name": "created_at", "type": "timestamp", "tableId": 10, "removed": false, "schemaState": {"name": "created_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 4, "options": [], "id": 67, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_68": {"name": "updated_at", "type": "timestamp", "tableId": 10, "removed": false, "schemaState": {"name": "updated_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 5, "options": [], "id": 68, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_69": {"name": "id", "type": "bigInteger", "index": false, "length": null, "unique": false, "tableId": 11, "removed": false, "schemaState": {"name": "id", "unsigned": true, "autoIncrement": true, "type": "bigInteger", "options": []}, "nullable": false, "unsigned": true, "default": "", "autoIncrement": true, "order": 0, "options": [], "id": 69, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_70": {"name": "team_id", "type": "bigInteger", "index": true, "tableId": 11, "removed": false, "schemaState": {"name": "team_id", "unsigned": true, "autoIncrement": false, "type": "bigInteger", "index": true, "options": []}, "unsigned": true, "autoIncrement": false, "faker": "fake()->randomNumber()", "order": 1, "options": [], "id": 70, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_71": {"name": "user_id", "type": "bigInteger", "index": true, "tableId": 11, "removed": false, "schemaState": {"name": "user_id", "unsigned": true, "autoIncrement": false, "type": "bigInteger", "index": true, "options": []}, "unsigned": true, "autoIncrement": false, "faker": "fake()->randomNumber()", "order": 2, "options": [], "id": 71, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_72": {"name": "role", "type": "string", "length": 255, "tableId": 11, "removed": false, "schemaState": {"name": "role", "length": 255, "nullable": true, "type": "string", "options": []}, "nullable": true, "faker": "fake()->text({LENGTH})", "order": 3, "options": [], "id": 72, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_73": {"name": "created_at", "type": "timestamp", "tableId": 11, "removed": false, "schemaState": {"name": "created_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 4, "options": [], "id": 73, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_74": {"name": "updated_at", "type": "timestamp", "tableId": 11, "removed": false, "schemaState": {"name": "updated_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 5, "options": [], "id": 74, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_75": {"name": "id", "type": "bigInteger", "index": false, "length": null, "unique": false, "tableId": 12, "removed": false, "schemaState": {"name": "id", "unsigned": true, "autoIncrement": true, "type": "bigInteger", "options": []}, "nullable": false, "unsigned": true, "default": "", "autoIncrement": true, "order": 0, "options": [], "id": 75, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_76": {"name": "team_id", "type": "bigInteger", "index": true, "tableId": 12, "removed": false, "schemaState": {"name": "team_id", "unsigned": true, "autoIncrement": false, "type": "bigInteger", "index": true, "options": []}, "unsigned": true, "autoIncrement": false, "faker": "fake()->randomNumber()", "order": 1, "options": [], "id": 76, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_77": {"name": "email", "type": "string", "index": true, "length": 255, "tableId": 12, "removed": false, "schemaState": {"name": "email", "length": 255, "type": "string", "index": true, "options": []}, "faker": "fake()->unique()->safeEmail()", "order": 2, "options": [], "id": 77, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_78": {"name": "role", "type": "string", "length": 255, "tableId": 12, "removed": false, "schemaState": {"name": "role", "length": 255, "nullable": true, "type": "string", "options": []}, "nullable": true, "faker": "fake()->text({LENGTH})", "order": 3, "options": [], "id": 78, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_79": {"name": "created_at", "type": "timestamp", "tableId": 12, "removed": false, "schemaState": {"name": "created_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 4, "options": [], "id": 79, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_80": {"name": "updated_at", "type": "timestamp", "tableId": 12, "removed": false, "schemaState": {"name": "updated_at", "nullable": true, "type": "timestamp", "options": []}, "nullable": true, "faker": "fake()->dateTime()", "order": 5, "options": [], "id": 80, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04"}, "item_81": {"name": "created_by", "type": "bigInteger", "tableId": 1, "removed": false, "schemaState": {"name": "created_by", "nullable": true, "unsigned": false, "autoIncrement": false, "type": "bigInteger", "options": []}, "nullable": true, "unsigned": false, "autoIncrement": false, "faker": "fake()->word()", "id": 81, "createdAt": "2025-07-09 20:23:55", "updatedAt": "2025-07-09 20:33:04", "order": 11, "options": []}, "item_82": {"name": "updated_by", "type": "bigInteger", "tableId": 1, "removed": false, "schemaState": {"name": "updated_by", "nullable": true, "unsigned": false, "autoIncrement": false, "type": "bigInteger", "options": []}, "nullable": true, "unsigned": false, "autoIncrement": false, "faker": "fake()->word()", "id": 82, "createdAt": "2025-07-09 20:24:07", "updatedAt": "2025-07-09 20:33:04", "order": 12, "options": []}}, "indices": {"item_1": {"tableId": 9, "name": "personal_access_tokens_tokenable_type_tokenable_id_index", "columns": ["tokenable_type", "tokenable_id"], "algorithm": null, "type": "index", "schemaState": {"name": "personal_access_tokens_tokenable_type_tokenable_id_index", "columns": ["tokenable_type", "tokenable_id"], "algorithm": null, "type": "index"}, "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "removed": false}, "__tableData": {"count": 4, "lastPrimaryKey": 4, "index": {"1": {"hasMany": {"index_column.indexId": [1, 2]}}, "2": {"hasMany": {"index_column.indexId": [3, 4]}}, "3": {"hasMany": {"index_column.indexId": [5]}}, "4": {"hasMany": {"index_column.indexId": [6, 7]}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"tableId": 11, "name": "team_user_team_id_user_id_unique", "columns": ["team_id", "user_id"], "algorithm": null, "type": "unique", "schemaState": {"name": "team_user_team_id_user_id_unique", "columns": ["team_id", "user_id"], "algorithm": null, "type": "unique"}, "id": 2, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "removed": false}, "item_3": {"tableId": 12, "name": "team_invitations_team_id_foreign", "columns": ["team_id"], "algorithm": null, "type": "foreign", "on": "team_invitations", "references": "id", "onDelete": "cascade", "schemaState": {"name": "team_invitations_team_id_foreign", "columns": ["team_id"], "algorithm": null, "type": "foreign", "on": "team_invitations", "references": "id", "onDelete": "cascade"}, "id": 3, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "onTableId": 12, "referencesColumnId": 75, "removed": false}, "item_4": {"tableId": 12, "name": "team_invitations_team_id_email_unique", "columns": ["team_id", "email"], "algorithm": null, "type": "unique", "schemaState": {"name": "team_invitations_team_id_email_unique", "columns": ["team_id", "email"], "algorithm": null, "type": "unique"}, "id": 4, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:04", "removed": false}}, "index_column": {"item_1": {"indexId": 1, "columnId": 54, "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "__tableData": {"count": 7, "lastPrimaryKey": 7, "index": {"1": {"hasMany": {}}, "2": {"hasMany": {}}, "3": {"hasMany": {}}, "4": {"hasMany": {}}, "5": {"hasMany": {}}, "6": {"hasMany": {}}, "7": {"hasMany": {}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"indexId": 1, "columnId": 55, "id": 2, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_3": {"indexId": 2, "columnId": 70, "id": 3, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_4": {"indexId": 2, "columnId": 71, "id": 4, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_5": {"indexId": 3, "columnId": 76, "id": 5, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_6": {"indexId": 4, "columnId": 76, "id": 6, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_7": {"indexId": 4, "columnId": 77, "id": 7, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}}, "models": {"item_1": {"projectId": 1, "name": "Membership", "fileName": "Membership.php", "tableName": "team_user", "class": "App\\Models\\Membership", "namespace": "App\\Models", "path": "Models\\Membership.php", "casts": [], "fillable": [], "guarded": [], "dates": [], "hidden": [], "appends": [], "methods": [], "createdFromInterface": false, "parentClass": "Laravel\\Jetstream\\Membership as JetstreamMembership", "interfaces": [], "traits": [], "allImports": ["Laravel\\Jetstream\\Membership as JetstreamMembership"], "hasGuarded": false, "hasFillable": false, "hasTimestamps": true, "hasSoftDeletes": false, "isAuthenticatable": false, "schemaState": {"name": "Membership", "fileName": "Membership.php", "tableName": "team_user", "class": "App\\Models\\Membership", "namespace": "App\\Models", "path": "Models\\Membership.php", "casts": [], "fillable": [], "guarded": [], "dates": [], "hidden": [], "appends": [], "methods": [], "parentClass": "Laravel\\Jetstream\\Membership as JetstreamMembership", "interfaces": [], "traits": [], "allImports": ["Laravel\\Jetstream\\Membership as JetstreamMembership"], "hasGuarded": false, "hasFillable": false, "hasTimestamps": true, "hasSoftDeletes": false, "isAuthenticatable": false}, "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:12", "plural": "Memberships", "pluralAndSingularAreSame": false, "tableId": 11, "callSeeder": true, "seederQuantity": 5, "attributesComments": false, "methodsComments": false, "removed": false}, "__tableData": {"count": 4, "lastPrimaryKey": 4, "index": {"1": {"hasMany": {}}, "2": {"hasMany": {"fillable_model_column.modelId": [1, 2], "casts_model_column.modelId": [1], "relationships.relatedModelId": [1]}}, "3": {"hasMany": {"fillable_model_column.modelId": [3, 4], "relationships.modelId": [1]}}, "4": {"hasMany": {"fillable_model_column.modelId": [5, 6, 7], "hidden_model_column.modelId": [1, 2, 3, 4], "casts_model_column.modelId": [2, 3], "cruds.modelId": [1, 2, 3]}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"projectId": 1, "name": "Team", "fileName": "Team.php", "tableName": "teams", "class": "App\\Models\\Team", "namespace": "App\\Models", "path": "Models\\Team.php", "casts": {"personal_team": "boolean"}, "fillable": ["name", "personal_team"], "guarded": ["*"], "dates": [], "hidden": [], "appends": [], "methods": [], "createdFromInterface": false, "parentClass": "Laravel\\Jetstream\\Team as JetstreamTeam", "interfaces": [], "traits": ["Illuminate\\Database\\Eloquent\\Factories\\HasFactory"], "allImports": ["Illuminate\\Database\\Eloquent\\Factories\\HasFactory", "Laravel\\Jetstream\\Events\\TeamCreated", "Laravel\\Jetstream\\Events\\TeamDeleted", "Laravel\\Jetstream\\Events\\TeamUpdated", "Laravel\\Jetstream\\Team as JetstreamTeam"], "hasGuarded": false, "hasFillable": true, "hasTimestamps": true, "hasSoftDeletes": false, "isAuthenticatable": false, "schemaState": {"name": "Team", "fileName": "Team.php", "tableName": "teams", "class": "App\\Models\\Team", "namespace": "App\\Models", "path": "Models\\Team.php", "casts": {"personal_team": "boolean"}, "fillable": ["name", "personal_team"], "guarded": ["*"], "dates": [], "hidden": [], "appends": [], "methods": [], "parentClass": "Laravel\\Jetstream\\Team as JetstreamTeam", "interfaces": [], "traits": ["Illuminate\\Database\\Eloquent\\Factories\\HasFactory"], "allImports": ["Illuminate\\Database\\Eloquent\\Factories\\HasFactory", "Laravel\\Jetstream\\Events\\TeamCreated", "Laravel\\Jetstream\\Events\\TeamDeleted", "Laravel\\Jetstream\\Events\\TeamUpdated", "Laravel\\Jetstream\\Team as JetstreamTeam"], "hasGuarded": false, "hasFillable": true, "hasTimestamps": true, "hasSoftDeletes": false, "isAuthenticatable": false}, "id": 2, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:12", "plural": "Teams", "pluralAndSingularAreSame": false, "tableId": 10, "callSeeder": true, "seederQuantity": 5, "attributesComments": false, "methodsComments": false, "removed": false}, "item_3": {"projectId": 1, "name": "TeamInvitation", "fileName": "TeamInvitation.php", "tableName": "team_invitations", "class": "App\\Models\\TeamInvitation", "namespace": "App\\Models", "path": "Models\\TeamInvitation.php", "casts": [], "fillable": ["email", "role"], "guarded": ["*"], "dates": [], "hidden": [], "appends": [], "methods": [], "createdFromInterface": false, "parentClass": "Laravel\\Jetstream\\TeamInvitation as JetstreamTeamInvitation", "interfaces": [], "traits": [], "allImports": ["Illuminate\\Database\\Eloquent\\Relations\\BelongsTo", "Laravel\\Jetstream\\Jetstream", "Laravel\\Jetstream\\TeamInvitation as JetstreamTeamInvitation"], "hasGuarded": false, "hasFillable": true, "hasTimestamps": true, "hasSoftDeletes": false, "isAuthenticatable": false, "schemaState": {"name": "TeamInvitation", "fileName": "TeamInvitation.php", "tableName": "team_invitations", "class": "App\\Models\\TeamInvitation", "namespace": "App\\Models", "path": "Models\\TeamInvitation.php", "casts": [], "fillable": ["email", "role"], "guarded": ["*"], "dates": [], "hidden": [], "appends": [], "methods": [], "parentClass": "Laravel\\Jetstream\\TeamInvitation as JetstreamTeamInvitation", "interfaces": [], "traits": [], "allImports": ["Illuminate\\Database\\Eloquent\\Relations\\BelongsTo", "Laravel\\Jetstream\\Jetstream", "Laravel\\Jetstream\\TeamInvitation as JetstreamTeamInvitation"], "hasGuarded": false, "hasFillable": true, "hasTimestamps": true, "hasSoftDeletes": false, "isAuthenticatable": false}, "id": 3, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:12", "plural": "TeamInvitations", "pluralAndSingularAreSame": false, "tableId": 12, "callSeeder": true, "seederQuantity": 5, "attributesComments": false, "methodsComments": false, "removed": false}, "item_4": {"projectId": 1, "name": "User", "fileName": "User.php", "tableName": "users", "class": "App\\Models\\User", "namespace": "App\\Models", "path": "Models\\User.php", "casts": {"email_verified_at": "datetime", "password": "hashed"}, "fillable": ["name", "email", "password"], "guarded": ["*"], "dates": [], "hidden": ["password", "remember_token", "two_factor_recovery_codes", "two_factor_secret"], "appends": ["profile_photo_url"], "methods": [], "createdFromInterface": false, "parentClass": "Illuminate\\Foundation\\Auth\\User as Authenticatable", "interfaces": [], "traits": ["Laravel\\Sanctum\\HasApiTokens", "Illuminate\\Database\\Eloquent\\Factories\\HasFactory", "Laravel\\Jetstream\\HasProfilePhoto", "Laravel\\Jetstream\\HasTeams", "Illuminate\\Notifications\\Notifiable", "Laravel\\Fortify\\TwoFactorAuthenticatable"], "allImports": ["Illuminate\\Contracts\\Auth\\MustVerifyEmail", "Illuminate\\Database\\Eloquent\\Factories\\HasFactory", "Illuminate\\Foundation\\Auth\\User as Authenticatable", "Illuminate\\Notifications\\Notifiable", "Laravel\\Fortify\\TwoFactorAuthenticatable", "Laravel\\Jetstream\\HasProfilePhoto", "Laravel\\Jetstream\\HasTeams", "Laravel\\Sanctum\\HasApiTokens"], "hasGuarded": false, "hasFillable": true, "hasTimestamps": true, "hasSoftDeletes": false, "isAuthenticatable": true, "schemaState": {"name": "User", "fileName": "User.php", "tableName": "users", "class": "App\\Models\\User", "namespace": "App\\Models", "path": "Models\\User.php", "casts": {"email_verified_at": "datetime", "password": "hashed"}, "fillable": ["name", "email", "password"], "guarded": ["*"], "dates": [], "hidden": ["password", "remember_token", "two_factor_recovery_codes", "two_factor_secret"], "appends": ["profile_photo_url"], "methods": [], "parentClass": "Illuminate\\Foundation\\Auth\\User as Authenticatable", "interfaces": [], "traits": ["Laravel\\Sanctum\\HasApiTokens", "Illuminate\\Database\\Eloquent\\Factories\\HasFactory", "Laravel\\Jetstream\\HasProfilePhoto", "Laravel\\Jetstream\\HasTeams", "Illuminate\\Notifications\\Notifiable", "Laravel\\Fortify\\TwoFactorAuthenticatable"], "allImports": ["Illuminate\\Contracts\\Auth\\MustVerifyEmail", "Illuminate\\Database\\Eloquent\\Factories\\HasFactory", "Illuminate\\Foundation\\Auth\\User as Authenticatable", "Illuminate\\Notifications\\Notifiable", "Laravel\\Fortify\\TwoFactorAuthenticatable", "Laravel\\Jetstream\\HasProfilePhoto", "Laravel\\Jetstream\\HasTeams", "Laravel\\Sanctum\\HasApiTokens"], "hasGuarded": false, "hasFillable": true, "hasTimestamps": true, "hasSoftDeletes": false, "isAuthenticatable": true}, "id": 4, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:12", "plural": "Users", "pluralAndSingularAreSame": false, "tableId": 1, "callSeeder": false, "seederQuantity": 5, "attributesComments": true, "methodsComments": true, "removed": false}}, "fillable_model_column": {"item_1": {"modelId": 2, "columnId": 65, "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "__tableData": {"count": 7, "lastPrimaryKey": 7, "index": {"1": {"hasMany": {}}, "2": {"hasMany": {}}, "3": {"hasMany": {}}, "4": {"hasMany": {}}, "5": {"hasMany": {}}, "6": {"hasMany": {}}, "7": {"hasMany": {}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"modelId": 2, "columnId": 66, "id": 2, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_3": {"modelId": 3, "columnId": 77, "id": 3, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_4": {"modelId": 3, "columnId": 78, "id": 4, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_5": {"modelId": 4, "columnId": 2, "id": 5, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_6": {"modelId": 4, "columnId": 3, "id": 6, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_7": {"modelId": 4, "columnId": 5, "id": 7, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}}, "casts_model_column": {"item_1": {"modelId": 2, "columnId": 66, "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40", "type": "boolean"}, "__tableData": {"count": 3, "lastPrimaryKey": 3, "index": {"1": {"hasMany": {}}, "2": {"hasMany": {}}, "3": {"hasMany": {}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"modelId": 4, "columnId": 4, "id": 2, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40", "type": "datetime"}, "item_3": {"modelId": 4, "columnId": 5, "id": 3, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40", "type": "hashed"}}, "relationships": {"item_1": {"modelId": 3, "projectId": 1, "name": "team", "type": "BelongsTo", "relatedTableName": "teams", "relatedModelName": "App\\Models\\Team", "parentTableName": "team_invitations", "parentModelName": "App\\Models\\TeamInvitation", "foreignKeyName": "team_id", "localKeyName": null, "ownerKeyName": "id", "relatedKeyName": null, "morphType": null, "createdFromInterface": false, "foreignPivotKeyName": null, "relatedPivotKeyName": null, "pivotTableName": null, "firstKeyName": null, "secondKeyName": null, "withPivotColumns": false, "includedPivotColumns": [], "schemaState": {"name": "team", "type": "BelongsTo", "relatedTableName": "teams", "relatedModelName": "App\\Models\\Team", "parentTableName": "team_invitations", "parentModelName": "App\\Models\\TeamInvitation", "foreignKeyName": "team_id", "localKeyName": null, "ownerKeyName": "id", "relatedKeyName": null, "morphType": null, "foreignPivotKeyName": null, "relatedPivotKeyName": null, "pivotTableName": null, "firstKeyName": null, "secondKeyName": null, "withPivotColumns": false, "includedPivotColumns": []}, "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:12", "relatedModelId": 2, "parentKeyId": 63, "foreignKeyId": 76, "removed": false}, "__tableData": {"count": 1, "lastPrimaryKey": 1, "index": {"1": {"hasMany": {}}}, "additionalIndexes": {}, "items": [], "relations": []}}, "hidden_model_column": {"item_1": {"modelId": 4, "columnId": 5, "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "__tableData": {"count": 4, "lastPrimaryKey": 4, "index": {"1": {"hasMany": {}}, "2": {"hasMany": {}}, "3": {"hasMany": {}}, "4": {"hasMany": {}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"modelId": 4, "columnId": 9, "id": 2, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_3": {"modelId": 4, "columnId": 7, "id": 3, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_4": {"modelId": 4, "columnId": 6, "id": 4, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}}, "app_sections": {"item_1": {"name": "Dashboard", "routePrefix": "dashboard", "routeBasePath": "dashboard", "projectId": 1, "requiresAuth": true, "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "__tableData": {"count": 4, "lastPrimaryKey": 4, "index": {"1": {"hasMany": {"cruds.sectionId": [1]}}, "2": {"hasMany": {"cruds.sectionId": [2]}}, "3": {"hasMany": {"pages.sectionId": []}}, "4": {"hasMany": {"cruds.sectionId": [3]}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"name": "API", "routePrefix": "api", "routeBasePath": "api", "projectId": 1, "requiresAuth": true, "id": 2, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_3": {"name": "Site", "routePrefix": "", "routeBasePath": "", "projectId": 1, "requiresAuth": false, "id": 3, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_4": {"name": "Filament Panel", "routePrefix": "panel", "routeBasePath": "panel", "projectId": 1, "requiresAuth": false, "id": 4, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}}, "schema_sections": {"item_1": {"name": "App", "scrollX": 24272, "scrollY": 24592, "projectId": 1, "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:32:47"}, "__tableData": {"count": 3, "lastPrimaryKey": 3, "index": {"1": {"hasMany": {"tables.sectionId": [1]}}, "2": {"hasMany": {"tables.sectionId": [2, 3, 4, 5, 6, 7, 8, 9]}}, "3": {"hasMany": {"tables.sectionId": [10, 11, 12]}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"name": "<PERSON><PERSON>", "scrollX": 0, "scrollY": 0, "projectId": 1, "id": 2, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}, "item_3": {"name": "Jetstream", "scrollX": 0, "scrollY": 0, "projectId": 1, "id": 3, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40"}}, "navs": {"item_1": {"projectId": 1, "name": "navigation.home", "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40", "tag": "home"}, "__tableData": {"count": 3, "lastPrimaryKey": 3, "index": {"1": {"hasMany": {}}, "2": {"hasMany": {"navs.parentNavId": [3]}}, "3": {"hasMany": {}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"projectId": 1, "name": "navigation.apps", "id": 2, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:22:40", "tag": "apps"}, "item_3": {"projectId": 1, "navigableType": "<PERSON><PERSON>", "navigableId": 1, "name": "navigation.users", "id": 3, "createdAt": "2025-07-09 20:25:10", "updatedAt": "2025-07-09 20:25:10", "parentNavId": 2}}, "renderable_files": {"item_1": {"path": "bootstrap", "name": "app.php", "fullPath": "bootstrap/app.php", "template": "laravel/BootstrapApp.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 1, "createdAt": "2025-07-09 20:22:40", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nuse Illuminate\\Support\\Facades\\Route;\nuse Illuminate\\Foundation\\Application;\nuse Illuminate\\Foundation\\Configuration\\Exceptions;\nuse Illuminate\\Foundation\\Configuration\\Middleware;\n\nreturn Application::configure(basePath: dirname(__DIR__))\n    ->withProviders()\n    ->withRouting(\n        web: __DIR__ . '/../routes/web.php',\n        // api: __DIR__.'/../routes/api.php',\n        commands: __DIR__ . '/../routes/console.php',\n        // channels: __DIR__.'/../routes/channels.php',\n        health: '/up',\n\n        then: function () {\n            Route::middleware('web')->group(__DIR__ . '/../routes/app.php');\n            Route::middleware('api')->group(__DIR__ . '/../routes/app-api.php');\n        }\n    )\n    ->withMiddleware(function (Middleware $middleware) {\n        //\n    })\n    ->withExceptions(function (Exceptions $exceptions) {\n        //\n    })\n    ->create();\n"}, "__tableData": {"count": 82, "lastPrimaryKey": 82, "index": {"1": {"hasMany": {}}, "2": {"hasMany": {}}, "3": {"hasMany": {}}, "4": {"hasMany": {}}, "5": {"hasMany": {}}, "6": {"hasMany": {}}, "7": {"hasMany": {}}, "8": {"hasMany": {}}, "9": {"hasMany": {}}, "10": {"hasMany": {}}, "11": {"hasMany": {}}, "12": {"hasMany": {}}, "13": {"hasMany": {}}, "14": {"hasMany": {}}, "15": {"hasMany": {}}, "16": {"hasMany": {}}, "17": {"hasMany": {}}, "18": {"hasMany": {}}, "19": {"hasMany": {}}, "20": {"hasMany": {}}, "21": {"hasMany": {}}, "22": {"hasMany": {}}, "23": {"hasMany": {}}, "24": {"hasMany": {}}, "25": {"hasMany": {}}, "26": {"hasMany": {}}, "27": {"hasMany": {}}, "28": {"hasMany": {}}, "29": {"hasMany": {}}, "30": {"hasMany": {}}, "31": {"hasMany": {}}, "32": {"hasMany": {}}, "33": {"hasMany": {}}, "34": {"hasMany": {}}, "35": {"hasMany": {}}, "36": {"hasMany": {}}, "37": {"hasMany": {}}, "38": {"hasMany": {}}, "39": {"hasMany": {}}, "40": {"hasMany": {}}, "41": {"hasMany": {}}, "42": {"hasMany": {}}, "43": {"hasMany": {}}, "44": {"hasMany": {}}, "45": {"hasMany": {}}, "46": {"hasMany": {}}, "47": {"hasMany": {}}, "48": {"hasMany": {}}, "49": {"hasMany": {}}, "50": {"hasMany": {}}, "51": {"hasMany": {}}, "52": {"hasMany": {}}, "53": {"hasMany": {}}, "54": {"hasMany": {}}, "55": {"hasMany": {}}, "56": {"hasMany": {}}, "57": {"hasMany": {}}, "58": {"hasMany": {}}, "59": {"hasMany": {}}, "60": {"hasMany": {}}, "61": {"hasMany": {}}, "62": {"hasMany": {}}, "63": {"hasMany": {}}, "64": {"hasMany": {}}, "65": {"hasMany": {}}, "66": {"hasMany": {}}, "67": {"hasMany": {}}, "68": {"hasMany": {}}, "69": {"hasMany": {}}, "70": {"hasMany": {}}, "71": {"hasMany": {}}, "72": {"hasMany": {}}, "73": {"hasMany": {}}, "74": {"hasMany": {}}, "75": {"hasMany": {}}, "76": {"hasMany": {}}, "77": {"hasMany": {}}, "78": {"hasMany": {}}, "79": {"hasMany": {}}, "80": {"hasMany": {}}, "81": {"hasMany": {}}, "82": {"hasMany": {}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"path": "routes", "name": "app.php", "fullPath": "routes/app.php", "template": "routes/Routes.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 2, "createdAt": "2025-07-09 20:22:41", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nuse Illuminate\\Support\\Facades\\Route;\n\n/*\n|--------------------------------------------------------------------------\n| Web Routes - Generated by Vemto\n|--------------------------------------------------------------------------\n|\n| It is not recommended to edit this file directly. Although you can do so,\n| it will generate a conflict on Vemto's next build.\n|\n*/\n\n// Dashboard\nRoute::prefix('/dashboard')\n    ->name('dashboard.')\n    ->middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified'])\n    ->group(function () {\n        Route::get('/users', App\\Livewire\\Dashboard\\UserIndex::class)->name(\n            'users.index'\n        );\n\n        Route::get(\n            '/users/create',\n            App\\Livewire\\Dashboard\\UserCreate::class\n        )->name('users.create');\n\n        Route::get(\n            '/users/{user}',\n            App\\Livewire\\Dashboard\\UserEdit::class\n        )->name('users.edit');\n    });\n\n// API\nRoute::prefix('/api')\n    ->name('api.')\n    ->middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified'])\n    ->group(function () {});\n\n// Site\nRoute::prefix('/')->group(function () {});\n\n// Filament Panel\nRoute::prefix('/panel')\n    ->name('panel.')\n    ->group(function () {});\n"}, "item_3": {"path": "routes", "name": "app-api.php", "fullPath": "routes/app-api.php", "template": "routes/ApiRoutes.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 3, "createdAt": "2025-07-09 20:22:41", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nuse Illuminate\\Support\\Facades\\Route;\nuse App\\Http\\Controllers\\Api\\AuthController;\nuse App\\Http\\Controllers\\Api\\UserController;\n\n/*\n|--------------------------------------------------------------------------\n| Api Routes - Generated by Vemto\n|--------------------------------------------------------------------------\n|\n| It is not recommended to edit this file directly. Although you can do so,\n| it will generate a conflict on Vemto's next build.\n|\n*/\n\nRoute::name('api.')\n    ->prefix('api')\n    ->group(function () {\n        Route::post('/login', [AuthController::class, 'login'])->name(\n            'api.login'\n        );\n\n        Route::middleware('auth:sanctum')->group(function () {\n            Route::get('/users', [UserController::class, 'index'])->name(\n                'users.index'\n            );\n\n            Route::post('/users', [UserController::class, 'store'])->name(\n                'users.store'\n            );\n\n            Route::get('/users/{user}', [UserController::class, 'show'])->name(\n                'users.show'\n            );\n\n            Route::put('/users/{user}', [\n                UserController::class,\n                'update',\n            ])->name('users.update');\n\n            Route::delete('/users/{user}', [\n                UserController::class,\n                'destroy',\n            ])->name('users.destroy');\n        });\n    });\n"}, "item_4": {"path": "resources/views/components/ui", "name": "/toast.blade.php", "fullPath": "resources/views/components/ui//toast.blade.php", "template": "blade/ui/Toast.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 4, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props([\r\n    'on',\r\n    'timeout' => 2000,\r\n    'closeable' => true,\r\n])\r\n\r\n<div x-data=\"{ shown: false, timeout: null }\"\r\n    x-init=\"@this.on('{{ $on }}', () => { clearTimeout(timeout); shown = true; timeout = setTimeout(() => { shown = false }, {{ $timeout }});  })\"\r\n    x-show.transition.out.opacity.duration.500ms=\"shown\"\r\n    x-transition:leave.opacity.duration.500ms\r\n    x-transition:enter.opacity.duration.500ms\r\n    style=\"display: none;\"\r\n    {{ $attributes->merge(['class' => 'fixed right-0 top-0 p-2']) }}>\r\n    <div class=\"flex items-center bg-green-500 text-white rounded px-6 py-4\">\r\n        <div>\r\n            {{ $slot->isEmpty() ? 'Saved.' : $slot }}\r\n        </div>\r\n\r\n        @if($closeable)\r\n        <div @click=\"shown = false\" class=\"ml-4 cursor-pointer\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"w-4 h-4\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n            </svg>              \r\n        </div>\r\n        @endif\r\n    </div>\r\n</div>"}, "item_5": {"path": "resources/views/components/ui", "name": "/action/danger.blade.php", "fullPath": "resources/views/components/ui//action/danger.blade.php", "template": "blade/ui/action/Danger.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 5, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<a {!! $attributes->merge(['class' => 'text-red-600 hover:text-red-700 cursor-pointer']) !!}>\r\n    {{ $slot }}\r\n</a>"}, "item_6": {"path": "resources/views/components/ui", "name": "/action/index.blade.php", "fullPath": "resources/views/components/ui//action/index.blade.php", "template": "blade/ui/action/Index.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 6, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<a {!! $attributes->merge(['class' => 'text-indigo-600 hover:text-indigo-700 cursor-pointer']) !!}>\r\n    {{ $slot }}\r\n</a>"}, "item_7": {"path": "resources/views/components/ui", "name": "/breadcrumbs/index.blade.php", "fullPath": "resources/views/components/ui//breadcrumbs/index.blade.php", "template": "blade/ui/breadcrumbs/Index.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 7, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<nav class=\"flex justify-between py-1\">\r\n    <ol class=\"inline-flex items-center mb-3 space-x-1 text-xs text-neutral-500 [&_.active-breadcrumb]:text-neutral-600 [&_.active-breadcrumb]:font-medium sm:mb-0\"> \r\n        {{ $slot }}\r\n    </ol>\r\n</nav>"}, "item_8": {"path": "resources/views/components/ui", "name": "/breadcrumbs/link.blade.php", "fullPath": "resources/views/components/ui//breadcrumbs/link.blade.php", "template": "blade/ui/breadcrumbs/Link.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 8, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props([\r\n    'href' => false,\r\n    'active' => false,\r\n])\r\n\r\n<li class=\"\">\r\n    <a \r\n        @if($href)\r\n            wire:navigate\r\n            href=\"{{ $href }}\"\r\n        @endif\r\n        class=\"inline-flex items-center py-1 font-normal hover:text-neutral-900 focus:outline-none {{ $active ? 'active-breadcrumb' : '' }}\"\r\n    >\r\n        {{ $slot }}\r\n    </a>\r\n</li>"}, "item_9": {"path": "resources/views/components/ui", "name": "/breadcrumbs/separator.blade.php", "fullPath": "resources/views/components/ui//breadcrumbs/separator.blade.php", "template": "blade/ui/breadcrumbs/Separator.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 9, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<svg class=\"w-5 h-5 text-gray-400/70\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><g fill=\"none\" stroke=\"none\"><path d=\"M10 8.013l4 4-4 4\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path></g></svg>"}, "item_10": {"path": "resources/views/components/ui", "name": "/button/danger.blade.php", "fullPath": "resources/views/components/ui//button/danger.blade.php", "template": "blade/ui/button/Danger.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 10, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<button {{ $attributes->merge(['type' => 'button', 'class' => 'inline-flex items-center justify-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150']) }}>\r\n    {{ $slot }}\r\n</button>"}, "item_11": {"path": "resources/views/components/ui", "name": "/button/index.blade.php", "fullPath": "resources/views/components/ui//button/index.blade.php", "template": "blade/ui/button/Index.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 11, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<button {{ $attributes->merge(['type' => 'submit', 'class' => 'inline-flex items-center justify-center px-4 py-2 text-sm font-medium tracking-wide text-white transition-colors duration-200 rounded-md bg-indigo-500 hover:bg-indigo-600 focus:ring-2 focus:ring-offset-2 focus:ring-gray-900 focus:shadow-outline focus:outline-none']) }}>\r\n    {{ $slot }}\r\n</button>"}, "item_12": {"path": "resources/views/components/ui", "name": "/card/body.blade.php", "fullPath": "resources/views/components/ui//card/body.blade.php", "template": "blade/ui/card/Body.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 12, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": ""}, "item_13": {"path": "resources/views/components/ui", "name": "/card/header.blade.php", "fullPath": "resources/views/components/ui//card/header.blade.php", "template": "blade/ui/card/Header.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 13, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": ""}, "item_14": {"path": "resources/views/components/ui", "name": "/card/index.blade.php", "fullPath": "resources/views/components/ui//card/index.blade.php", "template": "blade/ui/card/Index.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 14, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": ""}, "item_15": {"path": "resources/views/components/ui", "name": "/card/title.blade.php", "fullPath": "resources/views/components/ui//card/title.blade.php", "template": "blade/ui/card/Title.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 15, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": ""}, "item_16": {"path": "resources/views/components/ui", "name": "/container/table.blade.php", "fullPath": "resources/views/components/ui//container/table.blade.php", "template": "blade/ui/container/Table.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 16, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<div class=\"flex flex-col\">\r\n    <div class=\"overflow-x-auto\">\r\n        <div class=\"inline-block min-w-full\">\r\n            {{ $slot }}\r\n        </div>\r\n    </div>\r\n</div>\r\n"}, "item_17": {"path": "resources/views/components/ui", "name": "/input/checkbox.blade.php", "fullPath": "resources/views/components/ui//input/checkbox.blade.php", "template": "blade/ui/input/Checkbox.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 17, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<input \r\n    {{ $disabled ? 'disabled' : '' }} \r\n    type=\"checkbox\"\r\n    {!! $attributes->merge(['class' => '']) !!}\r\n>"}, "item_18": {"path": "resources/views/components/ui", "name": "/input/color.blade.php", "fullPath": "resources/views/components/ui//input/color.blade.php", "template": "blade/ui/input/Color.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 18, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<x-ui.input \r\n    :disabled=\"$disabled\" \r\n    type=\"color\"\r\n    :attributes=\"$attributes\"\r\n/>"}, "item_19": {"path": "resources/views/components/ui", "name": "/input/date.blade.php", "fullPath": "resources/views/components/ui//input/date.blade.php", "template": "blade/ui/input/Date.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 19, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<x-ui.input \r\n    :disabled=\"$disabled\" \r\n    type=\"date\"\r\n    :attributes=\"$attributes\"\r\n/>"}, "item_20": {"path": "resources/views/components/ui", "name": "/input/date-time.blade.php", "fullPath": "resources/views/components/ui//input/date-time.blade.php", "template": "blade/ui/input/DateTime.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 20, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<x-ui.input \r\n    :disabled=\"$disabled\" \r\n    type=\"datetime-local\"\r\n    :attributes=\"$attributes\"\r\n/>"}, "item_21": {"path": "resources/views/components/ui", "name": "/input/email.blade.php", "fullPath": "resources/views/components/ui//input/email.blade.php", "template": "blade/ui/input/Email.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 21, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<x-ui.input \r\n    :disabled=\"$disabled\" \r\n    type=\"email\"\r\n    :attributes=\"$attributes\"\r\n/>"}, "item_22": {"path": "resources/views/components/ui", "name": "/input/error.blade.php", "fullPath": "resources/views/components/ui//input/error.blade.php", "template": "blade/ui/input/Error.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 22, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['for'])\r\n\r\n@error($for)\r\n    <p {{ $attributes->merge(['class' => 'text-sm text-red-600']) }}>{{ $message }}</p>\r\n@enderror\r\n"}, "item_23": {"path": "resources/views/components/ui", "name": "/input/file.blade.php", "fullPath": "resources/views/components/ui//input/file.blade.php", "template": "blade/ui/input/File.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 23, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props([\r\n    'src' => '',\r\n    'accept' => '*',\r\n])\r\n\r\n<div {!! $attributes->except(['wire:model']) !!} x-data=\"{\r\n    fileUrl: '{{ $src }}',\r\n    file: null,\r\n    fileInput: null,\r\n    initFileInput() {\r\n        this.fileInput = this.$el.querySelector('input[type=file]')\r\n\r\n        this.fileInput.addEventListener('change', () => {\r\n            this.file = this.fileInput.files[0]\r\n            this.fileUrl = URL.createObjectURL(this.file)\r\n        })\r\n    },\r\n    destroyFileInput() {\r\n        this.fileInput.removeEventListener('change')\r\n    },\r\n    remove() {\r\n        if (!confirm('Are you sure?')) return\r\n\r\n        this.fileUrl = ''\r\n        this.file = null\r\n        this.fileInput.value = ''\r\n\r\n        this.$dispatch('removed')\r\n    }\r\n}\" x-init=\"initFileInput\" x-on:destroy.window=\"destroyFileInput\">\r\n    <div class=\"mb-2\">\r\n        {{-- Show the filename --}}\r\n        <template x-if=\"file\">\r\n            <div class=\"text-sm text-gray-500 p-1 rounded-sm bg-gray-50 border border-gray-200 mb-1\" x-text=\"file.name\"></div>\r\n        </template>\r\n\r\n        {{-- File selector --}}\r\n        <input style=\"display: none;\" type=\"file\" accept=\"{{ $accept }}\" {!! $attributes->only([ 'wire:model']) !!}>\r\n\r\n        {{-- Upload and remove buttons --}}\r\n        <div>\r\n            <button type=\"button\" class=\"p-0.5 px-1 text-xs border border-gray-200 rounded hover:bg-gray-200\" @click.stop=\"fileInput.click()\">Upload</button>\r\n            <button type=\"button\" class=\"p-0.5 px-1 text-xs border border-gray-200 rounded hover:bg-gray-200\" x-show=\"fileUrl\" @click.stop=\"remove()\">Remove</button>\r\n        </div>\r\n    </div>\r\n</div>"}, "item_24": {"path": "resources/views/components/ui", "name": "/input/hidden.blade.php", "fullPath": "resources/views/components/ui//input/hidden.blade.php", "template": "blade/ui/input/Hidden.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 24, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<x-ui.input \r\n    :disabled=\"$disabled\" \r\n    type=\"hidden\"\r\n    :attributes=\"$attributes\"\r\n/>"}, "item_25": {"path": "resources/views/components/ui", "name": "/input/image.blade.php", "fullPath": "resources/views/components/ui//input/image.blade.php", "template": "blade/ui/input/Image.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 25, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props([\r\n    'src' => '',\r\n    'accept' => 'image/*',\r\n])\r\n\r\n<div {!! $attributes->except(['wire:model']) !!} x-data=\"{\r\n    imageUrl: '{{ $src }}',\r\n    imageFile: null,\r\n    imageInput: null,\r\n    initFileInput() {\r\n        this.imageInput = this.$el.querySelector('input[type=file]')\r\n\r\n        this.imageInput.addEventListener('change', () => {\r\n            this.imageFile = this.imageInput.files[0]\r\n            this.imageUrl = URL.createObjectURL(this.imageFile)\r\n        })\r\n    },\r\n    destroyFileInput() {\r\n        this.imageInput.removeEventListener('change')\r\n    },\r\n    remove() {\r\n        if (!confirm('Are you sure?')) return\r\n\r\n        this.imageUrl = ''\r\n        this.imageFile = null\r\n        this.imageInput.value = ''\r\n\r\n        this.$dispatch('removed')\r\n    }\r\n}\" x-init=\"initFileInput\" x-on:destroy.window=\"destroyFileInput\">\r\n    <div class=\"mb-2\">\r\n\r\n        {{-- Show the image --}}\r\n        <template x-if=\"imageUrl\">\r\n            <img @click.stop=\"imageInput.click()\" :src=\"imageUrl\" class=\"object-cover rounded border border-gray-200 w-36 h-36 cursor-pointer bg-gray-50 hover:opacity-75 transition duration-100 ease-in-out\">\r\n        </template>\r\n\r\n        {{-- Show the gray box when image is not available --}}\r\n        <template x-if=\"!imageUrl\">\r\n            <div @click.stop=\"imageInput.click()\" class=\"object-cover rounded border border-gray-200 w-36 h-36 cursor-pointer bg-gray-50 hover:opacity-75 transition duration-100 ease-in-out\"></div>\r\n        </template>\r\n\r\n        {{-- Image file selector --}}\r\n        <input style=\"display: none;\" type=\"file\" accept=\"{{ $accept }}\" {!! $attributes->only([ 'wire:model']) !!}>\r\n\r\n        {{-- Upload and remove buttons --}}\r\n        <div>\r\n            <button type=\"button\" class=\"p-0.5 px-1 text-xs border border-gray-200 rounded hover:bg-gray-200\" @click.stop=\"imageInput.click()\">Upload</button>\r\n            <button type=\"button\" class=\"p-0.5 px-1 text-xs border border-gray-200 rounded hover:bg-gray-200\" x-show=\"imageUrl\" @click.stop=\"remove()\">Remove</button>\r\n        </div>\r\n    </div>\r\n</div>"}, "item_26": {"path": "resources/views/components/ui", "name": "/input/index.blade.php", "fullPath": "resources/views/components/ui//input/index.blade.php", "template": "blade/ui/input/Index.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 26, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<input \r\n    {{ $disabled ? 'disabled' : '' }} \r\n    {!! $attributes->merge(['class' => 'inline-flex h-10 px-3 py-2 text-sm bg-white border rounded-md border-gray-300 ring-offset-background placeholder:text-gray-500 focus:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:cursor-not-allowed disabled:opacity-50']) !!}\r\n>\r\n"}, "item_27": {"path": "resources/views/components/ui", "name": "/input/number.blade.php", "fullPath": "resources/views/components/ui//input/number.blade.php", "template": "blade/ui/input/Number.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 27, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<x-ui.input \r\n    :disabled=\"$disabled\" \r\n    type=\"number\"\r\n    :attributes=\"$attributes\"\r\n/>"}, "item_28": {"path": "resources/views/components/ui", "name": "/input/password.blade.php", "fullPath": "resources/views/components/ui//input/password.blade.php", "template": "blade/ui/input/Password.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 28, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<x-ui.input \r\n    :disabled=\"$disabled\" \r\n    type=\"password\"\r\n    :attributes=\"$attributes\"\r\n/>"}, "item_29": {"path": "resources/views/components/ui", "name": "/input/radio.blade.php", "fullPath": "resources/views/components/ui//input/radio.blade.php", "template": "blade/ui/input/Radio.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 29, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<input \r\n    :disabled=\"$disabled\" \r\n    type=\"radio\"\r\n    {!! $attributes->merge(['class' => '!w-4 !h-4 text-indigo-600 transition duration-150 ease-in-out border-gray-300 rounded-md focus:ring-indigo-500']) !!}\r\n/>"}, "item_30": {"path": "resources/views/components/ui", "name": "/input/select.blade.php", "fullPath": "resources/views/components/ui//input/select.blade.php", "template": "blade/ui/input/Select.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 30, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<select \r\n    {{ $disabled ? 'disabled' : '' }} \r\n    {!! $attributes->merge(['class' => 'inline-flex h-10 px-3 py-2 text-sm bg-white border rounded-md border-gray-300 ring-offset-background placeholder:text-gray-500 focus:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:cursor-not-allowed disabled:opacity-50']) !!}\r\n>\r\n    {{ $slot }}\r\n</select>"}, "item_31": {"path": "resources/views/components/ui", "name": "/input/text.blade.php", "fullPath": "resources/views/components/ui//input/text.blade.php", "template": "blade/ui/input/Text.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 31, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<x-ui.input \r\n    :disabled=\"$disabled\" \r\n    type=\"text\"\r\n    :attributes=\"$attributes\"\r\n/>"}, "item_32": {"path": "resources/views/components/ui", "name": "/input/textarea.blade.php", "fullPath": "resources/views/components/ui//input/textarea.blade.php", "template": "blade/ui/input/Textarea.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 32, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<textarea \r\n    {{ $disabled ? 'disabled' : '' }} \r\n    {!! $attributes->merge(['class' => 'inline-flex h-10 px-3 py-2 text-sm bg-white border rounded-md border-gray-300 ring-offset-background placeholder:text-gray-500 focus:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:cursor-not-allowed disabled:opacity-50']) !!}\r\n></textarea>"}, "item_33": {"path": "resources/views/components/ui", "name": "/input/time.blade.php", "fullPath": "resources/views/components/ui//input/time.blade.php", "template": "blade/ui/input/Time.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 33, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<x-ui.input \r\n    :disabled=\"$disabled\" \r\n    type=\"time\"\r\n    :attributes=\"$attributes\"\r\n/>"}, "item_34": {"path": "resources/views/components/ui", "name": "/input/url.blade.php", "fullPath": "resources/views/components/ui//input/url.blade.php", "template": "blade/ui/input/Url.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 34, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['disabled' => false])\r\n\r\n<x-ui.input \r\n    :disabled=\"$disabled\" \r\n    type=\"url\"\r\n    :attributes=\"$attributes\"\r\n/>"}, "item_35": {"path": "resources/views/components/ui", "name": "/label/index.blade.php", "fullPath": "resources/views/components/ui//label/index.blade.php", "template": "blade/ui/label/Index.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 35, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['value'])\r\n\r\n<label {{ $attributes->merge(['class' => 'block mb-1.5 font-medium text-sm text-gray-700']) }}>\r\n    {{ $value ?? $slot }}\r\n</label>"}, "item_36": {"path": "resources/views/components/ui", "name": "/modal/confirm.blade.php", "fullPath": "resources/views/components/ui//modal/confirm.blade.php", "template": "blade/ui/modal/Confirm.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 36, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['id' => null, 'maxWidth' => null])\r\n\r\n<x-ui.modal :id=\"$id\" :maxWidth=\"$maxWidth\" {{ $attributes }}>\r\n    <div class=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\r\n        <div class=\"sm:flex sm:items-start\">\r\n            <div class=\"mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10\">\r\n                <svg class=\"h-6 w-6 text-red-600\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\" />\r\n                </svg>\r\n            </div>\r\n\r\n            <div class=\"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left\">\r\n                <h3 class=\"text-lg font-medium text-gray-900\">\r\n                    {{ $title }}\r\n                </h3>\r\n\r\n                <div class=\"mt-4 text-sm text-gray-600\">\r\n                    {{ $content }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"flex flex-row justify-between p-4 bg-gray-100 text-right\">\r\n        {{ $footer }}\r\n    </div>\r\n</x-ui.modal>"}, "item_37": {"path": "resources/views/components/ui", "name": "/modal/index.blade.php", "fullPath": "resources/views/components/ui//modal/index.blade.php", "template": "blade/ui/modal/Index.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 37, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props(['id', 'maxWidth'])\r\n\r\n@php\r\n$id = $id ?? md5($attributes->wire('model'));\r\n\r\n$maxWidth = [\r\n    'sm' => 'sm:max-w-sm',\r\n    'md' => 'sm:max-w-md',\r\n    'lg' => 'sm:max-w-lg',\r\n    'xl' => 'sm:max-w-xl',\r\n    '2xl' => 'sm:max-w-2xl',\r\n][$maxWidth ?? '2xl'];\r\n@endphp\r\n\r\n<div\r\n    x-data=\"{ show: @entangle($attributes->wire('model')) }\"\r\n    x-on:close.stop=\"show = false\"\r\n    x-on:keydown.escape.window=\"show = false\"\r\n    x-show=\"show\"\r\n    id=\"{{ $id }}\"\r\n    class=\"jetstream-modal fixed inset-0 overflow-y-auto px-4 py-6 sm:px-0 z-50\"\r\n    style=\"display: none;\"\r\n>\r\n    <div x-show=\"show\" class=\"fixed inset-0 transform transition-all\" x-on:click=\"show = false\" x-transition:enter=\"ease-out duration-300\"\r\n                    x-transition:enter-start=\"opacity-0\"\r\n                    x-transition:enter-end=\"opacity-100\"\r\n                    x-transition:leave=\"ease-in duration-200\"\r\n                    x-transition:leave-start=\"opacity-100\"\r\n                    x-transition:leave-end=\"opacity-0\">\r\n        <div class=\"absolute inset-0 bg-gray-500 opacity-75\"></div>\r\n    </div>\r\n\r\n    <div x-show=\"show\" \r\n        class=\"mb-6 bg-white rounded-lg overflow-hidden shadow-xl transform transition-all sm:w-full {{ $maxWidth }} sm:mx-auto\"\r\n        x-trap.inert.noscroll=\"show\"\r\n        x-transition:enter=\"ease-out duration-300\"\r\n        x-transition:enter-start=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\"\r\n        x-transition:enter-end=\"opacity-100 translate-y-0 sm:scale-100\"\r\n        x-transition:leave=\"ease-in duration-200\"\r\n        x-transition:leave-start=\"opacity-100 translate-y-0 sm:scale-100\"\r\n        x-transition:leave-end=\"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95\">\r\n        {{ $slot }}\r\n    </div>\r\n</div>\r\n"}, "item_38": {"path": "resources/views/components/ui", "name": "/table/action-column.blade.php", "fullPath": "resources/views/components/ui//table/action-column.blade.php", "template": "blade/ui/table/ActionColumn.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 38, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<td {!! $attributes->merge(['class' => 'px-5 py-4 text-sm font-medium text-right whitespace-nowrap space-x-2']) !!}>\r\n    {{ $slot }}\r\n</td>"}, "item_39": {"path": "resources/views/components/ui", "name": "/table/action-header.blade.php", "fullPath": "resources/views/components/ui//table/action-header.blade.php", "template": "blade/ui/table/ActionHeader.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 39, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<th {!! $attributes->merge(['class' => 'px-5 py-3 text-xs font-medium text-right uppercase']) !!}>\r\n    {{ $slot }}\r\n</th>"}, "item_40": {"path": "resources/views/components/ui", "name": "/table/column.blade.php", "fullPath": "resources/views/components/ui//table/column.blade.php", "template": "blade/ui/table/Column.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 40, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props([\r\n    'forCrud' => false\r\n])\r\n\r\n@php\r\n$allClasses = [\r\n    'px-5 py-3 text-xs font-medium text-left uppercase' => true,\r\n    'max-w-xs truncate' => $forCrud,\r\n];\r\n\r\n$classes = join(' ', array_keys(array_filter($allClasses)));\r\n@endphp\r\n\r\n<td {!! $attributes->merge(['class' => 'px-5 py-4 text-sm whitespace-nowrap']) !!}>\r\n    {{ $slot }}\r\n</td>"}, "item_41": {"path": "resources/views/components/ui", "name": "/table/header.blade.php", "fullPath": "resources/views/components/ui//table/header.blade.php", "template": "blade/ui/table/Header.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 41, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props([\r\n    'forCrud' => false\r\n])\r\n\r\n@php\r\n$allClasses = [\r\n    'px-5 py-3 text-xs font-medium text-left uppercase' => true,\r\n    'cursor-pointer' => $forCrud,\r\n];\r\n\r\n$classes = join(' ', array_keys(array_filter($allClasses)));\r\n@endphp\r\n\r\n<th {!! $attributes->merge(['class' => $classes]) !!}>\r\n    {{ $slot }}\r\n</th>"}, "item_42": {"path": "resources/views/components/ui", "name": "/table/image.blade.php", "fullPath": "resources/views/components/ui//table/image.blade.php", "template": "blade/ui/table/Image.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 42, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props([\r\n    'url' => '',\r\n    'alt' => ''\r\n])\r\n\r\n<img\r\n    src=\"{{ $url }}\"\r\n    alt=\"{{ $alt }}\"\r\n    class=\"w-12 h-12 rounded-lg bg-center\"\r\n/>"}, "item_43": {"path": "resources/views/components/ui", "name": "/table/index.blade.php", "fullPath": "resources/views/components/ui//table/index.blade.php", "template": "blade/ui/table/Index.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 43, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@props([\r\n    'head',\r\n    'body',\r\n    'foot'\r\n])\r\n\r\n<div class=\"overflow-hidden border rounded-lg\">\r\n    <table {!! $attributes->merge(['class' => 'min-w-full divide-y divide-gray-200']) !!}>\r\n        @if(isset($head))\r\n        <thead class=\"bg-gray-50\">\r\n            <tr class=\"text-gray-500\">\r\n                {{ $head }}\r\n            </tr>\r\n        </thead>\r\n        @endif\r\n\r\n        @if(isset($body))\r\n        <tbody class=\"bg-white divide-y divide-gray-200 dark:divide-gray-700\">\r\n            {{ $body }}\r\n        </tbody>\r\n        @endif\r\n\r\n        @if(isset($foot))\r\n        <tfoot class=\"\">\r\n            {{ $foot }}\r\n        </tfoot>\r\n        @endif\r\n    </table>\r\n    \r\n</div>"}, "item_44": {"path": "resources/views/components/ui", "name": "/table/row.blade.php", "fullPath": "resources/views/components/ui//table/row.blade.php", "template": "blade/ui/table/Row.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 44, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<tr {!! $attributes->merge(['class' => 'text-gray-800']) !!}>\r\n    {{ $slot }}\r\n</tr>"}, "item_45": {"path": "resources/views/partials", "name": "app-menu.blade.php", "fullPath": "resources/views/partials/app-menu.blade.php", "template": "menu/AppMenu.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 45, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<div class=\"hidden sm:flex sm:items-center sm:ml-6\">\n    <div class=\"ml-3 relative\">\n        <x-dropdown align=\"right\" width=\"48\">\n            <x-slot name=\"trigger\">\n                <span class=\"inline-flex rounded-md\">\n                    <button\n                        type=\"button\"\n                        class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none focus:bg-gray-50 active:bg-gray-50 transition ease-in-out duration-150\"\n                    >\n                        {{ __('navigation.home') }}\n\n                        <svg\n                            class=\"ml-2 -mr-0.5 h-4 w-4\"\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                        >\n                            <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\"\n                            />\n                        </svg>\n                    </button>\n                </span>\n            </x-slot>\n\n            <x-slot name=\"content\">\n                <x-dropdown-link> No items found </x-dropdown-link>\n            </x-slot>\n        </x-dropdown>\n    </div>\n    <div class=\"ml-3 relative\">\n        <x-dropdown align=\"right\" width=\"48\">\n            <x-slot name=\"trigger\">\n                <span class=\"inline-flex rounded-md\">\n                    <button\n                        type=\"button\"\n                        class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none focus:bg-gray-50 active:bg-gray-50 transition ease-in-out duration-150\"\n                    >\n                        {{ __('navigation.apps') }}\n\n                        <svg\n                            class=\"ml-2 -mr-0.5 h-4 w-4\"\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                        >\n                            <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\"\n                            />\n                        </svg>\n                    </button>\n                </span>\n            </x-slot>\n\n            <x-slot name=\"content\">\n                @can('view-any', App\\Models\\User::class)\n                <x-dropdown-link\n                    wire:navigate\n                    href=\"{{ route('dashboard.users.index') }}\"\n                >\n                    {{ __('navigation.users') }}\n                </x-dropdown-link>\n                @endcan\n            </x-slot>\n        </x-dropdown>\n    </div>\n</div>\n"}, "item_46": {"path": "resources/views/partials", "name": "app-responsive-menu.blade.php", "fullPath": "resources/views/partials/app-responsive-menu.blade.php", "template": "menu/AppResponsiveMenu.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 46, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "@can('view-any', App\\Models\\User::class)\n<x-responsive-nav-link\n    href=\"{{ route('dashboard.users.index') }}\"\n    :active=\"request()->routeIs('dashboard.users.index')\"\n>\n    {{ __('navigation.users') }}\n</x-responsive-nav-link>\n@endcan\n"}, "item_47": {"path": "database/factories", "name": "MembershipFactory.php", "fullPath": "database/factories/MembershipFactory.php", "template": "database/Factory.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 47, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace Database\\Factories;\n\nuse App\\Models\\Membership;\nuse Illuminate\\Support\\Str;\nuse Illuminate\\Database\\Eloquent\\Factories\\Factory;\n\nclass MembershipFactory extends Factory\n{\n    /**\n     * The name of the factory's corresponding model.\n     *\n     * @var string\n     */\n    protected $model = Membership::class;\n\n    /**\n     * Define the model's default state.\n     *\n     * @return array<string, mixed>\n     */\n    public function definition(): array\n    {\n        return [\n            'team_id' => fake()->randomNumber(),\n            'user_id' => fake()->randomNumber(),\n            'role' => fake()->text(255),\n        ];\n    }\n}\n"}, "item_48": {"path": "database/seeders", "name": "MembershipSeeder.php", "fullPath": "database/seeders/MembershipSeeder.php", "template": "database/Seeder.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 48, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace Database\\Seeders;\n\nuse App\\Models\\Membership;\nuse Illuminate\\Database\\Seeder;\n\nclass MembershipSeeder extends Seeder\n{\n    /**\n     * Run the database seeds.\n     */\n    public function run(): void\n    {\n        Membership::factory()\n            ->count(5)\n            ->create();\n    }\n}\n"}, "item_49": {"path": "app/Policies", "name": "MembershipPolicy.php", "fullPath": "app/Policies/MembershipPolicy.php", "template": "models/Policy.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 49, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Policies;\n\nuse App\\Models\\User;\nuse App\\Models\\Membership;\n\nclass MembershipPolicy\n{\n    /**\n     * Determine whether the user can view any models.\n     */\n    public function viewAny(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can view the model.\n     */\n    public function view(User $user, Membership $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can create models.\n     */\n    public function create(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can update the model.\n     */\n    public function update(User $user, Membership $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can delete the model.\n     */\n    public function delete(User $user, Membership $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can restore the model.\n     */\n    public function restore(User $user, Membership $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can permanently delete the model.\n     */\n    public function forceDelete(User $user, Membership $model): bool\n    {\n        return true;\n    }\n}\n"}, "item_50": {"path": "database/factories", "name": "TeamFactory.php", "fullPath": "database/factories/TeamFactory.php", "template": "database/Factory.vemtl", "projectId": 1, "type": "php", "status": "conflict", "ignoreConflicts": false, "id": 50, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace Database\\Factories;\n\nuse App\\Models\\Team;\nuse Illuminate\\Support\\Str;\nuse Illuminate\\Database\\Eloquent\\Factories\\Factory;\n\nclass TeamFactory extends Factory\n{\n    /**\n     * The name of the factory's corresponding model.\n     *\n     * @var string\n     */\n    protected $model = Team::class;\n\n    /**\n     * Define the model's default state.\n     *\n     * @return array<string, mixed>\n     */\n    public function definition(): array\n    {\n        return [\n            'user_id' => fake()->randomNumber(),\n            'name' => fake()->name(),\n            'personal_team' => fake()->boolean(),\n        ];\n    }\n}\n", "conflictFileName": "ldokeNUDhFBxTuG38npxryh0AuZStjiQ.json"}, "item_51": {"path": "database/seeders", "name": "TeamSeeder.php", "fullPath": "database/seeders/TeamSeeder.php", "template": "database/Seeder.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 51, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace Database\\Seeders;\n\nuse App\\Models\\Team;\nuse Illuminate\\Database\\Seeder;\n\nclass TeamSeeder extends Seeder\n{\n    /**\n     * Run the database seeds.\n     */\n    public function run(): void\n    {\n        Team::factory()\n            ->count(5)\n            ->create();\n    }\n}\n"}, "item_52": {"path": "app/Policies", "name": "TeamPolicy.php", "fullPath": "app/Policies/TeamPolicy.php", "template": "models/Policy.vemtl", "projectId": 1, "type": "php", "status": "conflict", "ignoreConflicts": false, "id": 52, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Policies;\n\nuse App\\Models\\Team;\nuse App\\Models\\User;\n\nclass TeamPolicy\n{\n    /**\n     * Determine whether the user can view any models.\n     */\n    public function viewAny(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can view the model.\n     */\n    public function view(User $user, Team $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can create models.\n     */\n    public function create(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can update the model.\n     */\n    public function update(User $user, Team $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can delete the model.\n     */\n    public function delete(User $user, Team $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can restore the model.\n     */\n    public function restore(User $user, Team $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can permanently delete the model.\n     */\n    public function forceDelete(User $user, Team $model): bool\n    {\n        return true;\n    }\n}\n", "conflictFileName": "hzCyhEAQpOisqQA84sYxLyBR7LRRJOaG.json"}, "item_53": {"path": "database/factories", "name": "TeamInvitationFactory.php", "fullPath": "database/factories/TeamInvitationFactory.php", "template": "database/Factory.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 53, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace Database\\Factories;\n\nuse Illuminate\\Support\\Str;\nuse App\\Models\\TeamInvitation;\nuse Illuminate\\Database\\Eloquent\\Factories\\Factory;\n\nclass TeamInvitationFactory extends Factory\n{\n    /**\n     * The name of the factory's corresponding model.\n     *\n     * @var string\n     */\n    protected $model = TeamInvitation::class;\n\n    /**\n     * Define the model's default state.\n     *\n     * @return array<string, mixed>\n     */\n    public function definition(): array\n    {\n        return [\n            'email' => fake()\n                ->unique()\n                ->safeEmail(),\n            'role' => fake()->text(255),\n            'team_id' => \\App\\Models\\Team::factory(),\n        ];\n    }\n}\n"}, "item_54": {"path": "database/seeders", "name": "TeamInvitationSeeder.php", "fullPath": "database/seeders/TeamInvitationSeeder.php", "template": "database/Seeder.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 54, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace Database\\Seeders;\n\nuse App\\Models\\TeamInvitation;\nuse Illuminate\\Database\\Seeder;\n\nclass TeamInvitationSeeder extends Seeder\n{\n    /**\n     * Run the database seeds.\n     */\n    public function run(): void\n    {\n        TeamInvitation::factory()\n            ->count(5)\n            ->create();\n    }\n}\n"}, "item_55": {"path": "app/Policies", "name": "TeamInvitationPolicy.php", "fullPath": "app/Policies/TeamInvitationPolicy.php", "template": "models/Policy.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 55, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Policies;\n\nuse App\\Models\\User;\nuse App\\Models\\TeamInvitation;\n\nclass TeamInvitationPolicy\n{\n    /**\n     * Determine whether the user can view any models.\n     */\n    public function viewAny(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can view the model.\n     */\n    public function view(User $user, TeamInvitation $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can create models.\n     */\n    public function create(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can update the model.\n     */\n    public function update(User $user, TeamInvitation $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can delete the model.\n     */\n    public function delete(User $user, TeamInvitation $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can restore the model.\n     */\n    public function restore(User $user, TeamInvitation $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can permanently delete the model.\n     */\n    public function forceDelete(User $user, TeamInvitation $model): bool\n    {\n        return true;\n    }\n}\n"}, "item_56": {"path": "database/factories", "name": "UserFactory.php", "fullPath": "database/factories/UserFactory.php", "template": "database/Factory.vemtl", "projectId": 1, "type": "php", "status": "conflict", "ignoreConflicts": false, "id": 56, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace Database\\Factories;\n\nuse App\\Models\\User;\nuse Illuminate\\Support\\Str;\nuse Illuminate\\Database\\Eloquent\\Factories\\Factory;\n\nclass UserFactory extends Factory\n{\n    /**\n     * The name of the factory's corresponding model.\n     *\n     * @var string\n     */\n    protected $model = User::class;\n\n    /**\n     * Define the model's default state.\n     *\n     * @return array<string, mixed>\n     */\n    public function definition(): array\n    {\n        return [\n            'name' => fake()->name(),\n            'email' => fake()\n                ->unique()\n                ->safeEmail(),\n            'email_verified_at' => now(),\n            'password' => \\Hash::make('password'),\n            'two_factor_secret' => fake()->text(),\n            'two_factor_recovery_codes' => fake()->text(),\n            'remember_token' => \\Str::random(10),\n            'created_by' => fake()->word(),\n            'updated_by' => fake()->word(),\n        ];\n    }\n\n    /**\n     * Indicate that the model's email address should be unverified.\n     */\n    public function unverified(): Factory\n    {\n        return $this->state(function (array $attributes) {\n            return [\n                'email_verified_at' => null,\n            ];\n        });\n    }\n}\n", "conflictFileName": "FWI0VROcuRHYwDg1F2SfWPB71Iv9cQgH.json"}, "item_57": {"path": "database/seeders", "name": "UserSeeder.php", "fullPath": "database/seeders/UserSeeder.php", "template": "database/Seeder.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 57, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace Database\\Seeders;\n\nuse App\\Models\\User;\nuse Illuminate\\Database\\Seeder;\n\nclass UserSeeder extends Seeder\n{\n    /**\n     * Run the database seeds.\n     */\n    public function run(): void\n    {\n        User::factory()\n            ->count(5)\n            ->create();\n    }\n}\n"}, "item_58": {"path": "app/Policies", "name": "UserPolicy.php", "fullPath": "app/Policies/UserPolicy.php", "template": "models/Policy.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 58, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Policies;\n\nuse App\\Models\\User;\n\nclass UserPolicy\n{\n    /**\n     * Determine whether the user can view any models.\n     */\n    public function viewAny(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can view the model.\n     */\n    public function view(User $user, User $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can create models.\n     */\n    public function create(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can update the model.\n     */\n    public function update(User $user, User $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can delete the model.\n     */\n    public function delete(User $user, User $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can restore the model.\n     */\n    public function restore(User $user, User $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can permanently delete the model.\n     */\n    public function forceDelete(User $user, User $model): bool\n    {\n        return true;\n    }\n}\n"}, "item_59": {"path": "resources/views/livewire/dashboard/users", "name": "index.blade.php", "fullPath": "resources/views/livewire/dashboard/users/index.blade.php", "template": "crud/views/livewire/IndexView.vemtl", "projectId": 1, "type": "blade", "status": "rendered", "ignoreConflicts": false, "id": 59, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<div class=\"max-w-7xl mx-auto py-10 sm:px-6 lg:px-8 space-y-4\">\n    <x-ui.breadcrumbs>\n        <x-ui.breadcrumbs.link href=\"/dashboard\"\n            >Dashboard</x-ui.breadcrumbs.link\n        >\n        <x-ui.breadcrumbs.separator />\n        <x-ui.breadcrumbs.link active\n            >{{ __('crud.users.collectionTitle') }}</x-ui.breadcrumbs.link\n        >\n    </x-ui.breadcrumbs>\n\n    <div class=\"flex justify-between align-top py-4\">\n        <x-ui.input\n            wire:model.live=\"search\"\n            type=\"text\"\n            placeholder=\"Search {{ __('crud.users.collectionTitle') }}...\"\n        />\n\n        @can('create', App\\Models\\User::class)\n        <a wire:navigate href=\"{{ route('dashboard.users.create') }}\">\n            <x-ui.button>New</x-ui.button>\n        </a>\n        @endcan\n    </div>\n\n    {{-- Delete Modal --}}\n    <x-ui.modal.confirm wire:model=\"confirmingDeletion\">\n        <x-slot name=\"title\"> {{ __('Delete') }} </x-slot>\n\n        <x-slot name=\"content\"> {{ __('Are you sure?') }} </x-slot>\n\n        <x-slot name=\"footer\">\n            <x-ui.button\n                wire:click=\"$toggle('confirmingDeletion')\"\n                wire:loading.attr=\"disabled\"\n            >\n                {{ __('Cancel') }}\n            </x-ui.button>\n\n            <x-ui.button.danger\n                class=\"ml-3\"\n                wire:click=\"delete({{ $deletingUser }})\"\n                wire:loading.attr=\"disabled\"\n            >\n                {{ __('Delete') }}\n            </x-ui.button.danger>\n        </x-slot>\n    </x-ui.modal.confirm>\n\n    {{-- Index Table --}}\n    <x-ui.container.table>\n        <x-ui.table>\n            <x-slot name=\"head\">\n                <x-ui.table.header for-crud wire:click=\"sortBy('name')\"\n                    >{{ __('crud.users.inputs.name.label') }}</x-ui.table.header\n                >\n                <x-ui.table.header for-crud wire:click=\"sortBy('email')\"\n                    >{{ __('crud.users.inputs.email.label')\n                    }}</x-ui.table.header\n                >\n                <x-ui.table.header for-crud wire:click=\"sortBy('created_by')\"\n                    >{{ __('crud.users.inputs.created_by.label')\n                    }}</x-ui.table.header\n                >\n                <x-ui.table.header for-crud wire:click=\"sortBy('updated_by')\"\n                    >{{ __('crud.users.inputs.updated_by.label')\n                    }}</x-ui.table.header\n                >\n                <x-ui.table.action-header>Actions</x-ui.table.action-header>\n            </x-slot>\n\n            <x-slot name=\"body\">\n                @forelse ($users as $user)\n                <x-ui.table.row wire:loading.class.delay=\"opacity-75\">\n                    <x-ui.table.column for-crud\n                        >{{ $user->name }}</x-ui.table.column\n                    >\n                    <x-ui.table.column for-crud\n                        >{{ $user->email }}</x-ui.table.column\n                    >\n                    <x-ui.table.column for-crud\n                        >{{ $user->created_by }}</x-ui.table.column\n                    >\n                    <x-ui.table.column for-crud\n                        >{{ $user->updated_by }}</x-ui.table.column\n                    >\n                    <x-ui.table.action-column>\n                        @can('update', $user)\n                        <x-ui.action\n                            wire:navigate\n                            href=\"{{ route('dashboard.users.edit', $user) }}\"\n                            >Edit</x-ui.action\n                        >\n                        @endcan @can('delete', $user)\n                        <x-ui.action.danger\n                            wire:click=\"confirmDeletion({{ $user->id }})\"\n                            >Delete</x-ui.action.danger\n                        >\n                        @endcan\n                    </x-ui.table.action-column>\n                </x-ui.table.row>\n                @empty\n                <x-ui.table.row>\n                    <x-ui.table.column colspan=\"5\"\n                        >No {{ __('crud.users.collectionTitle') }} found.</x-ui.table.column\n                    >\n                </x-ui.table.row>\n                @endforelse\n            </x-slot>\n        </x-ui.table>\n\n        <div class=\"mt-2\">{{ $users->links() }}</div>\n    </x-ui.container.table>\n</div>\n"}, "item_60": {"path": "app/Livewire/Dashboard", "name": "UserIndex.php", "fullPath": "app/Livewire/Dashboard/UserIndex.php", "template": "crud/views/livewire/IndexComponent.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 60, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Livewire\\Dashboard;\n\nuse App\\Models\\User;\nuse Livewire\\Component;\nuse Livewire\\WithPagination;\n\nclass UserIndex extends Component\n{\n    use WithPagination;\n\n    public $search;\n    public $sortField = 'updated_at';\n    public $sortDirection = 'desc';\n\n    public $queryString = ['search', 'sortField', 'sortDirection'];\n\n    public $confirmingDeletion = false;\n    public $deletingUser;\n\n    public function updatingSearch()\n    {\n        $this->resetPage();\n    }\n\n    public function confirmDeletion(string $id)\n    {\n        $this->deletingUser = $id;\n\n        $this->confirmingDeletion = true;\n    }\n\n    public function delete(User $user)\n    {\n        $user->delete();\n\n        $this->confirmingDeletion = false;\n    }\n\n    public function sortBy($field)\n    {\n        if ($this->sortField === $field) {\n            $this->sortDirection =\n                $this->sortDirection === 'asc' ? 'desc' : 'asc';\n        } else {\n            $this->sortDirection = 'asc';\n        }\n\n        $this->sortField = $field;\n    }\n\n    public function getRowsProperty()\n    {\n        return $this->rowsQuery->paginate(5);\n    }\n\n    public function getRowsQueryProperty()\n    {\n        return User::query()\n            ->orderBy($this->sortField, $this->sortDirection)\n            ->where('name', 'like', \"%{$this->search}%\");\n    }\n\n    public function render()\n    {\n        return view('livewire.dashboard.users.index', [\n            'users' => $this->rows,\n        ]);\n    }\n}\n"}, "item_61": {"path": "resources/views/livewire/dashboard/users", "name": "create.blade.php", "fullPath": "resources/views/livewire/dashboard/users/create.blade.php", "template": "crud/views/livewire/CreateView.vemtl", "projectId": 1, "type": "blade", "status": "rendered", "ignoreConflicts": false, "id": 61, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<div class=\"max-w-7xl mx-auto py-10 sm:px-6 lg:px-8 space-y-4\">\n    <x-ui.breadcrumbs>\n        <x-ui.breadcrumbs.link href=\"/dashboard\"\n            >Dashboard</x-ui.breadcrumbs.link\n        >\n        <x-ui.breadcrumbs.separator />\n        <x-ui.breadcrumbs.link href=\"{{ route('dashboard.users.index') }}\"\n            >{{ __('crud.users.collectionTitle') }}</x-ui.breadcrumbs.link\n        >\n        <x-ui.breadcrumbs.separator />\n        <x-ui.breadcrumbs.link active\n            >Create {{ __('crud.users.itemTitle') }}</x-ui.breadcrumbs.link\n        >\n    </x-ui.breadcrumbs>\n\n    <div class=\"w-full text-gray-500 text-lg font-semibold py-4 uppercase\">\n        <h1>Create {{ __('crud.users.itemTitle') }}</h1>\n    </div>\n\n    <div class=\"overflow-hidden border rounded-lg bg-white\">\n        <form class=\"w-full mb-0\" wire:submit.prevent=\"save\">\n            <div class=\"p-6 space-y-3\">\n                <div class=\"w-full\">\n                    <x-ui.label for=\"name\"\n                        >{{ __('crud.users.inputs.name.label') }}</x-ui.label\n                    >\n                    <x-ui.input.text\n                        class=\"w-full\"\n                        wire:model=\"form.name\"\n                        name=\"name\"\n                        id=\"name\"\n                        placeholder=\"{{ __('crud.users.inputs.name.placeholder') }}\"\n                    />\n                    <x-ui.input.error for=\"form.name\" />\n                </div>\n\n                <div class=\"w-full\">\n                    <x-ui.label for=\"email\"\n                        >{{ __('crud.users.inputs.email.label') }}</x-ui.label\n                    >\n                    <x-ui.input.email\n                        class=\"w-full\"\n                        wire:model=\"form.email\"\n                        name=\"email\"\n                        id=\"email\"\n                        placeholder=\"{{ __('crud.users.inputs.email.placeholder') }}\"\n                    />\n                    <x-ui.input.error for=\"form.email\" />\n                </div>\n\n                <div class=\"w-full\">\n                    <x-ui.label for=\"password\"\n                        >{{ __('crud.users.inputs.password.label')\n                        }}</x-ui.label\n                    >\n                    <x-ui.input.password\n                        class=\"w-full\"\n                        wire:model=\"form.password\"\n                        name=\"password\"\n                        id=\"password\"\n                        placeholder=\"{{ __('crud.users.inputs.password.placeholder') }}\"\n                    />\n                    <x-ui.input.error for=\"form.password\" />\n                </div>\n\n                <div class=\"w-full\">\n                    <x-ui.label for=\"created_by\"\n                        >{{ __('crud.users.inputs.created_by.label')\n                        }}</x-ui.label\n                    >\n                    <x-ui.input.number\n                        class=\"w-full\"\n                        wire:model=\"form.created_by\"\n                        name=\"created_by\"\n                        id=\"created_by\"\n                        placeholder=\"{{ __('crud.users.inputs.created_by.placeholder') }}\"\n                        step=\"1\"\n                    />\n                    <x-ui.input.error for=\"form.created_by\" />\n                </div>\n\n                <div class=\"w-full\">\n                    <x-ui.label for=\"updated_by\"\n                        >{{ __('crud.users.inputs.updated_by.label')\n                        }}</x-ui.label\n                    >\n                    <x-ui.input.number\n                        class=\"w-full\"\n                        wire:model=\"form.updated_by\"\n                        name=\"updated_by\"\n                        id=\"updated_by\"\n                        placeholder=\"{{ __('crud.users.inputs.updated_by.placeholder') }}\"\n                        step=\"1\"\n                    />\n                    <x-ui.input.error for=\"form.updated_by\" />\n                </div>\n            </div>\n\n            <div class=\"flex justify-between mt-4 border-t border-gray-50 p-4\">\n                <div>\n                    <!-- Other buttons here -->\n                </div>\n                <div>\n                    <x-ui.button type=\"submit\">Save</x-ui.button>\n                </div>\n            </div>\n        </form>\n    </div>\n</div>\n"}, "item_62": {"path": "app/Livewire/Dashboard", "name": "UserCreate.php", "fullPath": "app/Livewire/Dashboard/UserCreate.php", "template": "crud/views/livewire/CreateComponent.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 62, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Livewire\\Dashboard;\n\nuse Livewire\\Component;\nuse Livewire\\WithFileUploads;\nuse Illuminate\\Support\\Collection;\nuse App\\Livewire\\Dashboard\\Users\\Forms\\CreateForm;\n\nclass UserCreate extends Component\n{\n    use WithFileUploads;\n\n    public CreateForm $form;\n\n    public function mount()\n    {\n    }\n\n    public function save()\n    {\n        $this->authorize('create', User::class);\n\n        $this->validate();\n\n        $user = $this->form->save();\n\n        return redirect()->route('dashboard.users.edit', $user);\n    }\n\n    public function render()\n    {\n        return view('livewire.dashboard.users.create', []);\n    }\n}\n"}, "item_63": {"path": "resources/views/livewire/dashboard/users", "name": "edit.blade.php", "fullPath": "resources/views/livewire/dashboard/users/edit.blade.php", "template": "crud/views/livewire/EditView.vemtl", "projectId": 1, "type": "blade", "status": "rendered", "ignoreConflicts": false, "id": 63, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<div class=\"max-w-7xl mx-auto py-10 sm:px-6 lg:px-8 space-y-4\">\n    <x-ui.breadcrumbs>\n        <x-ui.breadcrumbs.link href=\"/dashboard\"\n            >Dashboard</x-ui.breadcrumbs.link\n        >\n        <x-ui.breadcrumbs.separator />\n        <x-ui.breadcrumbs.link href=\"{{ route('dashboard.users.index') }}\"\n            >{{ __('crud.users.collectionTitle') }}</x-ui.breadcrumbs.link\n        >\n        <x-ui.breadcrumbs.separator />\n        <x-ui.breadcrumbs.link active\n            >Edit {{ __('crud.users.itemTitle') }}</x-ui.breadcrumbs.link\n        >\n    </x-ui.breadcrumbs>\n\n    <x-ui.toast on=\"saved\"> User saved successfully. </x-ui.toast>\n\n    <div class=\"w-full text-gray-500 text-lg font-semibold py-4 uppercase\">\n        <h1>Edit {{ __('crud.users.itemTitle') }}</h1>\n    </div>\n\n    <div class=\"overflow-hidden border rounded-lg bg-white\">\n        <form class=\"w-full mb-0\" wire:submit.prevent=\"save\">\n            <div class=\"p-6 space-y-3\">\n                <div class=\"w-full\">\n                    <x-ui.label for=\"name\"\n                        >{{ __('crud.users.inputs.name.label') }}</x-ui.label\n                    >\n                    <x-ui.input.text\n                        class=\"w-full\"\n                        wire:model=\"form.name\"\n                        name=\"name\"\n                        id=\"name\"\n                        placeholder=\"{{ __('crud.users.inputs.name.placeholder') }}\"\n                    />\n                    <x-ui.input.error for=\"form.name\" />\n                </div>\n\n                <div class=\"w-full\">\n                    <x-ui.label for=\"email\"\n                        >{{ __('crud.users.inputs.email.label') }}</x-ui.label\n                    >\n                    <x-ui.input.email\n                        class=\"w-full\"\n                        wire:model=\"form.email\"\n                        name=\"email\"\n                        id=\"email\"\n                        placeholder=\"{{ __('crud.users.inputs.email.placeholder') }}\"\n                    />\n                    <x-ui.input.error for=\"form.email\" />\n                </div>\n\n                <div class=\"w-full\">\n                    <x-ui.label for=\"password\"\n                        >{{ __('crud.users.inputs.password.label')\n                        }}</x-ui.label\n                    >\n                    <x-ui.input.password\n                        class=\"w-full\"\n                        wire:model=\"form.password\"\n                        name=\"password\"\n                        id=\"password\"\n                        placeholder=\"{{ __('crud.users.inputs.password.placeholder') }}\"\n                    />\n                    <x-ui.input.error for=\"form.password\" />\n                </div>\n\n                <div class=\"w-full\">\n                    <x-ui.label for=\"created_by\"\n                        >{{ __('crud.users.inputs.created_by.label')\n                        }}</x-ui.label\n                    >\n                    <x-ui.input.number\n                        class=\"w-full\"\n                        wire:model=\"form.created_by\"\n                        name=\"created_by\"\n                        id=\"created_by\"\n                        placeholder=\"{{ __('crud.users.inputs.created_by.placeholder') }}\"\n                        step=\"1\"\n                    />\n                    <x-ui.input.error for=\"form.created_by\" />\n                </div>\n\n                <div class=\"w-full\">\n                    <x-ui.label for=\"updated_by\"\n                        >{{ __('crud.users.inputs.updated_by.label')\n                        }}</x-ui.label\n                    >\n                    <x-ui.input.number\n                        class=\"w-full\"\n                        wire:model=\"form.updated_by\"\n                        name=\"updated_by\"\n                        id=\"updated_by\"\n                        placeholder=\"{{ __('crud.users.inputs.updated_by.placeholder') }}\"\n                        step=\"1\"\n                    />\n                    <x-ui.input.error for=\"form.updated_by\" />\n                </div>\n            </div>\n\n            <div class=\"flex justify-between mt-4 border-t border-gray-50 p-4\">\n                <div>\n                    <!-- Other buttons here -->\n                </div>\n                <div>\n                    <x-ui.button type=\"submit\">Save</x-ui.button>\n                </div>\n            </div>\n        </form>\n    </div>\n</div>\n"}, "item_64": {"path": "app/Livewire/Dashboard", "name": "UserEdit.php", "fullPath": "app/Livewire/Dashboard/UserEdit.php", "template": "crud/views/livewire/EditComponent.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 64, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Livewire\\Dashboard;\n\nuse App\\Models\\User;\nuse Livewire\\Component;\nuse Illuminate\\Support\\Collection;\nuse App\\Livewire\\Dashboard\\Users\\Forms\\UpdateForm;\n\nclass UserEdit extends Component\n{\n    public ?User $user = null;\n\n    public UpdateForm $form;\n\n    public function mount(User $user)\n    {\n        $this->authorize('view-any', User::class);\n\n        $this->user = $user;\n\n        $this->form->setUser($user);\n    }\n\n    public function save()\n    {\n        $this->authorize('update', $this->user);\n\n        $this->validate();\n\n        $this->form->save();\n\n        $this->dispatch('saved');\n    }\n\n    public function render()\n    {\n        return view('livewire.dashboard.users.edit', []);\n    }\n}\n"}, "item_65": {"path": "app/Livewire/Dashboard/Users/<USER>", "name": "CreateForm.php", "fullPath": "app/Livewire/Dashboard/Users/<USER>/CreateForm.php", "template": "crud/views/livewire/CreateFormComponent.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 65, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Livewire\\Dashboard\\Users\\Forms;\n\nuse Livewire\\Form;\nuse App\\Models\\User;\nuse Livewire\\Attributes\\Rule;\nuse Illuminate\\Support\\Facades\\Hash;\n\nclass CreateForm extends Form\n{\n    #[Rule('required|string')]\n    public $name = '';\n\n    #[Rule('required|string|unique:users,email')]\n    public $email = '';\n\n    #[Rule('required|string|min:6')]\n    public $password = '';\n\n    #[Rule('nullable')]\n    public $created_by = '';\n\n    #[Rule('nullable')]\n    public $updated_by = '';\n\n    public function save()\n    {\n        $this->validate();\n\n        $this->password = Hash::make($this->password);\n\n        $user = User::create($this->except([]));\n\n        $this->reset();\n\n        return $user;\n    }\n}\n"}, "item_66": {"path": "app/Livewire/Dashboard/Users/<USER>", "name": "UpdateForm.php", "fullPath": "app/Livewire/Dashboard/Users/<USER>/UpdateForm.php", "template": "crud/views/livewire/UpdateFormComponent.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 66, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Livewire\\Dashboard\\Users\\Forms;\n\nuse Livewire\\Form;\nuse App\\Models\\User;\nuse Illuminate\\Validation\\Rule;\nuse Illuminate\\Support\\Facades\\Hash;\n\nclass UpdateForm extends Form\n{\n    public ?User $user;\n\n    public $name = '';\n\n    public $email = '';\n\n    public $password = '';\n\n    public $created_by = '';\n\n    public $updated_by = '';\n\n    public function rules(): array\n    {\n        return [\n            'name' => ['required', 'string'],\n            'email' => [\n                'required',\n                'string',\n                Rule::unique('users', 'email')->ignore($this->user),\n            ],\n            'password' => ['nullable', 'string', 'min:6'],\n            'created_by' => ['nullable'],\n            'updated_by' => ['nullable'],\n        ];\n    }\n\n    public function setUser(User $user)\n    {\n        $this->user = $user;\n\n        $this->name = $user->name;\n        $this->email = $user->email;\n        $this->created_by = $user->created_by;\n        $this->updated_by = $user->updated_by;\n    }\n\n    public function save()\n    {\n        $this->validate();\n\n        $this->password = Hash::make($this->password);\n\n        $this->user->update($this->except(['user']));\n    }\n}\n"}, "item_67": {"path": "app/Http/Controllers/Api", "name": "AuthController.php", "fullPath": "app/Http/Controllers/Api/AuthController.php", "template": "api/ApiAuthController.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 67, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Http\\Controllers\\Api;\n\nuse App\\Models\\User;\nuse Illuminate\\Http\\Request;\nuse Illuminate\\Http\\JsonResponse;\nuse App\\Http\\Controllers\\Controller;\nuse Illuminate\\Validation\\ValidationException;\n\nclass AuthController extends Controller\n{\n    public function login(Request $request): JsonResponse\n    {\n        $credentials = $request->validate([\n            'email' => 'required|email',\n            'password' => 'required',\n        ]);\n\n        if (!auth()->attempt($credentials)) {\n            throw ValidationException::withMessages([\n                'email' => [trans('auth.failed')],\n            ]);\n        }\n\n        $user = User::whereEmail($request->email)->firstOrFail();\n\n        $token = $user->createToken('auth-token');\n\n        return response()->json([\n            'token' => $token->plainTextToken,\n        ]);\n    }\n}\n"}, "item_68": {"path": "app/Http/Requests", "name": "UserStoreRequest.php", "fullPath": "app/Http/Requests/UserStoreRequest.php", "template": "api/ApiStoreRequest.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 68, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Http\\Requests;\n\nuse Illuminate\\Validation\\Rule;\nuse Illuminate\\Foundation\\Http\\FormRequest;\n\nclass UserStoreRequest extends FormRequest\n{\n    /**\n     * Determine if the user is authorized to make this request.\n     */\n    public function authorize(): bool\n    {\n        return true;\n    }\n\n    /**\n     * Get the validation rules that apply to the request.\n     */\n    public function rules(): array\n    {\n        return [\n            'name' => ['required', 'string'],\n            'email' => ['required', 'string', Rule::unique('users', 'email')],\n            'password' => ['required', 'string', 'min:6'],\n            'created_by' => ['nullable'],\n            'updated_by' => ['nullable'],\n        ];\n    }\n}\n"}, "item_69": {"path": "app/Http/Requests", "name": "UserUpdateRequest.php", "fullPath": "app/Http/Requests/UserUpdateRequest.php", "template": "api/ApiUpdateRequest.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 69, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Http\\Requests;\n\nuse Illuminate\\Validation\\Rule;\nuse Illuminate\\Foundation\\Http\\FormRequest;\n\nclass UserUpdateRequest extends FormRequest\n{\n    /**\n     * Determine if the user is authorized to make this request.\n     */\n    public function authorize(): bool\n    {\n        return true;\n    }\n\n    /**\n     * Get the validation rules that apply to the request.\n     */\n    public function rules(): array\n    {\n        return [\n            'name' => ['required', 'string'],\n            'email' => [\n                'required',\n                'string',\n                Rule::unique('users', 'email')->ignore($this->user),\n            ],\n            'password' => ['nullable', 'string', 'min:6'],\n            'created_by' => ['nullable'],\n            'updated_by' => ['nullable'],\n        ];\n    }\n}\n"}, "item_70": {"path": "app/Http/Resources", "name": "UserCollection.php", "fullPath": "app/Http/Resources/UserCollection.php", "template": "api/ApiCollection.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 70, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Http\\Resources;\n\nuse Illuminate\\Http\\Request;\nuse Illuminate\\Http\\Resources\\Json\\ResourceCollection;\n\nclass UserCollection extends ResourceCollection\n{\n    /**\n     * Transform the resource collection into an array.\n     */\n    public function toArray(Request $request): array\n    {\n        return parent::toArray($request);\n    }\n}\n"}, "item_71": {"path": "app/Http/Resources", "name": "UserResource.php", "fullPath": "app/Http/Resources/UserResource.php", "template": "api/ApiResource.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 71, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Http\\Resources;\n\nuse Illuminate\\Http\\Request;\nuse Illuminate\\Http\\Resources\\Json\\JsonResource;\n\nclass UserResource extends JsonResource\n{\n    /**\n     * Transform the resource into an array.\n     */\n    public function toArray(Request $request): array\n    {\n        return parent::toArray($request);\n    }\n}\n"}, "item_72": {"path": "app/Http/Controllers/Api", "name": "UserController.php", "fullPath": "app/Http/Controllers/Api/UserController.php", "template": "api/ApiController.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 72, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Http\\Controllers\\Api;\n\nuse App\\Models\\User;\nuse Illuminate\\Http\\Request;\nuse Illuminate\\Http\\Response;\nuse Illuminate\\Validation\\Rule;\nuse App\\Http\\Resources\\UserResource;\nuse App\\Http\\Controllers\\Controller;\nuse Illuminate\\Support\\Facades\\Hash;\nuse App\\Http\\Resources\\UserCollection;\nuse App\\Http\\Requests\\UserStoreRequest;\nuse App\\Http\\Requests\\UserUpdateRequest;\n\nclass UserController extends Controller\n{\n    public function index(Request $request): UserCollection\n    {\n        $search = $request->get('search', '');\n\n        $users = $this->getSearchQuery($search)\n            ->latest()\n            ->paginate();\n\n        return new UserCollection($users);\n    }\n\n    public function store(UserStoreRequest $request): UserResource\n    {\n        $validated = $request->validated();\n\n        $validated['password'] = Hash::make($validated['password']);\n\n        $user = User::create($validated);\n\n        return new UserResource($user);\n    }\n\n    public function show(Request $request, User $user): UserResource\n    {\n        return new UserResource($user);\n    }\n\n    public function update(UserUpdateRequest $request, User $user): UserResource\n    {\n        $validated = $request->validated();\n\n        if (empty($validated['password'])) {\n            unset($validated['password']);\n        } else {\n            $validated['password'] = Hash::make($validated['password']);\n        }\n\n        $user->update($validated);\n\n        return new UserResource($user);\n    }\n\n    public function destroy(Request $request, User $user): Response\n    {\n        $user->delete();\n\n        return response()->noContent();\n    }\n\n    public function getSearchQuery(string $search)\n    {\n        return User::query()->where('name', 'like', \"%{$search}%\");\n    }\n}\n"}, "item_73": {"path": "tests/Feature/Api", "name": "UserTest.php", "fullPath": "tests/Feature/Api/UserTest.php", "template": "api/ApiTest.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 73, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nuse App\\Models\\User;\nuse Laravel\\Sanctum\\Sanctum;\nuse Illuminate\\Foundation\\Testing\\WithFaker;\nuse Illuminate\\Foundation\\Testing\\RefreshDatabase;\n\nuses(RefreshDatabase::class, WithFaker::class);\n\nbeforeEach(function () {\n    $this->withoutExceptionHandling();\n\n    $user = User::factory()->create(['email' => '<EMAIL>']);\n\n    Sanctum::actingAs($user, [], 'web');\n});\n\ntest('it gets users list', function () {\n    $users = User::factory()\n        ->count(5)\n        ->create();\n\n    $response = $this->get(route('api.users.index'));\n\n    $response->assertOk()->assertSee($users[0]->name);\n});\n\ntest('it stores the user', function () {\n    $data = User::factory()\n        ->make()\n        ->toArray();\n\n    $data['password'] = \\Str::random('8');\n\n    $response = $this->postJson(route('api.users.store'), $data);\n\n    unset($data['password']);\n    unset($data['email_verified_at']);\n    unset($data['two_factor_confirmed_at']);\n    unset($data['current_team_id']);\n    unset($data['profile_photo_path']);\n    unset($data['created_by']);\n    unset($data['updated_by']);\n    unset($data['created_at']);\n    unset($data['updated_at']);\n    unset($data['profile_photo_url']);\n\n    $this->assertDatabaseHas('users', $data);\n\n    $response->assertStatus(201)->assertJsonFragment($data);\n});\n\ntest('it updates the user', function () {\n    $user = User::factory()->create();\n\n    $data = [\n        'name' => fake()->name(),\n        'email' => fake()\n            ->unique()\n            ->safeEmail(),\n    ];\n\n    $data['password'] = \\Str::random('8');\n\n    $response = $this->putJson(route('api.users.update', $user), $data);\n\n    unset($data['password']);\n    unset($data['email_verified_at']);\n    unset($data['two_factor_confirmed_at']);\n    unset($data['current_team_id']);\n    unset($data['profile_photo_path']);\n    unset($data['created_by']);\n    unset($data['updated_by']);\n    unset($data['created_at']);\n    unset($data['updated_at']);\n    unset($data['profile_photo_url']);\n\n    $data['id'] = $user->id;\n\n    $this->assertDatabaseHas('users', $data);\n\n    $response->assertStatus(200)->assertJsonFragment($data);\n});\n\ntest('it deletes the user', function () {\n    $user = User::factory()->create();\n\n    $response = $this->deleteJson(route('api.users.destroy', $user));\n\n    $this->assertModelMissing($user);\n\n    $response->assertNoContent();\n});\n"}, "item_74": {"path": "app/Filament/Resources/Panel", "name": "UserResource.php", "fullPath": "app/Filament/Resources/Panel/UserResource.php", "template": "crud/views/filament/ResourceComponent.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 74, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Filament\\Resources\\Panel;\n\nuse Filament\\Forms;\nuse Filament\\Tables;\nuse App\\Models\\User;\nuse Livewire\\Component;\nuse Filament\\Forms\\Form;\nuse Filament\\Tables\\Table;\nuse Filament\\Resources\\Resource;\nuse Filament\\Forms\\Components\\Grid;\nuse Filament\\Forms\\Components\\Section;\nuse Filament\\Tables\\Columns\\TextColumn;\nuse Filament\\Forms\\Components\\TextInput;\nuse Illuminate\\Database\\Eloquent\\Builder;\nuse App\\Filament\\Resources\\Panel\\UserResource\\Pages;\nuse App\\Filament\\Resources\\Panel\\UserResource\\RelationManagers;\n\nclass UserResource extends Resource\n{\n    protected static ?string $model = User::class;\n\n    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';\n\n    protected static ?int $navigationSort = 1;\n\n    protected static ?string $navigationGroup = 'Admin';\n\n    public static function getModelLabel(): string\n    {\n        return __('crud.users.itemTitle');\n    }\n\n    public static function getPluralModelLabel(): string\n    {\n        return __('crud.users.collectionTitle');\n    }\n\n    public static function getNavigationLabel(): string\n    {\n        return __('crud.users.collectionTitle');\n    }\n\n    public static function form(Form $form): Form\n    {\n        return $form->schema([\n            Section::make()->schema([\n                Grid::make(['default' => 1])->schema([\n                    TextInput::make('name')\n                        ->required()\n                        ->string()\n                        ->autofocus(),\n\n                    TextInput::make('email')\n                        ->required()\n                        ->string()\n                        ->unique('users', 'email', ignoreRecord: true)\n                        ->email(),\n\n                    TextInput::make('password')\n                        ->required(\n                            fn(string $context): bool => $context === 'create'\n                        )\n                        ->dehydrated(fn($state) => filled($state))\n                        ->string()\n                        ->minLength(6)\n                        ->password(),\n\n                    TextInput::make('created_by')\n                        ->nullable()\n                        ->numeric()\n                        ->step(1),\n\n                    TextInput::make('updated_by')\n                        ->nullable()\n                        ->numeric()\n                        ->step(1),\n                ]),\n            ]),\n        ]);\n    }\n\n    public static function table(Table $table): Table\n    {\n        return $table\n            ->poll('60s')\n            ->columns([\n                TextColumn::make('name'),\n\n                TextColumn::make('email'),\n\n                TextColumn::make('created_by'),\n\n                TextColumn::make('updated_by'),\n            ])\n            ->filters([])\n            ->actions([\n                Tables\\Actions\\EditAction::make(),\n                Tables\\Actions\\ViewAction::make(),\n            ])\n            ->bulkActions([\n                Tables\\Actions\\BulkActionGroup::make([\n                    Tables\\Actions\\DeleteBulkAction::make(),\n                ]),\n            ])\n            ->defaultSort('id', 'desc');\n    }\n\n    public static function getRelations(): array\n    {\n        return [];\n    }\n\n    public static function getPages(): array\n    {\n        return [\n            'index' => Pages\\ListUsers::route('/'),\n            'create' => Pages\\CreateUser::route('/create'),\n            'view' => Pages\\ViewUser::route('/{record}'),\n            'edit' => Pages\\EditUser::route('/{record}/edit'),\n        ];\n    }\n}\n"}, "item_75": {"path": "app/Filament/Resources/Panel/UserResource/Pages", "name": "CreateUser.php", "fullPath": "app/Filament/Resources/Panel/UserResource/Pages/CreateUser.php", "template": "crud/views/filament/CreateComponent.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 75, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Filament\\Resources\\Panel\\UserResource\\Pages;\n\nuse Filament\\Actions;\nuse Filament\\Resources\\Pages\\CreateRecord;\nuse App\\Filament\\Resources\\Panel\\UserResource;\n\nclass CreateUser extends CreateRecord\n{\n    protected static string $resource = UserResource::class;\n}\n"}, "item_76": {"path": "app/Filament/Resources/Panel/UserResource/Pages", "name": "ViewUser.php", "fullPath": "app/Filament/Resources/Panel/UserResource/Pages/ViewUser.php", "template": "crud/views/filament/ViewComponent.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 76, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Filament\\Resources\\Panel\\UserResource\\Pages;\n\nuse Filament\\Actions;\nuse Filament\\Resources\\Pages\\ViewRecord;\nuse App\\Filament\\Resources\\Panel\\UserResource;\n\nclass ViewUser extends ViewRecord\n{\n    protected static string $resource = UserResource::class;\n\n    protected function getHeaderActions(): array\n    {\n        return [Actions\\EditAction::make()];\n    }\n}\n"}, "item_77": {"path": "app/Filament/Resources/Panel/UserResource/Pages", "name": "EditUser.php", "fullPath": "app/Filament/Resources/Panel/UserResource/Pages/EditUser.php", "template": "crud/views/filament/EditComponent.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 77, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Filament\\Resources\\Panel\\UserResource\\Pages;\n\nuse Filament\\Actions;\nuse Filament\\Resources\\Pages\\EditRecord;\nuse App\\Filament\\Resources\\Panel\\UserResource;\n\nclass EditUser extends EditRecord\n{\n    protected static string $resource = UserResource::class;\n\n    protected function getHeaderActions(): array\n    {\n        return [Actions\\DeleteAction::make()];\n    }\n}\n"}, "item_78": {"path": "app/Filament/Resources/Panel/UserResource/Pages", "name": "ListUsers.php", "fullPath": "app/Filament/Resources/Panel/UserResource/Pages/ListUsers.php", "template": "crud/views/filament/ListComponent.vemtl", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 78, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace App\\Filament\\Resources\\Panel\\UserResource\\Pages;\n\nuse Filament\\Actions;\nuse Filament\\Resources\\Pages\\ListRecords;\nuse App\\Filament\\Resources\\Panel\\UserResource;\n\nclass ListUsers extends ListRecords\n{\n    protected static string $resource = UserResource::class;\n\n    protected function getHeaderActions(): array\n    {\n        return [Actions\\CreateAction::make()];\n    }\n}\n"}, "item_79": {"path": "database/seeders", "name": "DatabaseSeeder.php", "fullPath": "database/seeders/DatabaseSeeder.php", "template": "database/DatabaseSeeder.vemtl", "projectId": 1, "type": "php", "status": "conflict", "ignoreConflicts": false, "id": 79, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nnamespace Database\\Seeders;\n\nuse Illuminate\\Database\\Seeder;\n// use Illuminate\\Database\\Console\\Seeds\\WithoutModelEvents;\n\nuse App\\Models\\User;\n\nclass DatabaseSeeder extends Seeder\n{\n    /**\n     * Seed the application's database.\n     */\n    public function run(): void\n    {\n        User::factory()\n            ->count(1)\n            ->create([\n                'email' => '<EMAIL>',\n                'password' => \\Hash::make('admin'),\n            ]);\n\n        $this->call(MembershipSeeder::class);\n        $this->call(TeamSeeder::class);\n        $this->call(TeamInvitationSeeder::class);\n    }\n}\n", "conflictFileName": "ffDFWzwI0UucxouoNGoE2Bj8kgrevDYr.json"}, "item_80": {"path": "resources/views/components/layouts", "name": "app.blade.php", "fullPath": "resources/views/components/layouts/app.blade.php", "template": "crud/views/livewire/JetstreamLayoutComponent.vemtl", "projectId": 1, "type": "html", "status": "rendered", "ignoreConflicts": false, "id": 80, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<!DOCTYPE html>\r\n<html lang=\"{{ str_replace('_', '-', app()->getLocale()) }}\">\r\n    <head>\r\n        <meta charset=\"utf-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n        <meta name=\"csrf-token\" content=\"{{ csrf_token() }}\">\r\n\r\n        <title>{{ config('app.name', 'Laravel') }}</title>\r\n\r\n        <!-- Fonts -->\r\n        <link rel=\"preconnect\" href=\"https://fonts.bunny.net\">\r\n        <link href=\"https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap\" rel=\"stylesheet\" />\r\n\r\n        <!-- Scripts -->\r\n        @vite(['resources/css/app.css', 'resources/js/app.js'])\r\n    </head>\r\n    <body class=\"font-sans antialiased\">\r\n        <x-banner />\r\n\r\n        <div class=\"min-h-screen bg-gray-100\">\r\n            @livewire('navigation-menu')\r\n\r\n            <!-- Page Heading -->\r\n            @if (isset($header))\r\n                <header class=\"bg-white shadow\">\r\n                    <div class=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\r\n                        {{ $header }}\r\n                    </div>\r\n                </header>\r\n            @endif\r\n\r\n            <!-- Page Content -->\r\n            <main>\r\n                {{ $slot }}\r\n            </main>\r\n        </div>\r\n\r\n        @stack('modals')\r\n    </body>\r\n</html>\r\n"}, "item_81": {"path": "/lang/en/", "name": "navigation.php", "fullPath": "/lang/en//navigation.php", "template": "NoTemplate", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 81, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nreturn [\n    'home' => 'Home',\n    'apps' => 'Apps',\n    'users' => 'Users',\n];\n"}, "item_82": {"path": "/lang/en/", "name": "crud.php", "fullPath": "/lang/en//crud.php", "template": "NoTemplate", "projectId": 1, "type": "php", "status": "rendered", "ignoreConflicts": false, "id": 82, "createdAt": "2025-07-09 20:33:07", "updatedAt": "2025-07-09 20:33:09", "content": "<?php\n\nreturn [\n    'users' => [\n        'itemTitle' => 'User',\n        'collectionTitle' => 'Users',\n        'inputs' => [\n            'name' => [\n                'label' => 'Name',\n                'placeholder' => 'Name',\n            ],\n            'email' => [\n                'label' => 'Email',\n                'placeholder' => 'Email',\n            ],\n            'password' => [\n                'label' => 'Password',\n                'placeholder' => 'Password',\n            ],\n            'created_by' => [\n                'label' => 'Created by',\n                'placeholder' => 'Created by',\n            ],\n            'updated_by' => [\n                'label' => 'Updated by',\n                'placeholder' => 'Updated by',\n            ],\n        ],\n        'filament' => [\n            'name' => [\n                'helper_text' => '',\n                'label' => '',\n                'description' => '',\n            ],\n            'email' => [\n                'helper_text' => '',\n                'label' => '',\n                'description' => '',\n            ],\n            'password' => [\n                'helper_text' => '',\n                'label' => '',\n                'description' => '',\n            ],\n            'created_by' => [\n                'helper_text' => '',\n                'label' => '',\n                'description' => '',\n            ],\n            'updated_by' => [\n                'helper_text' => '',\n                'label' => '',\n                'description' => '',\n            ],\n        ],\n    ],\n];\n"}}, "cruds": {"item_1": {"type": "Livewire", "name": "User", "plural": "Users", "sectionId": 1, "modelId": 4, "tableId": 1, "projectId": 1, "basePath": "Users", "settings": {"itemName": "user", "collectionName": "users", "itemTitle": "crud.users.itemTitle", "collectionTitle": "crud.users.collectionTitle"}, "livewireNamespace": "App\\Livewire\\Dashboard", "livewireFormsNamespace": "App\\Livewire\\Dashboard\\Users\\Forms", "livewireIndexComponentName": "UserIndex", "livewireShowComponentName": "UserShow", "livewireCreateComponentName": "UserCreate", "livewireEditComponentName": "UserEdit", "defaultSearchColumnId": 2, "defaultSortColumnId": 13, "defaultSortDirection": "desc", "id": 1, "createdAt": "2025-07-09 20:25:10", "updatedAt": "2025-07-09 20:25:10"}, "__tableData": {"count": 3, "lastPrimaryKey": 3, "index": {"1": {"hasMany": {"crud_panels.crudId": [1], "inputs.crudId": [1, 2, 3, 4, 5]}}, "2": {"hasMany": {"crud_panels.crudId": [2], "inputs.crudId": [6, 7, 8, 9, 10]}}, "3": {"hasMany": {"crud_panels.crudId": [3], "inputs.crudId": [11, 12, 13, 14, 15]}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"type": "API", "name": "User", "plural": "Users", "sectionId": 2, "modelId": 4, "tableId": 1, "projectId": 1, "basePath": "Users", "settings": {"itemName": "user", "collectionName": "users", "itemTitle": "crud.users.itemTitle", "collectionTitle": "crud.users.collectionTitle"}, "livewireNamespace": "App\\Livewire\\Api", "livewireFormsNamespace": "App\\Livewire\\Api\\Users\\Forms", "livewireIndexComponentName": "UserIndex", "livewireShowComponentName": "UserShow", "livewireCreateComponentName": "UserCreate", "livewireEditComponentName": "UserEdit", "defaultSearchColumnId": 2, "defaultSortColumnId": 13, "defaultSortDirection": "desc", "id": 2, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}, "item_3": {"type": "Filament", "name": "User", "plural": "Users", "sectionId": 4, "modelId": 4, "tableId": 1, "projectId": 1, "basePath": "Users", "settings": {"itemName": "user", "collectionName": "users", "itemTitle": "crud.users.itemTitle", "collectionTitle": "crud.users.collectionTitle"}, "livewireNamespace": "App\\Livewire\\FilamentPanel", "livewireFormsNamespace": "App\\Livewire\\FilamentPanel\\Users\\Forms", "livewireIndexComponentName": "UserIndex", "livewireShowComponentName": "UserShow", "livewireCreateComponentName": "UserCreate", "livewireEditComponentName": "UserEdit", "defaultSearchColumnId": 2, "defaultSortColumnId": 13, "defaultSortDirection": "desc", "filamentSettings": {"modelLabel": "crud.users.itemTitle", "pluralModelLabel": "crud.users.collectionTitle", "navigationLabel": "crud.users.collectionTitle", "navigationIcon": "heroicon-o-rectangle-stack", "navigationOrder": 1, "navigationParentItem": null, "hasTitleCaseModelLabel": true, "navigationGroup": "Admin", "slug": null}, "id": 3, "createdAt": "2025-07-09 20:26:25", "updatedAt": "2025-07-09 20:26:25"}}, "crud_panels": {"item_1": {"title": "Main", "crudId": 1, "order": 0, "id": 1, "createdAt": "2025-07-09 20:25:10", "updatedAt": "2025-07-09 20:25:10"}, "__tableData": {"count": 3, "lastPrimaryKey": 3, "index": {"1": {"hasMany": {"inputs.panelId": [1, 2, 3, 4, 5]}}, "2": {"hasMany": {"inputs.panelId": [6, 7, 8, 9, 10]}}, "3": {"hasMany": {"inputs.panelId": [11, 12, 13, 14, 15]}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"title": "Main", "crudId": 2, "order": 0, "id": 2, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}, "item_3": {"title": "Main", "crudId": 3, "order": 0, "id": 3, "createdAt": "2025-07-09 20:26:25", "updatedAt": "2025-07-09 20:26:25"}}, "inputs": {"item_1": {"crudId": 1, "columnId": 2, "name": "name", "label": "crud.users.inputs.name.label", "placeholder": "crud.users.inputs.name.placeholder", "readOnly": false, "required": true, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 0, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "text", "creationRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}], "updateRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}], "panelId": 1, "id": 1, "createdAt": "2025-07-09 20:25:10", "updatedAt": "2025-07-09 20:25:10"}, "__tableData": {"count": 15, "lastPrimaryKey": 15, "index": {"1": {"hasMany": {}}, "2": {"hasMany": {}}, "3": {"hasMany": {}}, "4": {"hasMany": {}}, "5": {"hasMany": {}}, "6": {"hasMany": {}}, "7": {"hasMany": {}}, "8": {"hasMany": {}}, "9": {"hasMany": {}}, "10": {"hasMany": {}}, "11": {"hasMany": {}}, "12": {"hasMany": {}}, "13": {"hasMany": {}}, "14": {"hasMany": {}}, "15": {"hasMany": {}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"crudId": 1, "columnId": 3, "name": "email", "label": "crud.users.inputs.email.label", "placeholder": "crud.users.inputs.email.placeholder", "readOnly": false, "required": true, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 0, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "email", "creationRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "unique:users,email"}], "updateRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "unique:users,email"}], "panelId": 1, "id": 2, "createdAt": "2025-07-09 20:25:10", "updatedAt": "2025-07-09 20:25:10"}, "item_3": {"crudId": 1, "columnId": 5, "name": "password", "label": "crud.users.inputs.password.label", "placeholder": "crud.users.inputs.password.placeholder", "readOnly": false, "required": true, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 0, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": false, "showOnIndex": false, "type": "password", "creationRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "min:6"}], "updateRules": [{"type": "textual", "value": "nullable"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "min:6"}], "panelId": 1, "id": 3, "createdAt": "2025-07-09 20:25:10", "updatedAt": "2025-07-09 20:25:10"}, "item_4": {"crudId": 1, "columnId": 81, "name": "created_by", "label": "crud.users.inputs.created_by.label", "placeholder": "crud.users.inputs.created_by.placeholder", "readOnly": false, "required": false, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 1, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "number", "creationRules": [{"type": "textual", "value": "nullable"}], "updateRules": [{"type": "textual", "value": "nullable"}], "panelId": 1, "id": 4, "createdAt": "2025-07-09 20:25:10", "updatedAt": "2025-07-09 20:25:10"}, "item_5": {"crudId": 1, "columnId": 82, "name": "updated_by", "label": "crud.users.inputs.updated_by.label", "placeholder": "crud.users.inputs.updated_by.placeholder", "readOnly": false, "required": false, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 1, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "number", "creationRules": [{"type": "textual", "value": "nullable"}], "updateRules": [{"type": "textual", "value": "nullable"}], "panelId": 1, "id": 5, "createdAt": "2025-07-09 20:25:10", "updatedAt": "2025-07-09 20:25:10"}, "item_6": {"crudId": 2, "columnId": 2, "name": "name", "label": "crud.users.inputs.name.label", "placeholder": "crud.users.inputs.name.placeholder", "readOnly": false, "required": true, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 0, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "text", "creationRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}], "updateRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}], "panelId": 2, "id": 6, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}, "item_7": {"crudId": 2, "columnId": 3, "name": "email", "label": "crud.users.inputs.email.label", "placeholder": "crud.users.inputs.email.placeholder", "readOnly": false, "required": true, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 0, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "email", "creationRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "unique:users,email"}], "updateRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "unique:users,email"}], "panelId": 2, "id": 7, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}, "item_8": {"crudId": 2, "columnId": 5, "name": "password", "label": "crud.users.inputs.password.label", "placeholder": "crud.users.inputs.password.placeholder", "readOnly": false, "required": true, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 0, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": false, "showOnIndex": false, "type": "password", "creationRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "min:6"}], "updateRules": [{"type": "textual", "value": "nullable"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "min:6"}], "panelId": 2, "id": 8, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}, "item_9": {"crudId": 2, "columnId": 81, "name": "created_by", "label": "crud.users.inputs.created_by.label", "placeholder": "crud.users.inputs.created_by.placeholder", "readOnly": false, "required": false, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 1, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "number", "creationRules": [{"type": "textual", "value": "nullable"}], "updateRules": [{"type": "textual", "value": "nullable"}], "panelId": 2, "id": 9, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}, "item_10": {"crudId": 2, "columnId": 82, "name": "updated_by", "label": "crud.users.inputs.updated_by.label", "placeholder": "crud.users.inputs.updated_by.placeholder", "readOnly": false, "required": false, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 1, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "number", "creationRules": [{"type": "textual", "value": "nullable"}], "updateRules": [{"type": "textual", "value": "nullable"}], "panelId": 2, "id": 10, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}, "item_11": {"crudId": 3, "columnId": 2, "name": "name", "label": "crud.users.inputs.name.label", "placeholder": "crud.users.inputs.name.placeholder", "readOnly": false, "required": true, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 0, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "text", "creationRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}], "updateRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}], "filamentSettings": {"formData": {"inputType": "text-input", "helperText": "crud.users.filament.name.helper_text", "autofocus": true, "autoComplete": true}, "columnData": {"columnType": "text-column", "label": "crud.users.filament.name.label", "description": "crud.users.filament.name.description"}}, "panelId": 3, "id": 11, "createdAt": "2025-07-09 20:26:25", "updatedAt": "2025-07-09 20:26:25"}, "item_12": {"crudId": 3, "columnId": 3, "name": "email", "label": "crud.users.inputs.email.label", "placeholder": "crud.users.inputs.email.placeholder", "readOnly": false, "required": true, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 0, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "email", "creationRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "unique:users,email"}], "updateRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "unique:users,email"}], "filamentSettings": {"formData": {"inputType": "text-input", "helperText": "crud.users.filament.email.helper_text", "autofocus": false, "autoComplete": true}, "columnData": {"columnType": "text-column", "label": "crud.users.filament.email.label", "description": "crud.users.filament.email.description"}}, "panelId": 3, "id": 12, "createdAt": "2025-07-09 20:26:25", "updatedAt": "2025-07-09 20:26:25"}, "item_13": {"crudId": 3, "columnId": 5, "name": "password", "label": "crud.users.inputs.password.label", "placeholder": "crud.users.inputs.password.placeholder", "readOnly": false, "required": true, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 0, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": false, "showOnIndex": false, "type": "password", "creationRules": [{"type": "textual", "value": "required"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "min:6"}], "updateRules": [{"type": "textual", "value": "nullable"}, {"type": "textual", "value": "string"}, {"type": "textual", "value": "min:6"}], "filamentSettings": {"formData": {"inputType": "text-input", "helperText": "crud.users.filament.password.helper_text", "autofocus": false, "autoComplete": true}, "columnData": {"columnType": "text-column", "label": "crud.users.filament.password.label", "description": "crud.users.filament.password.description"}}, "panelId": 3, "id": 13, "createdAt": "2025-07-09 20:26:25", "updatedAt": "2025-07-09 20:26:25"}, "item_14": {"crudId": 3, "columnId": 81, "name": "created_by", "label": "crud.users.inputs.created_by.label", "placeholder": "crud.users.inputs.created_by.placeholder", "readOnly": false, "required": false, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 1, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "number", "creationRules": [{"type": "textual", "value": "nullable"}], "updateRules": [{"type": "textual", "value": "nullable"}], "filamentSettings": {"formData": {"inputType": "text-input", "helperText": "crud.users.filament.created_by.helper_text", "autofocus": false, "autoComplete": true}, "columnData": {"columnType": "text-column", "label": "crud.users.filament.created_by.label", "description": "crud.users.filament.created_by.description"}}, "panelId": 3, "id": 14, "createdAt": "2025-07-09 20:26:25", "updatedAt": "2025-07-09 20:26:25"}, "item_15": {"crudId": 3, "columnId": 82, "name": "updated_by", "label": "crud.users.inputs.updated_by.label", "placeholder": "crud.users.inputs.updated_by.placeholder", "readOnly": false, "required": false, "hidden": false, "defaultValue": "", "checked": false, "max": 0, "min": 0, "step": 1, "items": [], "showOnCreation": true, "showOnUpdate": true, "showOnDetails": true, "showOnIndex": true, "type": "number", "creationRules": [{"type": "textual", "value": "nullable"}], "updateRules": [{"type": "textual", "value": "nullable"}], "filamentSettings": {"formData": {"inputType": "text-input", "helperText": "crud.users.filament.updated_by.helper_text", "autofocus": false, "autoComplete": true}, "columnData": {"columnType": "text-column", "label": "crud.users.filament.updated_by.label", "description": "crud.users.filament.updated_by.description"}}, "panelId": 3, "id": 15, "createdAt": "2025-07-09 20:26:25", "updatedAt": "2025-07-09 20:26:25"}}, "routes": {"item_1": {"name": "users.index", "tag": "index", "method": "get", "type": "route", "path": "/users", "routableId": 1, "routableType": "<PERSON><PERSON>", "projectId": 1, "id": 1, "createdAt": "2025-07-09 20:25:10", "updatedAt": "2025-07-09 20:25:10"}, "__tableData": {"count": 8, "lastPrimaryKey": 9, "index": {"1": {"hasMany": {}}, "2": {"hasMany": {}}, "3": {"hasMany": {}}, "5": {"hasMany": {}}, "6": {"hasMany": {}}, "7": {"hasMany": {}}, "8": {"hasMany": {}}, "9": {"hasMany": {}}}, "additionalIndexes": {}, "items": [], "relations": []}, "item_2": {"name": "users.create", "tag": "create", "method": "get", "type": "route", "path": "/users/create", "routableId": 1, "routableType": "<PERSON><PERSON>", "projectId": 1, "id": 2, "createdAt": "2025-07-09 20:25:10", "updatedAt": "2025-07-09 20:25:10"}, "item_3": {"name": "users.edit", "tag": "edit", "method": "get", "type": "route", "path": "/users/{user}", "routableId": 1, "routableType": "<PERSON><PERSON>", "projectId": 1, "id": 3, "createdAt": "2025-07-09 20:25:10", "updatedAt": "2025-07-09 20:25:10"}, "item_5": {"name": "users.index", "method": "get", "path": "/users", "tag": "users.index", "type": "apiRoute", "routableId": 2, "routableType": "<PERSON><PERSON>", "projectId": 1, "hasCustomContent": true, "customContent": "[UserController::class, 'index']", "id": 5, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}, "item_6": {"name": "users.store", "method": "post", "path": "/users", "tag": "users.store", "type": "apiRoute", "routableId": 2, "routableType": "<PERSON><PERSON>", "projectId": 1, "hasCustomContent": true, "customContent": "[UserController::class, 'store']", "id": 6, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}, "item_7": {"name": "users.show", "method": "get", "path": "/users/{user}", "tag": "users.show", "type": "apiRoute", "routableId": 2, "routableType": "<PERSON><PERSON>", "projectId": 1, "hasCustomContent": true, "customContent": "[UserController::class, 'show']", "id": 7, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}, "item_8": {"name": "users.update", "method": "put", "path": "/users/{user}", "tag": "users.update", "type": "apiRoute", "routableId": 2, "routableType": "<PERSON><PERSON>", "projectId": 1, "hasCustomContent": true, "customContent": "[UserController::class, 'update']", "id": 8, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}, "item_9": {"name": "users.destroy", "method": "delete", "path": "/users/{user}", "tag": "users.destroy", "type": "apiRoute", "routableId": 2, "routableType": "<PERSON><PERSON>", "projectId": 1, "hasCustomContent": true, "customContent": "[UserController::class, 'destroy']", "id": 9, "createdAt": "2025-07-09 20:26:19", "updatedAt": "2025-07-09 20:26:19"}}, "pages": {"__tableData": {"count": 0, "lastPrimaryKey": 1, "index": {}, "additionalIndexes": {}, "items": [], "relations": []}}}, "tablesNames": ["projects", "tables", "columns", "indices", "index_column", "models", "fillable_model_column", "casts_model_column", "relationships", "hidden_model_column", "app_sections", "schema_sections", "navs", "renderable_files", "cruds", "crud_panels", "inputs", "routes", "pages"]}