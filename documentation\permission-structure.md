# Permission Structure - Criminal Management System

## Overview
This document outlines the comprehensive role-based access control (RBAC) system implemented for the Criminal Management System using Spatie <PERSON>vel Permission package. All permissions are managed in a single seeder file for consistency and maintainability.

## 🔐 Permission Categories

### 1. Dashboard Permissions
- `view-dashboard` - Access to main dashboard
- `view-analytics` - View crime analytics and trends
- `view-crime-statistics` - Access crime statistics
- `export-dashboard-data` - Export dashboard data

### 2. Criminal Profile Management Permissions
- `view-criminals` - View criminal profiles
- `create-criminals` - Create new criminal profiles
- `edit-criminals` - Edit existing criminal profiles
- `delete-criminals` - Delete criminal profiles
- `view-criminal-details` - View detailed criminal information
- `manage-criminal-biometrics` - Manage biometric data
- `upload-criminal-photos` - Upload criminal photos
- `view-criminal-history` - View criminal history
- `export-criminal-data` - Export criminal data

### 3. Case Management Permissions
- `view-cases` - View criminal cases
- `create-cases` - Create new cases
- `edit-cases` - Edit existing cases
- `delete-cases` - Delete cases
- `assign-cases` - Assign cases to officers
- `close-cases` - Close completed cases
- `reopen-cases` - Reopen closed cases
- `view-case-details` - View detailed case information
- `manage-case-status` - Update case status
- `export-case-data` - Export case data

### 4. Evidence Management Permissions
- `view-evidence` - View evidence records
- `create-evidence` - Create evidence records
- `edit-evidence` - Edit evidence information
- `delete-evidence` - Delete evidence records
- `upload-evidence` - Upload evidence files
- `manage-chain-of-custody` - Manage evidence custody chain
- `transfer-evidence` - Transfer evidence between custodians
- `dispose-evidence` - Dispose of evidence
- `view-forensic-analysis` - View forensic analysis results
- `manage-forensic-analysis` - Manage forensic analysis
- `export-evidence-data` - Export evidence data

### 5. Personal Belongings Management Permissions
- `view-belongings` - View personal belongings
- `create-belongings` - Create belongings records
- `edit-belongings` - Edit belongings information
- `delete-belongings` - Delete belongings records
- `release-belongings` - Release belongings to owners
- `dispose-belongings` - Dispose of unclaimed belongings
- `manage-belongings-inventory` - Manage belongings inventory
- `export-belongings-data` - Export belongings data

### 6. Court Case Management Permissions
- `view-court-cases` - View court cases
- `create-court-cases` - Create court case records
- `edit-court-cases` - Edit court case information
- `delete-court-cases` - Delete court cases
- `schedule-hearings` - Schedule court hearings
- `manage-court-outcomes` - Manage court verdicts and sentences
- `manage-bail` - Manage bail information
- `manage-parole` - Manage parole information
- `view-court-calendar` - View court calendar
- `export-court-data` - Export court data

### 7. Document Management Permissions
- `view-documents` - View documents
- `upload-documents` - Upload new documents
- `edit-documents` - Edit document information
- `delete-documents` - Delete documents
- `share-documents` - Share documents with others
- `manage-document-categories` - Manage document categories
- `view-document-history` - View document history
- `export-documents` - Export documents

### 8. User Management Permissions
- `view-users` - View user accounts
- `create-users` - Create new user accounts
- `edit-users` - Edit user information
- `delete-users` - Delete user accounts
- `manage-user-roles` - Assign/remove user roles
- `manage-user-permissions` - Manage user permissions
- `view-user-activity` - View user activity logs
- `reset-user-passwords` - Reset user passwords

### 9. System Administration Permissions
- `manage-system-settings` - Configure system settings
- `view-audit-logs` - View system audit logs
- `manage-backups` - Manage system backups
- `view-system-reports` - View system reports
- `manage-system-maintenance` - Perform system maintenance
- `configure-system` - Configure system parameters

### 10. Reporting and Analytics Permissions
- `view-reports` - View generated reports
- `create-reports` - Create new reports
- `export-reports` - Export reports
- `view-crime-analytics` - View crime analytics
- `view-performance-metrics` - View performance metrics
- `generate-statistical-reports` - Generate statistical reports

### 11. Security and Compliance Permissions
- `view-security-logs` - View security logs
- `manage-access-control` - Manage access control
- `view-compliance-reports` - View compliance reports
- `manage-data-retention` - Manage data retention policies
- `audit-system-access` - Audit system access

## 👥 Role Hierarchy and Permissions

### 1. Super Administrator
**Description**: Full system access and administration
**Permissions**: ALL permissions (complete system control)
**Use Case**: System owner, technical administrator

### 2. System Administrator
**Description**: System configuration and user management
**Key Permissions**:
- All user management permissions
- System administration permissions
- Security and compliance permissions
- Dashboard and analytics access
**Use Case**: IT department, system maintenance

### 3. Police Chief
**Description**: Department oversight and high-level access
**Key Permissions**:
- View-only access to all modules
- Export capabilities for all data
- Analytics and reporting access
- User activity monitoring
**Use Case**: Department head, strategic oversight

### 4. Detective Supervisor
**Description**: Investigation oversight and case management
**Key Permissions**:
- Full criminal profile management
- Full case management (including assignment)
- Evidence management and forensic analysis
- Court case creation and management
- Document management and sharing
**Use Case**: Investigation team leader

### 5. Detective
**Description**: Investigation and case management
**Key Permissions**:
- Criminal profile creation and editing
- Case creation and management
- Evidence handling and documentation
- Court case creation
- Document management
**Use Case**: Investigating officer

### 6. Police Officer
**Description**: Basic operations and data entry
**Key Permissions**:
- Basic criminal profile creation
- Case creation and viewing
- Evidence creation and upload
- Personal belongings management
- Document upload
**Use Case**: Front-line officer, patrol officer

### 7. Evidence Custodian
**Description**: Evidence and belongings management specialist
**Key Permissions**:
- Full evidence management
- Chain of custody management
- Personal belongings management
- Evidence disposal and transfer
**Use Case**: Evidence room manager

### 8. Court Clerk
**Description**: Court case management and scheduling
**Key Permissions**:
- Court case management
- Hearing scheduling
- Bail and parole management
- Court calendar management
- Document management
**Use Case**: Court administrative staff

### 9. Data Entry Clerk
**Description**: Basic data entry operations
**Key Permissions**:
- Basic criminal profile entry
- Basic case entry
- Document upload and editing
**Use Case**: Administrative support staff

### 10. Forensic Analyst
**Description**: Forensic analysis and evidence examination
**Key Permissions**:
- Evidence viewing and editing
- Forensic analysis management
- Biometric data management
- Document management
**Use Case**: Laboratory technician, forensic expert

### 11. Prosecutor
**Description**: Case review and court proceedings
**Key Permissions**:
- View criminal profiles and history
- View case details and evidence
- Court case management
- Bail management
- Report generation
**Use Case**: State prosecutor, legal counsel

### 12. Judge
**Description**: Court case review and decision making
**Key Permissions**:
- View criminal profiles and history
- View case details and evidence
- Court outcome management
- Bail and parole decisions
**Use Case**: Judicial officer

### 13. Auditor
**Description**: System audit and compliance review
**Key Permissions**:
- View-only access to all data
- Audit log access
- Compliance reporting
- User activity monitoring
- Report generation
**Use Case**: Internal auditor, compliance officer

### 14. Guest
**Description**: Limited read-only access
**Key Permissions**:
- Basic dashboard access
- Limited criminal profile viewing
- Basic case viewing
- Document viewing
**Use Case**: Temporary access, external stakeholders

## 🔧 Implementation Details

### Seeder File Location
`database/seeders/RolePermissionSeeder.php`

### Key Features
- **Single File Management**: All permissions and roles in one seeder
- **Hierarchical Structure**: Clear role hierarchy based on criminal justice needs
- **Malawi Context**: Role names and permissions tailored for Malawi law enforcement
- **Comprehensive Coverage**: All system modules covered
- **Security First**: Principle of least privilege applied

### Usage in Controllers
```php
// Check permission in controller
public function index()
{
    $this->authorize('view-criminals');
    // Controller logic
}

// Check permission in middleware
Route::middleware(['permission:view-criminals'])->group(function () {
    // Protected routes
});

// Check permission in Blade templates
@can('create-criminals')
    <button>Add Criminal</button>
@endcan
```

### Usage in Models
```php
// Check if user has permission
if (auth()->user()->can('edit-criminals')) {
    // Allow editing
}

// Check if user has role
if (auth()->user()->hasRole('Detective')) {
    // Role-specific logic
}
```

## 🚀 Seeding Instructions

### Run the Seeder
```bash
# Run all seeders (includes RolePermissionSeeder)
php artisan db:seed

# Run only the permission seeder
php artisan db:seed --class=RolePermissionSeeder

# Fresh migration with seeding
php artisan migrate:fresh --seed
```

### Test Users Created
The seeder creates test users for each role:
- `<EMAIL>` - Super Administrator
- `<EMAIL>` - System Administrator
- `<EMAIL>` - Police Chief
- `<EMAIL>` - Detective Supervisor
- `<EMAIL>` - Detective
- `<EMAIL>` - Police Officer
- `<EMAIL>` - Evidence Custodian
- `<EMAIL>` - Court Clerk
- `<EMAIL>` - Test User (Police Officer role)

## 🔒 Security Considerations

### Permission Naming Convention
- **Module-Action**: `view-criminals`, `create-cases`
- **Descriptive**: Clear understanding of what permission allows
- **Consistent**: Same pattern across all modules

### Role Assignment Best Practices
- **Principle of Least Privilege**: Users get minimum required permissions
- **Regular Audits**: Periodic review of role assignments
- **Temporary Access**: Use Guest role for temporary access
- **Role Separation**: Clear separation between operational and administrative roles

### Audit Trail Integration
- All permission checks are logged via Spatie Activity Log
- User role changes are tracked
- Permission usage is monitored
- Regular compliance reports generated

---

**Implementation Status**: ✅ Complete seeder with all roles and permissions
**Next Steps**: Integrate permission checks in controllers and views
**Maintenance**: Regular review and updates based on operational needs