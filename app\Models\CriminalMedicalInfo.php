<?php

namespace App\Models;

use App\Traits\HasUserTracking;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class CriminalMedicalInfo extends Model
{
    use HasFactory, HasUserTracking, LogsActivity, SoftDeletes;

    protected $table = 'criminal_medical_info';

    protected $fillable = [
        'criminal_id',
        'medical_condition',
        'allergies',
        'medications',
        'special_instructions',
        'blood_type',
        'medical_history',
        'last_updated',
        'updated_by',
    ];

    protected $casts = [
        'last_updated' => 'date',
    ];

    protected $dates = [
        'last_updated',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'medical_condition',
                'allergies',
                'medications',
                'special_instructions',
                'blood_type',
                'medical_history'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the criminal that owns this medical information.
     */
    public function criminal()
    {
        return $this->belongsTo(Criminal::class);
    }

    /**
     * Get the user who last updated the medical information.
     */
    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope for records with medical conditions.
     */
    public function scopeWithConditions($query)
    {
        return $query->whereNotNull('medical_condition');
    }

    /**
     * Scope for records with allergies.
     */
    public function scopeWithAllergies($query)
    {
        return $query->whereNotNull('allergies');
    }

    /**
     * Scope for records with medications.
     */
    public function scopeWithMedications($query)
    {
        return $query->whereNotNull('medications');
    }

    /**
     * Scope for records with special instructions.
     */
    public function scopeWithSpecialInstructions($query)
    {
        return $query->whereNotNull('special_instructions');
    }

    /**
     * Scope for specific blood type.
     */
    public function scopeBloodType($query, $bloodType)
    {
        return $query->where('blood_type', $bloodType);
    }

    /**
     * Scope for recently updated records.
     */
    public function scopeRecentlyUpdated($query, $days = 30)
    {
        return $query->where('last_updated', '>=', now()->subDays($days));
    }

    /**
     * Check if medical information has critical conditions.
     */
    public function hasCriticalConditions()
    {
        $criticalKeywords = [
            'diabetes', 'heart', 'cardiac', 'epilepsy', 'seizure', 
            'asthma', 'hypertension', 'mental', 'psychiatric',
            'suicide', 'depression', 'bipolar', 'schizophrenia'
        ];

        $medicalText = strtolower($this->medical_condition . ' ' . $this->medical_history);
        
        foreach ($criticalKeywords as $keyword) {
            if (str_contains($medicalText, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if has drug allergies.
     */
    public function hasDrugAllergies()
    {
        if (!$this->allergies) {
            return false;
        }

        $drugKeywords = [
            'penicillin', 'aspirin', 'ibuprofen', 'codeine', 
            'morphine', 'sulfa', 'antibiotic', 'medication'
        ];

        $allergiesText = strtolower($this->allergies);
        
        foreach ($drugKeywords as $keyword) {
            if (str_contains($allergiesText, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if requires special handling.
     */
    public function requiresSpecialHandling()
    {
        return $this->hasCriticalConditions() || 
               $this->hasDrugAllergies() || 
               !empty($this->special_instructions);
    }

    /**
     * Get medical alert level.
     */
    public function getMedicalAlertLevelAttribute()
    {
        if ($this->hasCriticalConditions()) {
            return 'Critical';
        } elseif ($this->hasDrugAllergies()) {
            return 'High';
        } elseif ($this->requiresSpecialHandling()) {
            return 'Medium';
        } else {
            return 'Low';
        }
    }

    /**
     * Get medical alert color.
     */
    public function getMedicalAlertColorAttribute()
    {
        return match($this->medical_alert_level) {
            'Critical' => 'text-danger',
            'High' => 'text-warning',
            'Medium' => 'text-info',
            'Low' => 'text-success',
            default => 'text-muted',
        };
    }

    /**
     * Get formatted medical summary.
     */
    public function getMedicalSummaryAttribute()
    {
        $summary = [];

        if ($this->medical_condition) {
            $summary[] = "Condition: " . $this->medical_condition;
        }

        if ($this->allergies) {
            $summary[] = "Allergies: " . $this->allergies;
        }

        if ($this->medications) {
            $summary[] = "Medications: " . $this->medications;
        }

        if ($this->blood_type) {
            $summary[] = "Blood Type: " . $this->blood_type;
        }

        return implode(' | ', $summary) ?: 'No medical information recorded';
    }

    /**
     * Get available blood types.
     */
    public static function getBloodTypes()
    {
        return [
            'A+' => 'A Positive',
            'A-' => 'A Negative',
            'B+' => 'B Positive',
            'B-' => 'B Negative',
            'AB+' => 'AB Positive',
            'AB-' => 'AB Negative',
            'O+' => 'O Positive',
            'O-' => 'O Negative',
        ];
    }

    /**
     * Get blood type options for forms.
     */
    public static function getBloodTypeOptions()
    {
        return collect(self::getBloodTypes())->map(function ($label, $value) {
            return ['value' => $value, 'label' => $label];
        })->values()->toArray();
    }

    /**
     * Update the last updated timestamp.
     */
    public function updateLastUpdated()
    {
        $this->update([
            'last_updated' => now()->toDateString(),
            'updated_by' => auth()->id()
        ]);
    }

    /**
     * Check if medical information is outdated.
     */
    public function isOutdated($months = 12)
    {
        if (!$this->last_updated) {
            return true;
        }

        return $this->last_updated->diffInMonths(now()) > $months;
    }

    /**
     * Get medical information completeness percentage.
     */
    public function getCompletenessPercentage()
    {
        $fields = [
            'medical_condition',
            'allergies',
            'medications',
            'blood_type',
            'medical_history'
        ];

        $filledFields = 0;
        foreach ($fields as $field) {
            if (!empty($this->$field)) {
                $filledFields++;
            }
        }

        return round(($filledFields / count($fields)) * 100);
    }

    /**
     * Get medical information status.
     */
    public function getMedicalStatusAttribute()
    {
        $completeness = $this->getCompletenessPercentage();
        
        if ($completeness >= 80) {
            return 'Complete';
        } elseif ($completeness >= 50) {
            return 'Partial';
        } else {
            return 'Incomplete';
        }
    }

    /**
     * Get medical status color.
     */
    public function getMedicalStatusColorAttribute()
    {
        return match($this->medical_status) {
            'Complete' => 'text-success',
            'Partial' => 'text-warning',
            'Incomplete' => 'text-danger',
            default => 'text-muted',
        };
    }
}
