# Dashboard Implementation Task

## Overview
Create a comprehensive dashboard for the Criminal Management System that provides real-time crime statistics, analytics, and quick access to key functions for law enforcement officers in Malawi.

## Dashboard Requirements (from overview.md)

### Top Banner
- Username/Profile Picture
- Police Station/Department Name (Use Malawi Names)
- Date and Time display

### Main Dashboard Sections

#### Section 1: Case Overview
- Total Active Cases
- New Cases (last 24 hours)
- Cases Awaiting Investigation
- Cases Under Prosecution
- Closed Cases (last 30 days)

#### Section 2: Priority Cases
- High-Priority Cases (homicides, kidnappings)
- Cases with Upcoming Court Dates
- Cases with Pending Forensic Analysis

#### Section 3: Alerts and Notifications
- New Case Assignments
- Updates on Ongoing Investigations
- Reminders for Court Appearances
- System Messages (software updates)

#### Section 4: Quick Links
- Report a New Crime
- Search Cases
- Access Investigation Tools
- View Case Statistics

#### Section 5: Visual Analytics
- Crime Heatmap (geospatial visualization)
- Crime Trends (line chart or bar graph)
- Top Crime Categories (pie chart)

#### Bottom Panel
- System News and Updates
- Training and Resource Links
- Contact Support

## Technical Implementation

### 1. Dashboard Controller
**File**: `app/Http/Controllers/DashboardController.php`

**Methods**:
- `index()` - Main dashboard view
- `getCaseStatistics()` - API endpoint for case stats
- `getCrimeAnalytics()` - API endpoint for crime analytics
- `getRecentActivity()` - API endpoint for recent activities
- `getNotifications()` - API endpoint for notifications

### 2. Dashboard Models and Data

**Case Statistics Model**:
```php
class CaseStatistics
{
    public function getTotalActiveCases()
    public function getNewCases($hours = 24)
    public function getCasesAwaitingInvestigation()
    public function getCasesUnderProsecution()
    public function getClosedCases($days = 30)
    public function getPriorityCases()
    public function getUpcomingCourtDates()
    public function getPendingForensicAnalysis()
}
```

**Crime Analytics Model**:
```php
class CrimeAnalytics
{
    public function getCrimeTrends($period = '30days')
    public function getCrimeCategories()
    public function getCrimeHeatmapData()
    public function getCrimeByLocation()
    public function getCrimeByTimeOfDay()
}
```

### 3. Dashboard Widgets

#### Statistics Cards
- **Active Cases Card**: Display total active cases with trend indicator
- **New Cases Card**: Show new cases in last 24 hours
- **Investigation Card**: Cases awaiting investigation
- **Prosecution Card**: Cases under prosecution
- **Closed Cases Card**: Recently closed cases

#### Priority Alerts Widget
- **High Priority Cases**: List of urgent cases
- **Court Reminders**: Upcoming court dates
- **Forensic Pending**: Cases waiting for forensic results

#### Quick Actions Widget
- **Report Crime**: Quick form to report new crime
- **Search Cases**: Advanced search functionality
- **Investigation Tools**: Access to investigation resources
- **Statistics**: Detailed statistics view

### 4. Data Visualization Components

#### Crime Heatmap
- **Technology**: Leaflet.js with heat layer plugin
- **Data Source**: Crime location coordinates
- **Features**:
  - Interactive map of Malawi
  - Heat intensity based on crime frequency
  - Filter by crime type and date range
  - Click for detailed information

#### Crime Trends Chart
- **Technology**: Chart.js line chart
- **Data**: Crime incidents over time
- **Features**:
  - Multiple time periods (7 days, 30 days, 6 months, 1 year)
  - Filter by crime category
  - Trend analysis with percentage change

#### Crime Categories Chart
- **Technology**: Chart.js pie/doughnut chart
- **Data**: Crime distribution by category
- **Features**:
  - Interactive segments
  - Percentage and count display
  - Filter by location and time period

### 5. Real-time Features

#### Live Notifications
- **Technology**: Laravel Broadcasting with Pusher/WebSockets
- **Features**:
  - New case assignments
  - Case status updates
  - Court date reminders
  - System alerts

#### Auto-refresh Data
- **Technology**: AJAX polling or WebSockets
- **Frequency**: Every 5 minutes for statistics, real-time for notifications
- **Components**: Statistics cards, recent activity feed

## Implementation Steps

### Phase 1: Basic Dashboard Structure
1. **Create Dashboard Controller**
   - Set up basic controller with index method
   - Create data retrieval methods
   - Implement caching for performance

2. **Create Dashboard Blade Template**
   - Use DashUI Pro dashboard template as base
   - Implement responsive grid layout
   - Add placeholder widgets

3. **Implement Statistics Cards**
   - Create case statistics calculations
   - Design card components
   - Add trend indicators and icons

### Phase 2: Data Visualization
1. **Crime Heatmap Implementation**
   - Integrate Leaflet.js mapping library
   - Create crime location data API
   - Implement heat layer visualization
   - Add interactive features

2. **Charts Implementation**
   - Integrate Chart.js library
   - Create crime trends line chart
   - Implement crime categories pie chart
   - Add interactive features and filters

3. **Analytics API Development**
   - Create API endpoints for chart data
   - Implement data aggregation queries
   - Add caching for performance
   - Create data transformation methods

### Phase 3: Advanced Features
1. **Real-time Notifications**
   - Set up Laravel Broadcasting
   - Create notification components
   - Implement WebSocket connections
   - Add notification management

2. **Quick Actions Integration**
   - Create quick action modals
   - Implement AJAX form submissions
   - Add search functionality
   - Create investigation tools links

3. **Performance Optimization**
   - Implement Redis caching
   - Optimize database queries
   - Add lazy loading for charts
   - Implement pagination for large datasets

## Database Requirements

### Dashboard-specific Tables
```sql
-- Dashboard preferences per user
CREATE TABLE dashboard_preferences (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    widget_layout JSON,
    refresh_interval INT DEFAULT 300,
    default_filters JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- System notifications
CREATE TABLE notifications (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    type VARCHAR(50),
    title VARCHAR(255),
    message TEXT,
    data JSON,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP
);

-- Dashboard analytics cache
CREATE TABLE analytics_cache (
    id BIGINT PRIMARY KEY,
    cache_key VARCHAR(255) UNIQUE,
    data JSON,
    expires_at TIMESTAMP,
    created_at TIMESTAMP
);
```

### Required Indexes
```sql
CREATE INDEX idx_cases_status_created ON cases(status, created_at);
CREATE INDEX idx_cases_priority_status ON cases(priority, status);
CREATE INDEX idx_crimes_location_date ON crimes(latitude, longitude, created_at);
CREATE INDEX idx_court_dates_upcoming ON court_proceedings(hearing_date, status);
```

## Sample Data for Malawi Context

### Police Stations
- Lilongwe Central Police Station
- Blantyre Police Station
- Mzuzu Police Station
- Zomba Police Station
- Kasungu Police Station

### Sample Crime Categories (from overview.md)
- Violent Crimes (Murder, Assault, Robbery)
- Property Crimes (Burglary, Theft, Vandalism)
- Drug-Related Crimes (Possession, Trafficking)
- Traffic-Related Crimes (Reckless Driving, DUI)
- White-Collar Crimes (Fraud, Embezzlement)

## User Interface Design

### Layout Structure
```
┌─────────────────────────────────────────────────────────┐
│ Header: Logo | Police Station | User Profile | DateTime │
├─────────────────────────────────────────────────────────┤
│ Statistics Cards Row                                    │
│ [Active] [New] [Investigation] [Prosecution] [Closed]  │
├─────────────────────────────────────────────────────────┤
│ Priority Cases │ Quick Actions │ Recent Activity        │
│               │               │                        │
├─────────────────────────────────────────────────────────┤
│ Crime Heatmap                  │ Analytics Charts       │
│                               │                        │
├─────────────────────────────────────────────────────────┤
│ System News & Updates                                   │
└─────────────────────────────────────────────────────────┘
```

### Responsive Behavior
- **Desktop**: Full layout with all widgets visible
- **Tablet**: Stacked widgets, collapsible sidebar
- **Mobile**: Single column layout, swipeable cards

## Performance Considerations

### Caching Strategy
- **Statistics**: Cache for 5 minutes
- **Charts Data**: Cache for 15 minutes
- **Heatmap Data**: Cache for 30 minutes
- **User Preferences**: Cache until changed

### Database Optimization
- Use database views for complex statistics
- Implement query result caching
- Use indexes for frequently queried columns
- Consider read replicas for analytics queries

## Testing Requirements

### Unit Tests
- Dashboard controller methods
- Statistics calculation methods
- Data transformation functions
- Cache management functions

### Integration Tests
- Dashboard page rendering
- API endpoint responses
- Real-time notification delivery
- Chart data accuracy

### Performance Tests
- Page load time under various data volumes
- API response times
- Memory usage with large datasets
- Concurrent user handling

## Security Considerations

### Access Control
- Role-based dashboard access
- Data filtering based on user permissions
- Secure API endpoints with authentication
- Audit logging for sensitive data access

### Data Protection
- Anonymize sensitive data in analytics
- Secure transmission of real-time data
- Input validation for all user inputs
- XSS protection for dynamic content

## Deliverables
1. Dashboard controller with all required methods
2. Responsive dashboard Blade template
3. Interactive crime heatmap
4. Analytics charts and visualizations
5. Real-time notification system
6. Performance-optimized database queries
7. Comprehensive test suite
8. Documentation for dashboard features

## Timeline Estimate
- **Phase 1**: 4-5 days
- **Phase 2**: 6-7 days
- **Phase 3**: 4-5 days
- **Total**: 14-17 days

## Dependencies
- Completion of UI template integration
- Basic database schema setup
- Sample crime data for testing
- Mapping service API keys (if using external maps)