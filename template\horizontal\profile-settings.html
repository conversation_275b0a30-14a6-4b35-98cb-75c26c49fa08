<!DOCTYPE html>
<html lang="en" data-layout="horizontal">


<!-- Mirrored from dashui.codescandy.com/dashuipro/horizontal/profile-settings.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:31:00 GMT -->
<head>
  <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
<meta name="author" content="Codescandy" />

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M8S4MT3EYG"></script>
<script>
	window.dataLayer = window.dataLayer || [];
	function gtag() {
		dataLayer.push(arguments);
	}
	gtag('js', new Date());

	gtag('config', 'G-M8S4MT3EYG');
</script>

<!-- Favicon icon-->
<link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon/favicon.ico" />

<!-- Color modes -->
<script src="../assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="../assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />
<link href="../assets/libs/%40mdi/font/css/materialdesignicons.min.css" rel="stylesheet" />
<link href="../assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />

<!-- Theme CSS -->
<link rel="stylesheet" href="../assets/css/theme.min.css">

    <title>Activity - Admin Dashboard Template</title>
    <link href="../assets/libs/dropzone/dist/dropzone.css" rel="stylesheet">
  </head>

  <body>
    <main id="main-wrapper" class="main-wrapper">
      <div class="header">
	<!-- navbar -->
	<div class="navbar-custom navbar navbar-expand-lg">
		<div class="container-fluid px-0">
			<a class="navbar-brand d-block d-md-none" href="../index.html">
				<img src="../assets/images/brand/logo/logo-2.svg" alt="Image" />
			</a>

			<a id="nav-toggle" href="#!" class="ms-auto ms-md-0 me-0 me-lg-3">
				<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor" class="bi bi-text-indent-left text-muted" viewBox="0 0 16 16">
					<path
						d="M2 3.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm.646 2.146a.5.5 0 0 1 .708 0l2 2a.5.5 0 0 1 0 .708l-2 2a.5.5 0 0 1-.708-.708L4.293 8 2.646 6.354a.5.5 0 0 1 0-.708zM7 6.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm-5 3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"
					/>
				</svg>
			</a>

			<div class="d-none d-md-none d-lg-block">
				<!-- Form -->
				<form action="#">
					<div class="input-group">
						<input class="form-control rounded-3 bg-transparent ps-9" type="search" value="" id="searchInput" placeholder="Search" />
						<span class="">
							<button class="btn position-absolute start-0" type="button">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="15"
									height="15"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
									class="feather feather-search text-dark"
								>
									<circle cx="11" cy="11" r="8"></circle>
									<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
								</svg>
							</button>
						</span>
					</div>
				</form>
			</div>
			<!--Navbar nav -->
			<ul class="navbar-nav navbar-right-wrap ms-lg-auto d-flex nav-top-wrap align-items-center ms-4 ms-lg-0">
				<li>
					<div class="dropdown">
						<button class="btn btn-ghost btn-icon rounded-circle" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
							<i class="bi theme-icon-active"></i>
							<span class="visually-hidden bs-theme-text">Toggle theme</span>
						</button>
						<ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
									<i class="bi theme-icon bi-sun-fill"></i>
									<span class="ms-2">Light</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
									<i class="bi theme-icon bi-moon-stars-fill"></i>
									<span class="ms-2">Dark</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="auto" aria-pressed="true">
									<i class="bi theme-icon bi-circle-half"></i>
									<span class="ms-2">Auto</span>
								</button>
							</li>
						</ul>
					</div>
				</li>

				<li class="dropdown stopevent ms-2">
					<a class="btn btn-ghost btn-icon rounded-circle" href="#!" role="button" id="dropdownNotification" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<i class="icon-xs" data-feather="bell"></i>
					</a>
					<div class="dropdown-menu dropdown-menu-lg dropdown-menu-end" aria-labelledby="dropdownNotification">
						<div>
							<div class="border-bottom px-3 pt-2 pb-3 d-flex justify-content-between align-items-center">
								<p class="mb-0 text-dark fw-medium fs-4">Notifications</p>
								<a href="#!" class="text-muted">
									<span>
										<i class="me-1 icon-xs" data-feather="settings"></i>
									</span>
								</a>
							</div>
							<div data-simplebar style="height: 250px">
								<!-- List group -->
								<ul class="list-group list-group-flush notification-list-scroll">
									<!-- List group item -->
									<li class="list-group-item bg-light">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Rishi Chopra</h5>
											<p class="mb-0">Mauris blandit erat id nunc blandit, ac eleifend dolor pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Neha Kannned</h5>
											<p class="mb-0">Proin at elit vel est condimentum elementum id in ante. Maecenas et sapien metus.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Nirmala Chauhan</h5>
											<p class="mb-0">Morbi maximus urna lobortis elit sollicitudin sollicitudieget elit vel pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Sina Ray</h5>
											<p class="mb-0">Sed aliquam augue sit amet mauris volutpat hendrerit sed nunc eu diam.</p>
										</a>
									</li>
								</ul>
							</div>
							<div class="border-top px-3 py-2 text-center">
								<a href="#!" class="text-inherit">View all Notifications</a>
							</div>
						</div>
					</div>
				</li>
				<!-- List -->
				<li class="dropdown ms-2">
					<a class="rounded-circle" href="#!" role="button" id="dropdownUser" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<div class="avatar avatar-md avatar-indicators avatar-online">
							<img alt="avatar" src="../assets/images/avatar/avatar-11.jpg" class="rounded-circle" />
						</div>
					</a>
					<div class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownUser">
						<div class="px-4 pb-0 pt-2">
							<div class="lh-1">
								<h5 class="mb-1">John E. Grainger</h5>
								<a href="#!" class="text-inherit fs-6">View my profile</a>
							</div>
							<div class="dropdown-divider mt-3 mb-2"></div>
						</div>

						<ul class="list-unstyled">
							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="user"></i>
									Edit Profile
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="activity"></i>
									Activity Log
								</a>
							</li>

							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="settings"></i>
									Settings
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="../index.html">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="power"></i>
									Sign Out
								</a>
							</li>
						</ul>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>

 <!-- navbar horizontal -->
 <!-- navbar -->
<div class="navbar-horizontal nav-dashboard">
	<div class="container-fluid">
		<nav class="navbar navbar-expand-lg navbar-default navbar-dropdown p-0 py-lg-2">
			<div class="d-flex d-lg-block justify-content-between align-items-center w-100 w-lg-0 py-2 px-4 px-md-2 px-lg-0">
				<span class="d-lg-none">Menu</span>
				<!-- Button -->
				<button
					class="navbar-toggler collapsed ms-2"
					type="button"
					data-bs-toggle="collapse"
					data-bs-target="#navbar-default"
					aria-controls="navbar-default"
					aria-expanded="false"
					aria-label="Toggle navigation"
				>
					<span class="icon-bar top-bar mt-0"></span>
					<span class="icon-bar middle-bar"></span>
					<span class="icon-bar bottom-bar"></span>
				</button>
			</div>
			<!-- Collapse -->
			<div class="collapse navbar-collapse px-6 px-lg-0" id="navbar-default">
				<ul class="navbar-nav">
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarDashboard" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-bs-display="static">Dashboard</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarDashboard">
							<li>
								<a class="dropdown-item" href="index.html">Project</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-analytics.html">Analytics</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-ecommerce.html">Ecommerce</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-crm.html">CRM</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-finance.html">Finance</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-blog.html">Blog</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarApps" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Apps</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarApps">
							<li>
								<a class="dropdown-item" href="calendar.html">Calendar</a>
							</li>
							<li>
								<a class="dropdown-item" href="apps-file-manager.html">File Manager</a>
							</li>

							<li>
								<a class="dropdown-item" href="chat-app.html">Chat</a>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Kanban</a>
								<ul class="dropdown-menu">
									<li>
										<a href="task-kanban-grid.html" class="dropdown-item">Board</a>
									</li>
									<li>
										<a href="task-kanban-list.html" class="dropdown-item">List</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Email</a>
								<ul class="dropdown-menu">
									<li>
										<a href="mail.html" class="dropdown-item">Inbox</a>
									</li>
									<li>
										<a href="mail-details.html" class="dropdown-item">Details</a>
									</li>
									<li>
										<a href="mail-draft.html" class="dropdown-item">Draft</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Ecommerce</a>
								<ul class="dropdown-menu">
									<li>
										<a href="ecommerce-products.html" class="dropdown-item">Products</a>
									</li>
									<li>
										<a href="ecommerce-products-details.html" class="dropdown-item">Prouduct Details</a>
									</li>
									<li>
										<a href="ecommerce-product-edit.html" class="dropdown-item">Add Product</a>
									</li>

									<li>
										<a href="ecommerce-order-list.html" class="dropdown-item">Orders</a>
									</li>
									<li>
										<a href="ecommerce-order-detail.html" class="dropdown-item">Order Details</a>
									</li>
									<li>
										<a href="ecommerce-cart.html" class="dropdown-item">Shopping Cart</a>
									</li>
									<li>
										<a href="ecommerce-checkout.html" class="dropdown-item">Checkout</a>
									</li>
									<li>
										<a href="ecommerce-customer.html" class="dropdown-item">Customers</a>
									</li>
									<li>
										<a href="ecommerce-seller.html" class="dropdown-item">Seller</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Project</a>
								<ul class="dropdown-menu">
									<li class="nav-item">
										<a class="dropdown-item" href="project-grid.html">Grid</a>
									</li>
									<li class="nav-item">
										<a class="dropdown-item" href="project-list.html">List</a>
									</li>

									<li class="dropdown-submenu dropend">
										<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Single</a>
										<ul class="dropdown-menu">
											<li class="nav-item">
												<a class="dropdown-item" href="project-overview.html">Overview</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="project-task.html">Task</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="project-budget.html">Budget</a>
											</li>

											<li class="nav-item">
												<a class="dropdown-item" href="project-files.html">File</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="project-team.html">Team</a>
											</li>
										</ul>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">CRM</a>
								<ul class="dropdown-menu">
									<li>
										<a href="crm-company.html" class="dropdown-item">Company</a>
									</li>
									<li>
										<a href="crm-contacts.html" class="dropdown-item">Contacts</a>
									</li>
									<li>
										<a class="dropdown-item" href="deals.html">
											Deals
											<span class="badge text-bg-primary ms-2">New</span>
										</a>
									</li>
									<li>
										<a class="dropdown-item" href="deals-single.html">
											Deals Single
											<span class="badge text-bg-primary ms-2">New</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Invoice</a>
								<ul class="dropdown-menu">
									<li>
										<a href="invoice-list.html" class="dropdown-item">List</a>
									</li>
									<li>
										<a href="invoice-detail.html" class="dropdown-item">Details</a>
									</li>
									<li>
										<a href="invoice-generator.html" class="dropdown-item">Create Invoice</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Profile</a>
								<ul class="dropdown-menu">
									<li>
										<a href="profile-overview.html" class="dropdown-item">Overview</a>
									</li>
									<li>
										<a href="profile-project.html" class="dropdown-item">Project</a>
									</li>
									<li>
										<a href="profile-file.html" class="dropdown-item">Files</a>
									</li>
									<li>
										<a href="profile-team.html" class="dropdown-item">Team</a>
									</li>
									<li>
										<a href="profile-followers.html" class="dropdown-item">Followers</a>
									</li>
									<li>
										<a href="profile-activity.html" class="dropdown-item">Activity</a>
									</li>
									<li>
										<a class="dropdown-item" href="profile-settings.html">Settings</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Blog</a>
								<ul class="dropdown-menu">
									<li>
										<a class="dropdown-item" href="blog-author.html">Author</a>
									</li>
									<li>
										<a class="dropdown-item" href="blog-author-detail.html">Detail</a>
									</li>
									<li>
										<a class="dropdown-item" href="create-blog-post.html">Create Post</a>
									</li>
								</ul>
							</li>
						</ul>
					</li>
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarAuthentication" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Authentication</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarAuthentication">
							<li>
								<a class="dropdown-item" href="sign-in.html">Sign In</a>
							</li>
							<li>
								<a class="dropdown-item" href="sign-up.html">Sign Up</a>
							</li>
							<li>
								<a class="dropdown-item" href="forget-password.html">Forgot Password</a>
							</li>
							<li>
								<a class="dropdown-item" href="maintenance.html">maintenance</a>
							</li>
							<li>
								<a class="dropdown-item" href="404-error.html">404 Error</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="layoutsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">Layouts</a>
						<ul class="dropdown-menu dropdown-menu-start" aria-labelledby="layoutsDropdown">
							<li><span class="dropdown-header">Layouts</span></li>
							<li class="nav-item">
								<a class="dropdown-item" href="../index.html">Default</a>
							</li>

							<li class="nav-item">
								<a class="dropdown-item" href="index.html">Horizontal</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarPages" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Pages</a>
						<ul class="dropdown-menu" aria-labelledby="navbarPages">
							<li>
								<a class="dropdown-item" href="pricing.html">Pricing</a>
							</li>
							<li>
								<a class="dropdown-item" href="starter.html">Starter</a>
							</li>

							<li>
								<a class="dropdown-item" href="maintenance.html">Maintenance</a>
							</li>
							<li>
								<a class="dropdown-item" href="404-error.html">404 Error</a>
							</li>
						</ul>
					</li>
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarBaseUI" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Components</a>
						<div class="dropdown-menu dropdown-menu-xl" aria-labelledby="navbarBaseUI">
							<div class="row">
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="components/accordions.html" class="dropdown-item">Accordions</a>
										</li>
										<li class="nav-item">
											<a class="dropdown-item" href="components/alerts.html">Alert</a>
										</li>

										<li class="nav-item">
											<a href="components/badge.html" class="dropdown-item">Badge</a>
										</li>

										<li class="nav-item">
											<a href="components/breadcrumb.html" class="dropdown-item">Breadcrumb</a>
										</li>
										<li class="nav-item">
											<a href="components/buttons.html" class="dropdown-item">Buttons</a>
										</li>
										<li class="nav-item">
											<a href="components/button-group.html" class="dropdown-item">Button group</a>
										</li>
										<li class="nav-item">
											<a href="components/card.html" class="dropdown-item">Card</a>
										</li>
										<li class="nav-item">
											<a href="components/carousel.html" class="dropdown-item">Carousel</a>
										</li>
										<li class="nav-item">
											<a href="components/close-button.html" class="dropdown-item">Close Button</a>
										</li>
									</ul>
								</div>
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="components/collapse.html" class="dropdown-item">Collapse</a>
										</li>
										<li class="nav-item">
											<a href="components/dropdowns.html" class="dropdown-item">Dropdowns</a>
										</li>
										<li class="nav-item">
											<a href="components/forms.html" class="dropdown-item">Forms</a>
										</li>

										<li class="nav-item">
											<a href="components/list-group.html" class="dropdown-item">List group</a>
										</li>
										<li class="nav-item">
											<a href="components/modal.html" class="dropdown-item">Modal</a>
										</li>
										<li class="nav-item">
											<a href="components/navs-tabs.html" class="dropdown-item">Navs and tabs</a>
										</li>
										<li class="nav-item">
											<a href="components/navbar.html" class="dropdown-item">Navbar</a>
										</li>
										<li class="nav-item">
											<a href="components/offcanvas.html" class="dropdown-item">Offcanvas</a>
										</li>
										<li class="nav-item">
											<a href="components/pagination.html" class="dropdown-item">Pagination</a>
										</li>
									</ul>
								</div>
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="components/placeholders.html" class="dropdown-item">Placeholders</a>
										</li>
										<li class="nav-item">
											<a href="components/popovers.html" class="dropdown-item">Popovers</a>
										</li>
										<li class="nav-item">
											<a href="components/progress.html" class="dropdown-item">Progress</a>
										</li>
										<li class="nav-item">
											<a href="components/scrollspy.html" class="dropdown-item">Scrollspy</a>
										</li>
										<li class="nav-item">
											<a href="components/spinners.html" class="dropdown-item">Spinners</a>
										</li>
										<li class="nav-item">
											<a href="components/tables.html" class="dropdown-item">Tables</a>
										</li>
										<li class="nav-item">
											<a href="components/toasts.html" class="dropdown-item">Toasts</a>
										</li>
										<li class="nav-item">
											<a href="components/tooltips.html" class="dropdown-item">Tooltips</a>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<i data-feather="more-horizontal" class="icon-xxs"></i>
						</a>
						<div class="dropdown-menu dropdown-menu-md" aria-labelledby="navbarDropdown">
							<div class="list-group">
								<a class="list-group-item list-group-item-action border-0" href="../docs/index.html">
									<div class="d-flex align-items-center">
										<i data-feather="file-text" class="icon-sm text-primary"></i>

										<div class="ms-3">
											<h5 class="mb-0">Documentations</h5>
											<p class="mb-0 fs-6">Browse the all documentation</p>
										</div>
									</div>
								</a>
								<a class="list-group-item list-group-item-action border-0" href="../docs/changelog.html">
									<div class="d-flex align-items-center">
										<i data-feather="layers" class="icon-sm text-primary"></i>
										<div class="ms-3">
											<h5 class="mb-0">
												Changelog
												<span class="text-primary ms-1">v1.0.0</span>
											</h5>
											<p class="mb-0 fs-6">See what's new</p>
										</div>
									</div>
								</a>
							</div>
						</div>
					</li>
				</ul>
			</div>
		</nav>
	</div>
</div>


       <!-- page content  -->
      <div id="app-content">

        <!-- Container fluid -->
        <div class="app-content-area">
          <div class="container-fluid">

            <div class="row">
                <div class="col-lg-12 col-md-12 col-12">
                  <!-- Page header -->

                    <div class=" mb-5">
                        <h3 class="mb-0 fw-bold">General</h3>

                    </div>

                </div>
              </div>
              <div class="row mb-8">
                <div class="col-xl-3 col-lg-4 col-md-12 col-12">
                  <div class="mb-4 mb-lg-0">
                    <h4 class="mb-1">General Setting</h4>
                    <p class="mb-0 fs-5 text-muted">Profile configuration settings </p>
                  </div>

                </div>

                <div class="col-xl-9 col-lg-8 col-md-12 col-12">
                  <!-- card -->
                  <div class="card">
                    <!-- card body -->
                    <div class="card-body">
                      <div class=" mb-6">
                        <h4 class="mb-1">General Settings</h4>

                      </div>
                      <div class="row align-items-center mb-8">
                        <div class="col-md-3 mb-3 mb-md-0">
                          <h5 class="mb-0">Avatar</h5>
                        </div>
                        <div class="col-md-9">
                            <div class="d-flex align-items-center mb-4">
                                <div >
                                    <img class="image  avatar avatar-lg rounded-circle" src="../assets/images/avatar/avatar-11.jpg" alt="Image">
                                  </div>

                                <div class="file-upload btn btn-outline-white ms-4">
                                  <input type="file" class="file-input opacity-0">Upload Photo
                                </div>
                              </div>

                        </div>
                      </div>
                      <!-- col -->
                      <div class="row mb-8">
                        <div class="col-md-3 mb-3 mb-md-0">
                          <!-- heading -->
                          <h5 class="mb-0">Cover photo</h5>
                        </div>
                        <div class="col-md-9">
                          <!-- dropzone input -->
                          <div>
                            <form action="#" class="dropzone mb-3 border-dashed">
                              <div class="fallback">
                                <input name="file" type="file" multiple>
                              </div>
                            </form>
                            <button type="submit" class="btn btn-outline-white">Change</button>
                          </div>
                        </div>
                      </div>
                      <div>
                        <!-- border -->
                        <div class="mb-6">
                          <h4 class="mb-1">Basic information</h4>

                        </div>
                        <form>
                          <!-- row -->

                          <div class="mb-3 row">
                            <label for="fullName" class="col-sm-4 col-form-label
                                form-label">Full name</label>
                            <div class="col-sm-4 mb-3 mb-lg-0">
                              <input type="text" class="form-control" placeholder="First name" id="fullName" required>
                            </div>
                            <div class="col-sm-4">
                              <input type="text" class="form-control" placeholder="Last name" id="lastName" required>
                            </div>
                          </div>

                          <!-- row -->
                          <div class="mb-3 row">
                            <label for="email" class="col-sm-4 col-form-label
                                form-label">Email</label>
                            <div class="col-md-8 col-12">
                              <input type="email" class="form-control" placeholder="Email" id="email" required>
                            </div>
                          </div>
                          <!-- row -->
                          <div class="mb-3 row">
                            <label for="phone" class="col-sm-4 col-form-label
                                form-label">Phone <span class="text-muted">(Optional)</span></label>
                            <div class="col-md-8 col-12">
                              <input type="text" class="form-control" placeholder="Phone" id="phone" required>
                            </div>
                          </div>
                          <!-- row -->
                          <div class="mb-3 row">
                            <label for="location" class="col-sm-4 col-form-label
                                form-label">Location</label>

                            <div class="col-md-8 col-12">
                              <select class="form-select" id="location">
                                  <option selected>Select Country</option>
                                  <option value="1">India</option>
                                  <option value="2">UK</option>
                                  <option value="3">USA</option>
                                </select>
                            </div>
                          </div>
                          <!-- row -->
                          <div class="mb-3 row">
                            <label for="addressLine" class="col-sm-4 col-form-label
                                form-label">Address line 1</label>


                            <div class="col-md-8 col-12">
                              <input type="text" class="form-control" placeholder="Placeholder" id="addressLine" required>
                            </div>
                          </div>
                          <!-- row -->
                          <div class="mb-3 row">
                            <label for="addressLineTwo" class="col-sm-4
                                col-form-label form-label">Address line 2</label>
                            <div class="col-md-8 col-12">
                              <input type="text" class="form-control" placeholder="Placeholder" id="addressLineTwo" required>
                            </div>
                          </div>
                          <!-- row -->
                          <div class="row align-items-center">
                            <label for="zipcode" class="col-sm-4 col-form-label
                                form-label">Zip code <i data-feather="info"
                                  class="me-2 icon-xs"></i></label>

                            <div class="col-md-8 col-12">
                              <input type="text" class="form-control" placeholder="Placeholder" id="zipcode" required>
                            </div>
                            <div class="offset-md-4 col-md-8 mt-4">
                              <button type="submit" class="btn btn-primary"> Save
                                  Changes</button>
                            </div>
                          </div>
                        </form>
                      </div>
                    </div>
                  </div>

                </div>
              </div>
              <div class="row mb-8">
                <div class="col-xl-3 col-lg-4 col-md-12 col-12">
                  <div class="mb-4 mb-lg-0">
                    <h4 class="mb-1">Email Setting</h4>
                    <p class="mb-0 fs-5 text-muted">Add an email settings to profile </p>
                  </div>

                </div>

                <div class="col-xl-9 col-lg-8 col-md-12 col-12">
                  <!-- card -->
                  <div class="card" id="edit">
                    <!-- card body -->
                    <div class="card-body">
                      <div class="mb-6">
                        <h4 class="mb-1">Email</h4>

                      </div>
                      <form>
                        <!-- row -->
                        <div class="mb-3 row">
                          <!-- label -->
                          <label for="newEmailAddress" class="col-sm-4
                              col-form-label form-label">New email</label>
                          <div class="col-md-8 col-12">
                            <!-- input -->
                            <input type="email" class="form-control" placeholder="Enter your email address" id="newEmailAddress" required>
                          </div>
                          <!-- button -->
                          <div class="offset-md-4 col-md-8 col-12 mt-3">
                            <button type="submit" class="btn btn-primary">Save
                                Changes</button>
                          </div>
                        </div>
                      </form>

                      <div class="mb-6 mt-6">
                        <h4 class="mb-1">Change your password</h4>

                      </div>
                      <form>
                        <!-- row -->
                        <div class="mb-3 row">
                          <label for="currentPassword" class="col-sm-4
                              col-form-label form-label">Current password</label>

                          <div class="col-md-8 col-12">
                            <input type="password" class="form-control" placeholder="Enter Current password" id="currentPassword" required>
                          </div>
                        </div>
                        <!-- row -->
                        <div class="mb-3 row">
                          <label for="currentNewPassword" class="col-sm-4
                              col-form-label form-label">New password</label>

                          <div class="col-md-8 col-12">
                            <input type="password" class="form-control" placeholder="Enter New password" id="currentNewPassword" required>
                          </div>
                        </div>
                        <!-- row -->
                        <div class="row align-items-center">
                          <label for="confirmNewpassword" class="col-sm-4
                              col-form-label form-label">Confirm new password</label>
                          <div class="col-md-8 col-12 mb-2 mb-lg-0">
                            <input type="password" class="form-control" placeholder="Confirm new password" id="confirmNewpassword" required>
                          </div>
                          <!-- list -->
                          <div class="offset-md-4 col-md-8 col-12 mt-4">
                            <h6 class="mb-1">Password requirements:</h6>
                            <p>Ensure that these requirements are met:</p>
                            <ul>
                              <li> Minimum 8 characters long the more, the better</li>
                              <li>At least one lowercase character</li>
                              <li>At least one uppercase character</li>
                              <li>At least one number, symbol, or whitespace character
                              </li>
                            </ul>
                            <button type="submit" class="btn btn-primary">Save
                                Changes</button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row mb-8">
                <div class="col-xl-3 col-lg-4 col-md-12 col-12">
                  <div class="mb-4 mb-lg-0">
                    <h4 class="mb-1">Preferences</h4>
                    <p class="mb-0 fs-5 text-muted">Configure your preferences </p>
                  </div>

                </div>

                <div class="col-xl-9 col-lg-8 col-md-12 col-12">
                  <div class="card" id="preferences">
                    <div class="card-body">
                      <div class="mb-6">
                        <h4 class="mb-1">Preferences</h4>

                      </div>
                      <form>
                        <!-- row -->
                        <div class="mb-3 row">
                          <label for="langauge" class="col-sm-4 col-form-label
                              form-label">Langauge</label>

                          <div class="col-md-8 col-12">
                            <select class="form-select" id="langauge">
                                <option selected>English</option>
                                <option value="1">Hindi</option>
                                <option value="2">Spanish</option>
                                <option value="3">Arabic </option>
                              </select>
                          </div>
                        </div>
                        <!-- row -->
                        <div class="mb-3 row">
                          <label for="timeZone" class="col-sm-4 col-form-label
                              form-label">Time Zone</label>

                          <div class="col-md-8 col-12">
                            <select class="form-select" id="timeZone">
                                <option selected>GMT +5.30</option>
                                <option value="1">GMT +5.30</option>
                                <option value="2">GMT +5.30</option>
                                <option value="3">GMT +5.30 </option>
                              </select>
                          </div>
                        </div>
                        <!-- row -->
                        <div class="mb-3 row">
                          <label for="dateFormat" class="col-sm-4 col-form-label
                              form-label">Date Format</label>

                          <div class="col-md-8 col-12">
                            <select class="form-select" id="dateFormat">
                                <option selected>No Preference</option>
                                <option value="Preference">Preference</option>
                              </select>
                          </div>
                        </div>

                        <!-- row -->
                        <div class="mb-3 row">
                          <label class="col-sm-4 col-form-label form-label">Default</label>
                          <div class="col-md-8 col-12">
                            <div class="form-check custom-radio
                                form-check-inline">
                              <input type="radio" id="customRadioInlineOn" name="customRadioInline" class="form-check-input">
                              <label class="form-check-label" for="customRadioInlineOn">On
                                </label>
                            </div>
                            <div class="form-check custom-radio
                                form-check-inline">
                              <input type="radio" id="customRadioInlineOff" name="customRadioInline" class="form-check-input">
                              <label class="form-check-label" for="customRadioInlineOff">Off</label>
                            </div>
                          </div>
                        </div>
                        <!-- row -->
                        <div class="mb-3 row">
                          <div class="col-md-4 col-12">
                            <label class="mb-0 form-label">Choose option default</label>
                          </div>
                          <div class="col-md-8 col-12">
                            <div class="form-check  mb-2">
                              <input type="checkbox" class="form-check-input" id="customChecktellMe">
                              <label class="form-check-label" for="customChecktellMe">Tell me</label>
                            </div>
                            <div class="form-check  mb-2">
                              <input type="checkbox" class="form-check-input" id="customCheckemail">
                              <label class="form-check-label" for="customCheckemail">Open e-mail</label>
                            </div>
                            <div class="form-check  mb-2">
                              <input type="checkbox" class="form-check-input" id="customCheckemailTwo" checked>
                              <label class="form-check-label" for="customCheckemailTwo">Show default</label>
                            </div>
                          </div>
                          <div class="offset-md-4 col-md-8 col-12 mt-2">
                            <button type="submit" class="btn btn-primary">Save
                                Changes</button>
                          </div>
                        </div>


                      </form>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row mb-8">
                <div class="col-xl-3 col-lg-4 col-md-12 col-12">
                  <div class="mb-4 mb-lg-0">
                    <h4 class="mb-1">Notifications</h4>
                    <p class="mb-0 fs-5 text-muted">Change notification settings </p>
                  </div>

                </div>

                <div class="col-xl-9 col-lg-8 col-md-12 col-12">
                  <!-- card -->

                  <div class="card">
                    <!-- card body -->
                    <div class="card-body">
                      <div class="mb-6">
                        <h4 class="mb-1">Notification for email, mobile & Slack</h4>
                      </div>
                      <div class="mb-4">
                        <!-- alert -->
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                          To start using Slack for personal notifications, you should first connect Slack.
                          <button type="submit" class="btn-close" data-bs-dismiss="alert" aria-label="Close">

              </button>
                        </div>
                      </div>
                      <!-- table -->
                      <div class="table-responsive mb-3">
                        <table class="table text-nowrap">
                          <thead class="table-light">
                            <tr>
                              <th class="w-75">Activity & Conversation</th>
                              <th><i data-feather="smartphone" class="icon-sm me-2"></i></th>
                              <th><i data-feather="slack" class="icon-sm me-2"></i></th>
                              <th><i data-feather="mail" class="icon-sm me-2"></i></th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>

                              <td class="border-top-0">

                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckOne">
                                  <label class="form-check-label" for="customCheckOne"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckTwo">
                                  <label class="form-check-label" for="customCheckTwo"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckThree">
                                  <label class="form-check-label" for="customCheckThree"></label>
                                </div>
                              </td>
                            </tr>
                            <tr>

                              <td class="border-top-0">
                                When a Files is shared with a team
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckFour">
                                  <label class="form-check-label" for="customCheckFour"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <i data-feather="minus" class="text-muted icon-sm"></i>
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckFive">
                                  <label class="form-check-label" for="customCheckFive"></label>
                                </div>
                              </td>
                            </tr>

                            <tr>

                              <td class="border-top-0">
                                When someone requests access to my design
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckSix">
                                  <label class="form-check-label" for="customCheckSix"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckSeven">
                                  <label class="form-check-label" for="customCheckSeven"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckEight">
                                  <label class="form-check-label" for="customCheckEight"></label>
                                </div>
                              </td>
                            </tr>
                            <tr>

                              <td class="border-top-0">
                                When someone comments in threads I’m following
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckNine">
                                  <label class="form-check-label" for="customCheckNine"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckTen">
                                  <label class="form-check-label" for="customCheckTen"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckEleven">
                                  <label class="form-check-label" for="customCheckEleven"></label>
                                </div>
                              </td>
                            </tr>
                            <tr>

                              <td class="border-top-0">
                                When someone @mentions me in any comments
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckTwelve">
                                  <label class="form-check-label" for="customCheckTwelve"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <i class="text-muted icon-sm" data-feather="minus"></i>
                              </td>
                              <td class="border-top-0">
                                <i class="text-muted icon-sm" data-feather="minus"></i>
                              </td>
                            </tr>

                          </tbody>
                        </table>
                      </div>
                      <div class="table-responsive mb-3">
                        <table class="table text-nowrap">
                          <thead class="table-light">
                            <tr>
                              <th class="w-75">Project activity activity</th>
                              <th><i data-feather="smartphone" class="me-2 icon-sm"></i></th>
                              <th><i data-feather="slack" class="me-2 icon-sm"></i></th>
                              <th><i data-feather="mail" class="me-2 icon-sm"></i></th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>

                              <td class="border-top-0">
                                When someone adds me to a project
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckThirteen">
                                  <label class="form-check-label" for="customCheckThirteen"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <i class="text-muted icon-sm" data-feather="minus"></i>
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckFourteen">
                                  <label class="form-check-label" for="customCheckFourteen"></label>
                                </div>
                              </td>
                            </tr>


                          </tbody>
                        </table>
                      </div>
                      <div class="table-responsive">
                        <table class="table mb-0 text-nowrap">
                          <thead class="table-light">
                            <tr>
                              <th class="w-75">Team activity
                              </th>
                              <th><i data-feather="smartphone" class="me-2 icon-sm"></i></th>
                              <th><i data-feather="slack" class="me-2 icon-sm"></i></th>
                              <th><i data-feather="mail" class="me-2 icon-sm"></i></th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>

                              <td class="border-top-0">
                                When my invitees sign up
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckSixteen">
                                  <label class="form-check-label" for="customCheckSixteen"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckSeventeen">
                                  <label class="form-check-label" for="customCheckSeventeen"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckEighteen">
                                  <label class="form-check-label" for="customCheckEighteen"></label>
                                </div>
                              </td>
                            </tr>
                            <tr>

                              <td class="border-top-0">
                                When someone requests access to my team
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckNineteen">
                                  <label class="form-check-label" for="customCheckNineteen"></label>
                                </div>
                              </td>
                              <td class="border-top-0">
                                <i data-feather="minus" class="text-muted icon-sm"></i>
                              </td>
                              <td class="border-top-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckTwenty">
                                  <label class="form-check-label" for="customCheckTwenty"></label>
                                </div>
                              </td>
                            </tr>

                            <tr>

                              <td class="border-bottom-0">
                                When someone invites me to a team
                              </td>
                              <td class="border-bottom-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckTwentyone">
                                  <label class="form-check-label" for="customCheckTwentyone"></label>
                                </div>
                              </td>
                              <td class="border-bottom-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckTwentytwo">
                                  <label class="form-check-label" for="customCheckTwentytwo"></label>
                                </div>
                              </td>
                              <td class="border-bottom-0">
                                <div class="form-check ">
                                  <input type="checkbox" class="form-check-input" id="customCheckTwentythree">
                                  <label class="form-check-label" for="customCheckTwentythree"></label>
                                </div>
                              </td>
                            </tr>


                          </tbody>
                        </table>
                      </div>
                      <hr class="my-6">
                      <div class="row">
                        <div class="col-xl-6 col-md-12 mb-3">
                          <label for="notification" class="form-label">When should
                we send you notifications?</label>
                          <select class="form-select" id="notification">
                <option selected>Always</option>
                <option value="1">One</option>
                <option value="2">Two</option>
                <option value="3">Three</option>
              </select>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                          <label for="dailyDigest" class="form-label">Daily Digest
              </label>
                          <select class="form-select" id="dailyDigest">
                <option selected>Everyday</option>
                <option value="1">One</option>
                <option value="2">Two</option>
                <option value="3">Three</option>
              </select>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                          <label for="time" class="form-label">Time</label>
                          <select class="form-select" id="time">
                <option selected>2PM
                </option>
                <option value="1">One</option>
                <option value="2">Two</option>
                <option value="3">Three</option>
              </select>
                        </div>
                        <div class="col-xl-3 col-md-12 ">
                          <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                      </div>


                    </div>

                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-xl-3 col-lg-4 col-md-12 col-12">
                  <div class="mb-4 mb-lg-0">
                    <h4 class="mb-1">Delete Account</h4>
                    <p class="mb-0 fs-5 text-muted">Easily set up social media accounts</p>
                  </div>

                </div>

                <div class="col-xl-9 col-lg-8 col-md-12 col-12">
                  <!-- card -->

                  <div class="card mb-6">
                    <!-- card body -->
                    <div class="card-body">
                      <div class="mb-6">
                        <h4 class="mb-1">Danger Zone </h4>

                      </div>
                      <div>
                        <!-- text -->
                        <p>Delete any and all content you have, such as articles, comments, your reading list or chat messages. Allow your username to become available to anyone.</p>
                        <a href="#" class="btn btn-danger">Delete Account</a>
                        <p class="small mb-0 mt-3">Feel free to contact with any <a href="#"><EMAIL></a> questions.
                        </p>
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
      </div>
    </div>
  </main>
    <!-- Scripts -->
    <script src="../assets/libs/dropzone/dist/min/dropzone.min.js"></script>
    <!-- Libs JS -->

<script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../assets/libs/feather-icons/dist/feather.min.js"></script>
<script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>

<!-- Theme JS -->
<script src="../assets/js/theme.min.js"></script>

  </body>


<!-- Mirrored from dashui.codescandy.com/dashuipro/horizontal/profile-settings.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:31:00 GMT -->
</html>