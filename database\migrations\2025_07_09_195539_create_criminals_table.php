<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('criminals', function (Blueprint $table) {
            $table->id();

            // Unique identifier
            $table->string('criminal_number', 20)->unique();
            $table->string('slug')->unique();

            // Personal Information
            $table->string('first_name', 100);
            $table->string('middle_name', 100)->nullable();
            $table->string('last_name', 100);
            $table->string('alias', 100)->nullable();
            $table->enum('gender', ['Male', 'Female', 'Other']);
            $table->date('date_of_birth')->nullable();
            $table->string('place_of_birth', 200)->nullable();
            $table->string('nationality', 100)->default('Malawian');
            $table->string('national_id', 20)->nullable()->unique();

            // Physical Description
            $table->decimal('height', 5, 2)->nullable(); // in meters
            $table->decimal('weight', 5, 2)->nullable(); // in kg
            $table->string('eye_color', 50)->nullable();
            $table->string('hair_color', 50)->nullable();
            $table->string('complexion', 50)->nullable();
            $table->text('distinguishing_marks')->nullable();
            $table->text('scars_tattoos')->nullable();

            // Contact Information
            $table->string('phone_number', 20)->nullable();
            $table->string('email', 100)->nullable();
            $table->text('current_address')->nullable();
            $table->text('permanent_address')->nullable();
            $table->string('district', 100)->nullable();
            $table->string('traditional_authority', 100)->nullable();
            $table->string('village', 100)->nullable();

            // Emergency Contact
            $table->string('emergency_contact_name', 200)->nullable();
            $table->string('emergency_contact_phone', 20)->nullable();
            $table->string('emergency_contact_relationship', 100)->nullable();

            // Criminal Status
            $table->enum('status', ['Active', 'Inactive', 'Deceased', 'Deported'])->default('Active');
            $table->enum('risk_level', ['Low', 'Medium', 'High', 'Critical'])->default('Low');
            $table->boolean('is_wanted')->default(false);
            $table->boolean('is_repeat_offender')->default(false);

            // Additional Information
            $table->string('occupation', 200)->nullable();
            $table->string('education_level', 100)->nullable();
            $table->string('marital_status', 50)->nullable();
            $table->text('known_associates')->nullable();
            $table->text('notes')->nullable();

            // Standard tracking fields
            $table->timestamps();
            $table->softDeletes();

            // User tracking fields (NEW REQUIREMENT)
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');

            // Indexes for performance
            $table->index(['criminal_number']);
            $table->index(['first_name', 'last_name']);
            $table->index(['national_id']);
            $table->index(['status']);
            $table->index(['risk_level']);
            $table->index(['is_wanted']);
            $table->index(['district']);
            $table->index(['created_by']);
            $table->index(['updated_by']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('criminals');
    }
};
