<?php

namespace App\Filament\Resources\CriminalResource\Pages;

use App\Filament\Resources\CriminalResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCriminals extends ListRecords
{
    protected static string $resource = CriminalResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
