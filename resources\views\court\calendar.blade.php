@extends('layouts.app')

@section('title', 'Court Calendar - ' . config('app.name'))

@push('styles')
<style>
    .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
        background-color: #e5e7eb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        overflow: hidden;
    }
    
    .calendar-day {
        background: white;
        min-height: 120px;
        padding: 0.5rem;
        position: relative;
        transition: all 0.3s ease;
    }
    
    .calendar-day:hover {
        background-color: rgba(30, 64, 175, 0.05);
    }
    
    .calendar-day.other-month {
        background-color: #f9fafb;
        color: #9ca3af;
    }
    
    .calendar-day.today {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
    }
    
    .calendar-event {
        background: rgba(30, 64, 175, 0.1);
        border-left: 3px solid #1e40af;
        padding: 0.25rem 0.5rem;
        margin: 0.25rem 0;
        border-radius: 4px;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .calendar-event:hover {
        background: rgba(30, 64, 175, 0.2);
        transform: translateX(2px);
    }
    
    .calendar-event.hearing {
        border-left-color: #1e40af;
        background: rgba(30, 64, 175, 0.1);
    }
    
    .calendar-event.meeting {
        border-left-color: #f59e0b;
        background: rgba(245, 158, 11, 0.1);
    }
    
    .calendar-header {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 1rem;
        text-align: center;
        font-weight: 600;
    }
    
    .calendar-weekdays {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        background: #f8fafc;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .calendar-weekday {
        padding: 0.75rem;
        text-align: center;
        font-weight: 600;
        color: #374151;
        border-right: 1px solid #e5e7eb;
    }
    
    .calendar-weekday:last-child {
        border-right: none;
    }
    
    .event-legend {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }
    
    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 4px;
    }
</style>
@endpush

@section('content')
    <!-- Branded Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <div class="icon-shape icon-lg bg-white bg-opacity-20 text-white rounded-3">
                                <i data-feather="calendar" class="icon-md"></i>
                            </div>
                        </div>
                        <div>
                            <h1 class="h2 mb-1">Court Calendar</h1>
                            <p class="mb-0 opacity-75">View and manage court schedules and events</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex flex-wrap gap-2 justify-content-md-end">
                        <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addEventModal">
                            <i data-feather="plus" class="icon-xs me-2"></i>
                            Add Event
                        </button>
                        <button type="button" class="btn btn-outline-light">
                            <i data-feather="download" class="icon-xs me-2"></i>
                            Export Calendar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Navigation -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="d-flex align-items-center gap-3">
                <button class="btn btn-outline-primary" onclick="previousMonth()">
                    <i data-feather="chevron-left" class="icon-xs"></i>
                </button>
                <h4 class="mb-0" id="currentMonth">{{ now()->format('F Y') }}</h4>
                <button class="btn btn-outline-primary" onclick="nextMonth()">
                    <i data-feather="chevron-right" class="icon-xs"></i>
                </button>
            </div>
        </div>
        <div class="col-md-6">
            <div class="event-legend justify-content-md-end">
                <div class="legend-item">
                    <div class="legend-color" style="background: rgba(30, 64, 175, 0.2); border-left: 3px solid #1e40af;"></div>
                    <span>Court Hearings</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: rgba(245, 158, 11, 0.2); border-left: 3px solid #f59e0b;"></div>
                    <span>Meetings</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar -->
    <div class="card">
        <div class="calendar-header">
            Court Calendar - {{ now()->format('F Y') }}
        </div>
        <div class="calendar-weekdays">
            <div class="calendar-weekday">Sunday</div>
            <div class="calendar-weekday">Monday</div>
            <div class="calendar-weekday">Tuesday</div>
            <div class="calendar-weekday">Wednesday</div>
            <div class="calendar-weekday">Thursday</div>
            <div class="calendar-weekday">Friday</div>
            <div class="calendar-weekday">Saturday</div>
        </div>
        <div class="calendar-grid">
            @php
                $startOfMonth = now()->startOfMonth();
                $endOfMonth = now()->endOfMonth();
                $startOfCalendar = $startOfMonth->copy()->startOfWeek();
                $endOfCalendar = $endOfMonth->copy()->endOfWeek();
                $currentDate = $startOfCalendar->copy();
            @endphp
            
            @while($currentDate <= $endOfCalendar)
                <div class="calendar-day {{ $currentDate->month !== now()->month ? 'other-month' : '' }} {{ $currentDate->isToday() ? 'today' : '' }}">
                    <div class="fw-bold mb-1">{{ $currentDate->day }}</div>
                    
                    @foreach($events as $event)
                        @if($event['start'] === $currentDate->format('Y-m-d'))
                            <div class="calendar-event {{ $event['type'] }}" 
                                 data-bs-toggle="tooltip" 
                                 title="{{ $event['title'] }} - {{ $event['time'] }} ({{ $event['court_room'] }})">
                                <div class="fw-bold">{{ $event['time'] }}</div>
                                <div>{{ Str::limit($event['title'], 20) }}</div>
                                @if($event['court_room'])
                                    <small class="text-muted">{{ $event['court_room'] }}</small>
                                @endif
                            </div>
                        @endif
                    @endforeach
                </div>
                @php $currentDate->addDay(); @endphp
            @endwhile
        </div>
    </div>

    <!-- Upcoming Events -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="clock" class="icon-sm me-2"></i>
                        Upcoming Events
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        @foreach($events->sortBy('start')->take(5) as $event)
                            <div class="list-group-item px-0">
                                <div class="d-flex align-items-start">
                                    <div class="me-3">
                                        <div class="icon-shape icon-sm {{ $event['type'] === 'hearing' ? 'bg-primary' : 'bg-warning' }} text-white rounded">
                                            <i data-feather="{{ $event['type'] === 'hearing' ? 'calendar' : 'users' }}" class="icon-xs"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ $event['title'] }}</h6>
                                        <p class="mb-1 text-muted">
                                            {{ \Carbon\Carbon::parse($event['start'])->format('l, F j, Y') }} at {{ $event['time'] }}
                                        </p>
                                        <small class="text-muted">
                                            <i data-feather="map-pin" class="icon-xs me-1"></i>
                                            {{ $event['court_room'] }}
                                            @if($event['case_number'])
                                                | Case: {{ $event['case_number'] }}
                                            @endif
                                        </small>
                                    </div>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary">
                                            <i data-feather="eye" class="icon-xs"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="bar-chart" class="icon-sm me-2"></i>
                        Calendar Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span>This Month's Hearings</span>
                            <strong>{{ $events->where('type', 'hearing')->count() }}</strong>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-primary" style="width: 75%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span>Meetings</span>
                            <strong>{{ $events->where('type', 'meeting')->count() }}</strong>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-warning" style="width: 25%"></div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <h4 class="text-primary">{{ $events->count() }}</h4>
                        <p class="mb-0 text-muted">Total Events This Month</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Event Modal -->
    <div class="modal fade" id="addEventModal" tabindex="-1" aria-labelledby="addEventModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addEventModalLabel">
                        <i data-feather="plus" class="icon-sm me-2"></i>
                        Add Calendar Event
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="eventTitle" class="form-label">Event Title</label>
                            <input type="text" class="form-control" id="eventTitle" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="eventDate" class="form-label">Date</label>
                                    <input type="date" class="form-control" id="eventDate" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="eventTime" class="form-label">Time</label>
                                    <input type="time" class="form-control" id="eventTime" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="eventType" class="form-label">Event Type</label>
                            <select class="form-select" id="eventType" required>
                                <option value="">Select Type</option>
                                <option value="hearing">Court Hearing</option>
                                <option value="meeting">Meeting</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="courtRoom" class="form-label">Court Room</label>
                            <select class="form-select" id="courtRoom">
                                <option value="">Select Court Room</option>
                                <option value="Court Room 1">Court Room 1</option>
                                <option value="Court Room 2">Court Room 2</option>
                                <option value="Court Room 3">Court Room 3</option>
                                <option value="Conference Room">Conference Room</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="caseNumber" class="form-label">Case Number (Optional)</label>
                            <input type="text" class="form-control" id="caseNumber" placeholder="CR/2024/001">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Add Event</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function previousMonth() {
    // Implementation for previous month navigation
    console.log('Previous month');
}

function nextMonth() {
    // Implementation for next month navigation
    console.log('Next month');
}
</script>
@endpush
