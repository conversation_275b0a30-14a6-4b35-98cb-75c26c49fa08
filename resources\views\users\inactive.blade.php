@extends('layouts.app')

@section('title', 'Inactive Users - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Inactive Users</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'User Management', 'url' => route('users.index')],
                ['title' => 'Inactive Users']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('users.create') }}" class="btn btn-primary">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Add New User
            </a>
        </div>
    </div>

    <!-- Inactive Users Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning">8</h4>
                    <p class="mb-0">Inactive Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-danger">3</h4>
                    <p class="mb-0">Suspended</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-secondary">5</h4>
                    <p class="mb-0">Deactivated</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-info">2</h4>
                    <p class="mb-0">Pending Activation</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Inactive Users List -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Inactive System Users</h4>
            <div class="d-flex gap-2">
                <a href="{{ route('users.index') }}" class="btn btn-sm btn-outline-secondary">
                    <i data-feather="users" class="icon-xs me-2"></i>
                    All Users
                </a>
                <a href="{{ route('users.active') }}" class="btn btn-sm btn-outline-success">
                    <i data-feather="user-check" class="icon-xs me-2"></i>
                    Active Users
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Role</th>
                            <th>Department</th>
                            <th>Last Login</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="table-warning">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-secondary text-white me-3">MK</div>
                                    <div>
                                        <strong>Constable Mwawi Kumwenda</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-success">Constable</span></td>
                            <td>Administration</td>
                            <td>
                                10/12/2024 03:15 PM
                                <br><small class="text-muted">5 days ago</small>
                            </td>
                            <td><span class="badge bg-warning">Suspended</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('users.show', 4) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('users.edit', 4) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit User
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-success" href="#!">
                                            <i data-feather="user-check" class="icon-xs me-2"></i>Activate
                                        </a></li>
                                        <li><a class="dropdown-item text-info" href="#!">
                                            <i data-feather="key" class="icon-xs me-2"></i>Reset Password
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr class="table-secondary">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-secondary text-white me-3">JN</div>
                                    <div>
                                        <strong>Sergeant John Nyirenda</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-info">Sergeant</span></td>
                            <td>Forensics</td>
                            <td>
                                05/12/2024 11:30 AM
                                <br><small class="text-muted">10 days ago</small>
                            </td>
                            <td><span class="badge bg-secondary">Deactivated</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('users.show', 5) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('users.edit', 5) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit User
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-success" href="#!">
                                            <i data-feather="user-check" class="icon-xs me-2"></i>Reactivate
                                        </a></li>
                                        <li><a class="dropdown-item text-danger" href="#!">
                                            <i data-feather="trash-2" class="icon-xs me-2"></i>Delete User
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr class="table-info">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-info text-white me-3">LM</div>
                                    <div>
                                        <strong>Constable Lucy Mbewe</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                        <span class="badge bg-info ms-2">New User</span>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-success">Constable</span></td>
                            <td>Community Policing</td>
                            <td>
                                <span class="text-muted">Never logged in</span>
                            </td>
                            <td><span class="badge bg-info">Pending Activation</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('users.show', 6) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('users.edit', 6) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit User
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-success" href="#!">
                                            <i data-feather="user-check" class="icon-xs me-2"></i>Activate
                                        </a></li>
                                        <li><a class="dropdown-item text-info" href="#!">
                                            <i data-feather="mail" class="icon-xs me-2"></i>Send Welcome Email
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
@endpush
