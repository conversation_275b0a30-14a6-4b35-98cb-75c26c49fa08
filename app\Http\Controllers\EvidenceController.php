<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class EvidenceController extends Controller
{
    /**
     * Display a listing of the evidence.
     */
    public function index()
    {
        return view('evidence.index');
    }

    /**
     * Show the form for creating a new evidence.
     */
    public function create()
    {
        return view('evidence.create');
    }

    /**
     * Store a newly created evidence in storage.
     */
    public function store(Request $request)
    {
        // Validate the request
        $request->validate([
            'evidence_id' => 'required|string|max:255|unique:evidence,evidence_id',
            'case_number' => 'required|string|max:255',
            'category' => 'required|string|max:255',
            'description' => 'required|string',
            'collection_date' => 'required|date',
            'collector_name' => 'required|string|max:255',
            'storage_location' => 'required|string|max:255',
        ]);

        // For now, just redirect back with success message
        // In a real application, you would save to database here

        return redirect()->route('evidence.index')->with('success', 'Evidence item added successfully!');
    }

    /**
     * Display the specified evidence.
     */
    public function show(string $id)
    {
        return view('evidence.show', compact('id'));
    }

    /**
     * Show the form for editing the specified evidence.
     */
    public function edit(string $id)
    {
        return view('evidence.edit', compact('id'));
    }

    /**
     * Update the specified evidence in storage.
     */
    public function update(Request $request, string $id)
    {
        // Validate and update evidence
        return redirect()->route('evidence.index')->with('success', 'Evidence item updated successfully!');
    }

    /**
     * Remove the specified evidence from storage.
     */
    public function destroy(string $id)
    {
        return redirect()->route('evidence.index')->with('success', 'Evidence item deleted successfully!');
    }

    /**
     * Display chain of custody for evidence.
     */
    public function custody()
    {
        return view('evidence.custody');
    }

    /**
     * Display forensic analysis page.
     */
    public function forensic()
    {
        return view('evidence.forensic');
    }
}
