<?php

namespace App\Models;

use App\Traits\HasUserTracking;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Cases extends Model implements HasMedia
{
    use HasFactory;
    use HasUserTracking;
    use LogsActivity;
    use InteractsWithMedia;
    use HasSlug;
    use SoftDeletes;

    protected $fillable = [
        'case_number',
        'title',
        'description',
        'type',
        'severity',
        'priority',
        'incident_location',
        'district',
        'traditional_authority',
        'village',
        'latitude',
        'longitude',
        'incident_date',
        'reported_date',
        'investigation_started_date',
        'reporting_officer_id',
        'investigating_officer_id',
        'supervisor_id',
        'complainant_name',
        'complainant_phone',
        'complainant_email',
        'complainant_address',
        'complainant_id_number',
        'status',
        'progress_percentage',
        'status_notes',
        'court_case_filed',
        'court_case_number',
        'court_name',
        'court_date',
        'estimated_loss',
        'recovered_amount',
        'currency',
        'modus_operandi',
        'evidence_summary',
        'witness_summary',
        'investigation_notes',
        'media_attention',
        'high_profile',
        'closed_date',
        'closure_reason',
        'closure_notes',
    ];

    protected $casts = [
        'incident_date' => 'datetime',
        'reported_date' => 'datetime',
        'investigation_started_date' => 'datetime',
        'court_date' => 'date',
        'closed_date' => 'date',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'estimated_loss' => 'decimal:2',
        'recovered_amount' => 'decimal:2',
        'progress_percentage' => 'integer',
        'court_case_filed' => 'boolean',
        'media_attention' => 'boolean',
        'high_profile' => 'boolean',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('case_number')
            ->saveSlugsTo('slug');
    }

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'case_number', 'title', 'type', 'status', 'priority',
                'investigating_officer_id', 'progress_percentage'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Register media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('case_files')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png', 'application/msword']);

        $this->addMediaCollection('crime_scene_photos')
            ->acceptsMimeTypes(['image/jpeg', 'image/png']);

        $this->addMediaCollection('witness_statements')
            ->acceptsMimeTypes(['application/pdf', 'application/msword']);
    }

    /**
     * Register media conversions.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(300)
            ->sharpen(10);

        $this->addMediaConversion('preview')
            ->width(800)
            ->height(600)
            ->quality(90);
    }

    // Relationships
    public function reportingOfficer()
    {
        return $this->belongsTo(User::class, 'reporting_officer_id');
    }

    public function investigatingOfficer()
    {
        return $this->belongsTo(User::class, 'investigating_officer_id');
    }

    public function supervisor()
    {
        return $this->belongsTo(User::class, 'supervisor_id');
    }

    public function evidence()
    {
        return $this->hasMany(Evidence::class, 'case_id');
    }

    public function criminals()
    {
        return $this->belongsToMany(Criminal::class, 'case_criminals')
                    ->withPivot(['role', 'arrest_date', 'charges'])
                    ->withTimestamps();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['Case Closed', 'Transferred']);
    }

    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['High', 'Urgent']);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByOfficer($query, $officerId)
    {
        return $query->where('investigating_officer_id', $officerId);
    }

    // Accessors
    public function getDaysOpenAttribute()
    {
        $endDate = $this->closed_date ?? now();
        return $this->reported_date->diffInDays($endDate);
    }

    public function getIsOverdueAttribute()
    {
        if ($this->status === 'Case Closed') {
            return false;
        }

        // Consider a case overdue if it's been open for more than 30 days
        return $this->days_open > 30;
    }
}
