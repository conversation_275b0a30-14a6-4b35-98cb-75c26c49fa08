@extends('layouts.app')

@section('title', 'My Profile - ' . config('app.name'))

@push('styles')
<style>
    .profile-header {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .profile-avatar {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        font-weight: bold;
        color: white;
        margin: 0 auto;
        position: relative;
        z-index: 1;
    }

    .status-indicator {
        position: absolute;
        bottom: 5px;
        right: 5px;
        width: 20px;
        height: 20px;
        border: 3px solid white;
        border-radius: 50%;
        background-color: #28a745;
    }

    .profile-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }

    .profile-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    }

    .info-item {
        border-bottom: 1px solid #eee;
        padding: 1rem 0;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .btn-action {
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .nav-pills .nav-link {
        border-radius: 10px;
        margin-right: 0.5rem;
        transition: all 0.3s ease;
    }

    .nav-pills .nav-link.active {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    }
</style>
@endpush

@section('content')
    <!-- Success/Error Messages -->
    @if(session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i data-feather="check-circle" class="icon-xs me-2"></i>
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i data-feather="alert-circle" class="icon-xs me-2"></i>
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Profile Header -->
    <div class="profile-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-3 text-center">
                    <div class="position-relative d-inline-block">
                        @php
                            $initials = collect(explode(' ', auth()->user()->name))->map(fn($name) => strtoupper(substr($name, 0, 1)))->take(2)->implode('');
                        @endphp
                        <div class="profile-avatar rounded-circle">
                            {{ $initials }}
                        </div>
                        <div class="status-indicator rounded-circle"></div>
                    </div>
                </div>
                <div class="col-md-9">
                    <h1 class="h2 mb-2">{{ auth()->user()->name }}</h1>
                    <p class="mb-2 opacity-75">{{ auth()->user()->email }}</p>
                    @if(auth()->user()->department)
                        <p class="mb-2 opacity-75">{{ auth()->user()->department }}</p>
                    @endif
                    <div class="d-flex flex-wrap gap-2">
                        @if(auth()->user()->roles->isNotEmpty())
                            @foreach(auth()->user()->roles as $role)
                                <span class="badge bg-light text-dark">{{ $role->name }}</span>
                            @endforeach
                        @endif
                        <span class="badge bg-success">{{ ucfirst(auth()->user()->status ?? 'active') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Navigation Tabs -->
        <ul class="nav nav-pills mb-4" id="profileTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="personal-tab" data-bs-toggle="pill" data-bs-target="#personal" type="button" role="tab">
                    <i data-feather="user" class="icon-xs me-2"></i>
                    Personal Information
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="security-tab" data-bs-toggle="pill" data-bs-target="#security" type="button" role="tab">
                    <i data-feather="shield" class="icon-xs me-2"></i>
                    Security Settings
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="preferences-tab" data-bs-toggle="pill" data-bs-target="#preferences" type="button" role="tab">
                    <i data-feather="settings" class="icon-xs me-2"></i>
                    Preferences
                </button>
            </li>
        </ul>

        <div class="tab-content" id="profileTabsContent">
            <!-- Personal Information Tab -->
            <div class="tab-pane fade show active" id="personal" role="tabpanel">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card profile-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i data-feather="user" class="icon-sm me-2"></i>
                                    Personal Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="{{ route('user-profile-information.update') }}">
                                    @csrf
                                    @method('PUT')

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label fw-semibold">Full Name</label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                   value="{{ old('name', auth()->user()->name) }}" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label fw-semibold">Email Address</label>
                                            <input type="email" class="form-control" id="email" name="email"
                                                   value="{{ old('email', auth()->user()->email) }}" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label fw-semibold">Phone Number</label>
                                            <input type="text" class="form-control" id="phone" name="phone"
                                                   value="{{ old('phone', auth()->user()->phone) }}"
                                                   placeholder="+265 999 123 456">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="badge_number" class="form-label fw-semibold">Badge Number</label>
                                            <input type="text" class="form-control" id="badge_number" name="badge_number"
                                                   value="{{ old('badge_number', auth()->user()->badge_number) }}"
                                                   placeholder="PB001">
                                        </div>
                                        <div class="col-md-12 mb-3">
                                            <label for="department" class="form-label fw-semibold">Department</label>
                                            <select class="form-select" id="department" name="department">
                                                <option value="">Select Department</option>
                                                <option value="Criminal Investigation" {{ auth()->user()->department === 'Criminal Investigation' ? 'selected' : '' }}>Criminal Investigation</option>
                                                <option value="Traffic Police" {{ auth()->user()->department === 'Traffic Police' ? 'selected' : '' }}>Traffic Police</option>
                                                <option value="Forensics" {{ auth()->user()->department === 'Forensics' ? 'selected' : '' }}>Forensics</option>
                                                <option value="Administration" {{ auth()->user()->department === 'Administration' ? 'selected' : '' }}>Administration</option>
                                                <option value="Community Policing" {{ auth()->user()->department === 'Community Policing' ? 'selected' : '' }}>Community Policing</option>
                                                <option value="Special Operations" {{ auth()->user()->department === 'Special Operations' ? 'selected' : '' }}>Special Operations</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary btn-action">
                                            <i data-feather="save" class="icon-xs me-2"></i>
                                            Update Information
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card profile-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i data-feather="info" class="icon-sm me-2"></i>
                                    Account Details
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="info-item">
                                    <label class="form-label fw-bold text-muted small">Account Created</label>
                                    <p class="mb-0">{{ auth()->user()->created_at->format('d/m/Y H:i') }}</p>
                                </div>
                                <div class="info-item">
                                    <label class="form-label fw-bold text-muted small">Last Updated</label>
                                    <p class="mb-0">{{ auth()->user()->updated_at->format('d/m/Y H:i') }}</p>
                                </div>
                                <div class="info-item">
                                    <label class="form-label fw-bold text-muted small">Last Login</label>
                                    <p class="mb-0">
                                        @if(auth()->user()->last_login)
                                            {{ auth()->user()->last_login->format('d/m/Y H:i') }}
                                            <small class="text-muted d-block">{{ auth()->user()->last_login->diffForHumans() }}</small>
                                        @else
                                            <span class="text-muted">Never</span>
                                        @endif
                                    </p>
                                </div>
                                <div class="info-item">
                                    <label class="form-label fw-bold text-muted small">Email Verified</label>
                                    <p class="mb-0">
                                        @if(auth()->user()->email_verified_at)
                                            <span class="badge bg-success">Verified</span>
                                        @else
                                            <span class="badge bg-warning">Not Verified</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Settings Tab -->
            <div class="tab-pane fade" id="security" role="tabpanel">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card profile-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i data-feather="lock" class="icon-sm me-2"></i>
                                    Change Password
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="{{ route('user-password.update') }}">
                                    @csrf
                                    @method('PUT')

                                    <div class="mb-3">
                                        <label for="current_password" class="form-label fw-semibold">Current Password</label>
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password" class="form-label fw-semibold">New Password</label>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password_confirmation" class="form-label fw-semibold">Confirm New Password</label>
                                        <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary btn-action">
                                            <i data-feather="shield" class="icon-xs me-2"></i>
                                            Update Password
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card profile-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i data-feather="smartphone" class="icon-sm me-2"></i>
                                    Active Sessions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="me-3">
                                        <i data-feather="monitor" class="text-success"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">Current Session</h6>
                                        <small class="text-muted">{{ request()->ip() }} - {{ request()->userAgent() }}</small>
                                    </div>
                                    <span class="badge bg-success">Active</span>
                                </div>

                                <div class="d-grid">
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="logoutOtherSessions()">
                                        <i data-feather="log-out" class="icon-xs me-2"></i>
                                        Logout Other Sessions
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preferences Tab -->
            <div class="tab-pane fade" id="preferences" role="tabpanel">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card profile-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i data-feather="settings" class="icon-sm me-2"></i>
                                    Application Preferences
                                </h5>
                            </div>
                            <div class="card-body">
                                <form>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="language" class="form-label fw-semibold">Language</label>
                                            <select class="form-select" id="language">
                                                <option value="en" selected>English</option>
                                                <option value="ny">Chichewa</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="timezone" class="form-label fw-semibold">Timezone</label>
                                            <select class="form-select" id="timezone">
                                                <option value="Africa/Blantyre" selected>Africa/Blantyre (CAT)</option>
                                                <option value="UTC">UTC</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="date_format" class="form-label fw-semibold">Date Format</label>
                                            <select class="form-select" id="date_format">
                                                <option value="d/m/Y" selected>DD/MM/YYYY</option>
                                                <option value="m/d/Y">MM/DD/YYYY</option>
                                                <option value="Y-m-d">YYYY-MM-DD</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="theme" class="form-label fw-semibold">Theme</label>
                                            <select class="form-select" id="theme">
                                                <option value="light" selected>Light</option>
                                                <option value="dark">Dark</option>
                                                <option value="auto">Auto</option>
                                            </select>
                                        </div>
                                    </div>

                                    <h6 class="mt-4 mb-3">Notifications</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="email_notifications" checked>
                                                <label class="form-check-label" for="email_notifications">
                                                    Email Notifications
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="case_updates" checked>
                                                <label class="form-check-label" for="case_updates">
                                                    Case Updates
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="system_alerts" checked>
                                                <label class="form-check-label" for="system_alerts">
                                                    System Alerts
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="security_alerts" checked>
                                                <label class="form-check-label" for="security_alerts">
                                                    Security Alerts
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary btn-action">
                                            <i data-feather="save" class="icon-xs me-2"></i>
                                            Save Preferences
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card profile-card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h5 class="mb-0">
                                    <i data-feather="alert-triangle" class="icon-sm me-2"></i>
                                    Danger Zone
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-3">
                                    Once you delete your account, all of its resources and data will be permanently deleted.
                                </p>
                                <button type="button" class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
                                    <i data-feather="trash-2" class="icon-xs me-2"></i>
                                    Delete Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logout Other Sessions Modal -->
    <div class="modal fade" id="logoutSessionsModal" tabindex="-1" aria-labelledby="logoutSessionsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="logoutSessionsModalLabel">
                        <i data-feather="log-out" class="icon-sm me-2"></i>
                        Logout Other Sessions
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.</p>
                    <form id="logoutSessionsForm" method="POST" action="{{ route('other-browser-sessions.destroy') }}">
                        @csrf
                        @method('DELETE')

                        <div class="mb-3">
                            <label for="password_sessions" class="form-label">Password:</label>
                            <input type="password" class="form-control" id="password_sessions" name="password" required>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-warning">Logout Other Sessions</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Account Modal -->
    <div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteAccountModalLabel">
                        <i data-feather="alert-triangle" class="icon-sm me-2"></i>
                        Delete Account
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete your account? This action cannot be undone.</p>
                    <form method="POST" action="{{ route('current-user.destroy') }}">
                        @csrf
                        @method('DELETE')

                        <div class="mb-3">
                            <label for="password_delete" class="form-label">Enter your password to confirm:</label>
                            <input type="password" class="form-control" id="password_delete" name="password" required>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-danger">Delete Account</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});

function logoutOtherSessions() {
    const modal = new bootstrap.Modal(document.getElementById('logoutSessionsModal'));
    modal.show();
}
</script>
@endpush
