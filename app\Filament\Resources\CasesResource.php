<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CasesResource\Pages;
use App\Filament\Resources\CasesResource\RelationManagers;
use App\Models\Cases;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CasesResource extends Resource
{
    protected static ?string $model = Cases::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('case_number')
                    ->required()
                    ->maxLength(30),
                Forms\Components\TextInput::make('slug')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(300),
                Forms\Components\Textarea::make('description')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('type')
                    ->required(),
                Forms\Components\TextInput::make('severity')
                    ->required(),
                Forms\Components\TextInput::make('priority')
                    ->required(),
                Forms\Components\Textarea::make('incident_location')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('district')
                    ->required()
                    ->maxLength(100),
                Forms\Components\TextInput::make('traditional_authority')
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\TextInput::make('village')
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\TextInput::make('latitude')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('longitude')
                    ->numeric()
                    ->default(null),
                Forms\Components\DateTimePicker::make('incident_date')
                    ->required(),
                Forms\Components\DateTimePicker::make('reported_date')
                    ->required(),
                Forms\Components\DateTimePicker::make('investigation_started_date'),
                Forms\Components\TextInput::make('reporting_officer_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('investigating_officer_id')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('supervisor_id')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('complainant_name')
                    ->required()
                    ->maxLength(200),
                Forms\Components\TextInput::make('complainant_phone')
                    ->tel()
                    ->maxLength(20)
                    ->default(null),
                Forms\Components\TextInput::make('complainant_email')
                    ->email()
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\Textarea::make('complainant_address')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('complainant_id_number')
                    ->maxLength(20)
                    ->default(null),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\TextInput::make('progress_percentage')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\Textarea::make('status_notes')
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('court_case_filed')
                    ->required(),
                Forms\Components\TextInput::make('court_case_number')
                    ->maxLength(50)
                    ->default(null),
                Forms\Components\TextInput::make('court_name')
                    ->maxLength(200)
                    ->default(null),
                Forms\Components\DatePicker::make('court_date'),
                Forms\Components\TextInput::make('estimated_loss')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('recovered_amount')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('currency')
                    ->required()
                    ->maxLength(10)
                    ->default('MWK'),
                Forms\Components\Textarea::make('modus_operandi')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('evidence_summary')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('witness_summary')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('investigation_notes')
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('media_attention')
                    ->required(),
                Forms\Components\Toggle::make('high_profile')
                    ->required(),
                Forms\Components\DatePicker::make('closed_date'),
                Forms\Components\TextInput::make('closure_reason'),
                Forms\Components\Textarea::make('closure_notes')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('created_by')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('updated_by')
                    ->numeric()
                    ->default(null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('case_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('slug')
                    ->searchable(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type'),
                Tables\Columns\TextColumn::make('severity'),
                Tables\Columns\TextColumn::make('priority'),
                Tables\Columns\TextColumn::make('district')
                    ->searchable(),
                Tables\Columns\TextColumn::make('traditional_authority')
                    ->searchable(),
                Tables\Columns\TextColumn::make('village')
                    ->searchable(),
                Tables\Columns\TextColumn::make('latitude')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('longitude')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('incident_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reported_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('investigation_started_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reporting_officer_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('investigating_officer_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('supervisor_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('complainant_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('complainant_phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('complainant_email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('complainant_id_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('progress_percentage')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('court_case_filed')
                    ->boolean(),
                Tables\Columns\TextColumn::make('court_case_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('court_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('court_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('estimated_loss')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('recovered_amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('currency')
                    ->searchable(),
                Tables\Columns\IconColumn::make('media_attention')
                    ->boolean(),
                Tables\Columns\IconColumn::make('high_profile')
                    ->boolean(),
                Tables\Columns\TextColumn::make('closed_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('closure_reason'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_by')
                    ->numeric()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCases::route('/'),
            'create' => Pages\CreateCases::route('/create'),
            'edit' => Pages\EditCases::route('/{record}/edit'),
        ];
    }
}
