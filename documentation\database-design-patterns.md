# Database Design Patterns - Criminal Management System

## Overview
This document outlines the standardized database design patterns that must be implemented across all important tables in the Criminal Management System for audit trails, security, and compliance.

## Standard Timestamp and User Tracking Fields

### Required Fields for All Important Tables
All important tables (excluding pivot tables and simple lookup tables) must include these fields:

```sql
-- Standard Laravel timestamps
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

-- User tracking fields (NEW REQUIREMENT)
created_by BIGINT UNSIGNED NULL,
updated_by BIGINT UNSIGNED NULL,

-- Foreign key constraints
FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
```

### HasUserTracking Trait Implementation

#### Trait Location
`app/Traits/HasUserTracking.php`

#### Trait Features
- **Automatic Population**: Automatically sets `created_by` and `updated_by` on model creation/update
- **Relationship Methods**: Provides `creator()` and `updater()` relationships
- **Query Scopes**: Includes `createdBy()` and `updatedBy()` scopes for filtering
- **Auth Integration**: Uses Laravel's Auth facade to get current user ID

#### Usage in Models
```php
<?php

namespace App\Models;

use App\Traits\HasUserTracking;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Criminal extends Model implements HasMedia
{
    use HasUserTracking, LogsActivity, InteractsWithMedia;

    protected $fillable = [
        'criminal_number', 'first_name', 'last_name',
        // ... other fields
        // Note: created_by and updated_by are handled by the trait
    ];

    // Relationships provided by HasUserTracking trait:
    // - creator() - returns User who created the record
    // - updater() - returns User who last updated the record
}
```

## Tables Requiring User Tracking

### Core Criminal Justice Tables
1. **criminals** - Criminal profile records
2. **criminal_arrests** - Arrest records
3. **criminal_biometrics** - Biometric data
4. **criminal_relationships** - Criminal associations
5. **criminal_medical_info** - Medical information
6. **cases** - Criminal cases
7. **evidence** - Evidence records
8. **chain_of_custody** - Evidence custody tracking
9. **personal_belongings** - Personal property
10. **forensic_analysis** - Forensic analysis results
11. **court_cases** - Court proceedings
12. **court_hearings** - Court hearing records
13. **court_outcomes** - Court verdicts and sentences
14. **bail_information** - Bail records
15. **parole_information** - Parole records

### Administrative Tables
16. **documents** - Document management
17. **case_documents** - Case-document associations
18. **investigation_tasks** - Investigation assignments
19. **case_notes** - Case notes and updates
20. **evidence_transfers** - Evidence transfer records

## Migration Pattern Example

### Standard Migration Template
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('criminals', function (Blueprint $table) {
            $table->id();

            // Business logic fields
            $table->string('criminal_number', 20)->unique();
            $table->string('first_name', 100);
            $table->string('last_name', 100);
            // ... other business fields

            // Standard tracking fields
            $table->timestamps(); // created_at, updated_at

            // User tracking fields (NEW REQUIREMENT)
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');

            // Indexes for performance
            $table->index(['created_by']);
            $table->index(['updated_by']);
            $table->index(['created_at']);
            $table->index(['updated_at']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('criminals');
    }
};
```

## Benefits of User Tracking Pattern

### 1. Audit Trail Compliance
- **Legal Requirements**: Meets criminal justice audit requirements
- **Chain of Custody**: Essential for evidence handling
- **Accountability**: Clear responsibility for data changes
- **Forensic Analysis**: Can trace all data modifications

### 2. Security and Access Control
- **User Activity Monitoring**: Track who accessed what data
- **Suspicious Activity Detection**: Identify unusual access patterns
- **Data Integrity**: Verify data modification legitimacy
- **Compliance Reporting**: Generate audit reports for oversight

### 3. Operational Benefits
- **Data Quality**: Identify users who need training
- **Performance Tracking**: Monitor user productivity
- **Error Tracking**: Trace data entry errors to source
- **Workflow Analysis**: Understand data flow patterns

## Implementation Guidelines

### 1. Model Implementation
```php
// Always use the trait in important models
use App\Traits\HasUserTracking;

class YourModel extends Model
{
    use HasUserTracking;

    // The trait will automatically handle created_by and updated_by
}
```

### 2. Migration Implementation
```php
// Always add these fields to important tables
$table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
$table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
```

### 3. Query Examples
```php
// Find records created by specific user
$records = Criminal::createdBy($userId)->get();

// Find records updated by specific user
$records = Criminal::updatedBy($userId)->get();

// Get creator information
$criminal = Criminal::find(1);
$creator = $criminal->creator; // Returns User model

// Get last updater information
$lastUpdater = $criminal->updater; // Returns User model
```

## Exceptions (Tables NOT Requiring User Tracking)

### System Tables
- `migrations`
- `failed_jobs`
- `password_reset_tokens`
- `sessions`

### Pivot Tables
- `case_criminals` (many-to-many pivot)
- `case_evidence` (many-to-many pivot)
- `role_has_permissions` (Spatie permissions)
- `model_has_permissions` (Spatie permissions)
- `model_has_roles` (Spatie permissions)

### Simple Lookup Tables
- `districts` (static reference data)
- `traditional_authorities` (static reference data)
- `crime_categories` (static reference data)
- `court_types` (static reference data)

## Database Indexes for Performance

### Standard Indexes for User Tracking
```sql
-- Performance indexes for user tracking fields
CREATE INDEX idx_tablename_created_by ON table_name(created_by);
CREATE INDEX idx_tablename_updated_by ON table_name(updated_by);
CREATE INDEX idx_tablename_created_at ON table_name(created_at);
CREATE INDEX idx_tablename_updated_at ON table_name(updated_at);

-- Composite indexes for common queries
CREATE INDEX idx_tablename_user_date ON table_name(created_by, created_at);
CREATE INDEX idx_tablename_status_user ON table_name(status, created_by);
```

## Reporting and Analytics

### User Activity Reports
```sql
-- Most active users (by record creation)
SELECT
    u.name,
    COUNT(*) as records_created
FROM criminals c
JOIN users u ON c.created_by = u.id
GROUP BY u.id, u.name
ORDER BY records_created DESC;

-- Recent activity by user
SELECT
    u.name,
    c.criminal_number,
    c.created_at
FROM criminals c
JOIN users u ON c.created_by = u.id
WHERE c.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY c.created_at DESC;
```

### Data Integrity Checks
```sql
-- Find records with missing user tracking
SELECT COUNT(*) as orphaned_records
FROM criminals
WHERE created_by IS NULL OR updated_by IS NULL;

-- Find records modified by deactivated users
SELECT c.*, u.name as creator_name
FROM criminals c
LEFT JOIN users u ON c.created_by = u.id
WHERE u.id IS NULL AND c.created_by IS NOT NULL;
```

## Security Considerations

### 1. Data Protection
- User tracking fields should be protected from direct manipulation
- Only system should set these fields via the trait
- Regular audits of user tracking data integrity

### 2. Privacy Compliance
- Consider data retention policies for user tracking
- Anonymization procedures for historical data
- GDPR compliance for user data in tracking fields

### 3. Access Control
- Restrict access to user tracking information based on roles
- Audit trail viewing permissions
- Secure deletion procedures that maintain audit integrity

---

**Implementation Status**: ✅ Trait created and documented
**Next Steps**: Update all existing migrations and models to include user tracking
**Compliance**: Meets criminal justice audit and security requirements