<?php $__env->startSection('title', 'Edit Role - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Edit Role</h1>
            <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => ['items' => [
                ['title' => 'System'],
                ['title' => 'Role Management', 'url' => route('roles.index')],
                ['title' => 'Edit Role']
            ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                ['title' => 'System'],
                ['title' => 'Role Management', 'url' => route('roles.index')],
                ['title' => 'Edit Role']
            ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
        </div>
        <div class="d-flex gap-2">
            <a href="<?php echo e(route('roles.show', $id)); ?>" class="btn btn-outline-info">
                <i data-feather="eye" class="icon-xs me-2"></i>
                View Role
            </a>
            <a href="<?php echo e(route('roles.index')); ?>" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Roles
            </a>
        </div>
    </div>

    <form method="POST" action="<?php echo e(route('roles.update', $id)); ?>">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>
        
        <!-- Role Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="shield" class="icon-sm me-2"></i>
                    Role Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">Role Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required
                               value="inspector" readonly>
                        <small class="text-muted">Role name cannot be changed for system roles.</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="display_name" class="form-label">Display Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="display_name" name="display_name" required
                               value="Inspector">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3">Police inspector with case management access. Can create, edit, and manage criminal cases, evidence, and generate reports.</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Permissions -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="key" class="icon-sm me-2"></i>
                    Role Permissions
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Case Management</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="view_cases" name="permissions[]" value="view_cases" checked>
                            <label class="form-check-label" for="view_cases">
                                View Cases
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="create_cases" name="permissions[]" value="create_cases" checked>
                            <label class="form-check-label" for="create_cases">
                                Create Cases
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="edit_cases" name="permissions[]" value="edit_cases" checked>
                            <label class="form-check-label" for="edit_cases">
                                Edit Cases
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="delete_cases" name="permissions[]" value="delete_cases">
                            <label class="form-check-label" for="delete_cases">
                                Delete Cases
                            </label>
                        </div>
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="assign_cases" name="permissions[]" value="assign_cases" checked>
                            <label class="form-check-label" for="assign_cases">
                                Assign Cases
                            </label>
                        </div>

                        <h6 class="text-info">Evidence Management</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="view_evidence" name="permissions[]" value="view_evidence" checked>
                            <label class="form-check-label" for="view_evidence">
                                View Evidence
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="add_evidence" name="permissions[]" value="add_evidence" checked>
                            <label class="form-check-label" for="add_evidence">
                                Add Evidence
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="edit_evidence" name="permissions[]" value="edit_evidence" checked>
                            <label class="form-check-label" for="edit_evidence">
                                Edit Evidence
                            </label>
                        </div>
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="chain_custody" name="permissions[]" value="chain_custody" checked>
                            <label class="form-check-label" for="chain_custody">
                                Manage Chain of Custody
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">Criminal Profiles</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="view_criminals" name="permissions[]" value="view_criminals" checked>
                            <label class="form-check-label" for="view_criminals">
                                View Criminal Profiles
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="create_criminals" name="permissions[]" value="create_criminals" checked>
                            <label class="form-check-label" for="create_criminals">
                                Create Profiles
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="edit_criminals" name="permissions[]" value="edit_criminals" checked>
                            <label class="form-check-label" for="edit_criminals">
                                Edit Profiles
                            </label>
                        </div>
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="delete_criminals" name="permissions[]" value="delete_criminals">
                            <label class="form-check-label" for="delete_criminals">
                                Delete Profiles
                            </label>
                        </div>

                        <h6 class="text-success">Reports & Analytics</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="view_reports" name="permissions[]" value="view_reports" checked>
                            <label class="form-check-label" for="view_reports">
                                View Reports
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="generate_reports" name="permissions[]" value="generate_reports" checked>
                            <label class="form-check-label" for="generate_reports">
                                Generate Reports
                            </label>
                        </div>
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="system_reports" name="permissions[]" value="system_reports">
                            <label class="form-check-label" for="system_reports">
                                System Reports
                            </label>
                        </div>

                        <h6 class="text-danger">System Administration</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="manage_users" name="permissions[]" value="manage_users">
                            <label class="form-check-label" for="manage_users">
                                Manage Users
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="manage_roles" name="permissions[]" value="manage_roles">
                            <label class="form-check-label" for="manage_roles">
                                Manage Roles
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="system_settings" name="permissions[]" value="system_settings">
                            <label class="form-check-label" for="system_settings">
                                System Settings
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="manage_backups" name="permissions[]" value="manage_backups">
                            <label class="form-check-label" for="manage_backups">
                                Backup Management
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permission Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="info" class="icon-sm me-2"></i>
                    Permission Summary
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i data-feather="info" class="icon-sm me-2"></i>
                    <strong>Current Permissions:</strong> This role currently has <strong>15 permissions</strong> assigned.
                    <br><strong>Assigned Users:</strong> <strong>12 users</strong> currently have this role.
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Permission Categories</h6>
                        <ul class="list-unstyled">
                            <li><i data-feather="check" class="icon-xs text-success me-2"></i>Case Management (5/5)</li>
                            <li><i data-feather="check" class="icon-xs text-success me-2"></i>Evidence Management (4/4)</li>
                            <li><i data-feather="check" class="icon-xs text-warning me-2"></i>Criminal Profiles (3/4)</li>
                            <li><i data-feather="check" class="icon-xs text-success me-2"></i>Reports (2/3)</li>
                            <li><i data-feather="x" class="icon-xs text-danger me-2"></i>System Administration (0/4)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Impact Assessment</h6>
                        <ul class="list-unstyled">
                            <li><strong>Security Level:</strong> Medium</li>
                            <li><strong>Access Scope:</strong> Operational</li>
                            <li><strong>Data Access:</strong> Case & Evidence Data</li>
                            <li><strong>Administrative:</strong> Limited</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-end gap-2 mb-4">
            <a href="<?php echo e(route('roles.show', $id)); ?>" class="btn btn-outline-secondary">
                <i data-feather="x" class="icon-xs me-2"></i>
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i data-feather="save" class="icon-xs me-2"></i>
                Update Role
            </button>
        </div>
    </form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Count permissions dynamically
    function updatePermissionCount() {
        const checkedPermissions = document.querySelectorAll('input[name="permissions[]"]:checked').length;
        // Update the permission count display if needed
    }
    
    // Add event listeners to checkboxes
    document.querySelectorAll('input[name="permissions[]"]').forEach(checkbox => {
        checkbox.addEventListener('change', updatePermissionCount);
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/roles/edit.blade.php ENDPATH**/ ?>