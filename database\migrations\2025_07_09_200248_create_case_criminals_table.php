<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('case_criminals', function (Blueprint $table) {
            $table->id();

            // Foreign keys
            $table->foreignId('case_id')->constrained('cases')->onDelete('cascade');
            $table->foreignId('criminal_id')->constrained('criminals')->onDelete('cascade');

            // Relationship details
            $table->enum('role', [
                'Primary Suspect', 'Secondary Suspect', 'Accomplice',
                'Witness', 'Victim', 'Person of Interest'
            ]);
            $table->date('arrest_date')->nullable();
            $table->text('charges')->nullable();
            $table->enum('status', [
                'At Large', 'Arrested', 'Released on Bail', 'In Custody',
                'Convicted', 'Acquitted', 'Charges Dropped'
            ])->default('At Large');
            $table->text('notes')->nullable();

            $table->timestamps();

            // Unique constraint to prevent duplicate entries
            $table->unique(['case_id', 'criminal_id']);

            // Indexes
            $table->index(['case_id']);
            $table->index(['criminal_id']);
            $table->index(['role']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('case_criminals');
    }
};
