@extends('layouts.app')

@section('title', 'Court Case Status - ' . config('app.name'))

@section('content')
    <!-- Branded Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <div class="icon-shape icon-lg bg-white bg-opacity-20 text-white rounded-3">
                                <i data-feather="activity" class="icon-md"></i>
                            </div>
                        </div>
                        <div>
                            <h1 class="h2 mb-1">Court Case Status</h1>
                            <p class="mb-0 opacity-75">Track the progress of court cases</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex flex-wrap gap-2 justify-content-md-end">
                        <button type="button" class="btn btn-light" data-bs-toggle="collapse" data-bs-target="#searchCollapse">
                            <i data-feather="search" class="icon-xs me-2"></i>
                            Search Cases
                        </button>
                        <button type="button" class="btn btn-outline-light">
                            <i data-feather="download" class="icon-xs me-2"></i>
                            Export Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="collapse mb-4" id="searchCollapse">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="{{ route('court.status') }}">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search Cases</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="Case number, title, or defendant">
                        </div>
                        <div class="col-md-3">
                            <label for="status_filter" class="form-label">Status</label>
                            <select class="form-select" id="status_filter" name="status_filter">
                                <option value="">All Status</option>
                                <option value="Pending" {{ request('status_filter') === 'Pending' ? 'selected' : '' }}>Pending</option>
                                <option value="In Progress" {{ request('status_filter') === 'In Progress' ? 'selected' : '' }}>In Progress</option>
                                <option value="Closed" {{ request('status_filter') === 'Closed' ? 'selected' : '' }}>Closed</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <div class="d-flex gap-2 w-100">
                                <button type="submit" class="btn btn-primary">
                                    <i data-feather="search" class="icon-xs me-2"></i>
                                    Search
                                </button>
                                <a href="{{ route('court.status') }}" class="btn btn-outline-secondary">
                                    <i data-feather="x" class="icon-xs me-2"></i>
                                    Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="icon-shape icon-md bg-primary text-white rounded-circle mx-auto mb-3">
                        <i data-feather="clock" class="icon-sm"></i>
                    </div>
                    <h4 class="text-primary">{{ $cases->where('status', 'Pending')->count() }}</h4>
                    <p class="mb-0">Pending Cases</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="icon-shape icon-md bg-warning text-white rounded-circle mx-auto mb-3">
                        <i data-feather="activity" class="icon-sm"></i>
                    </div>
                    <h4 class="text-warning">{{ $cases->where('status', 'In Progress')->count() }}</h4>
                    <p class="mb-0">In Progress</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="icon-shape icon-md bg-success text-white rounded-circle mx-auto mb-3">
                        <i data-feather="check-circle" class="icon-sm"></i>
                    </div>
                    <h4 class="text-success">{{ $cases->where('status', 'Closed')->count() }}</h4>
                    <p class="mb-0">Closed Cases</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="icon-shape icon-md bg-info text-white rounded-circle mx-auto mb-3">
                        <i data-feather="file-text" class="icon-sm"></i>
                    </div>
                    <h4 class="text-info">{{ $cases->count() }}</h4>
                    <p class="mb-0">Total Cases</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Cases Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i data-feather="list" class="icon-sm me-2"></i>
                Court Cases Status
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Case Number</th>
                            <th>Case Title</th>
                            <th>Status</th>
                            <th>Progress</th>
                            <th>Filed Date</th>
                            <th>Next Hearing</th>
                            <th>Judge</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($cases as $case)
                            <tr>
                                <td>
                                    <strong>{{ $case['case_number'] }}</strong>
                                </td>
                                <td>{{ $case['case_title'] }}</td>
                                <td>
                                    @if($case['status'] === 'Pending')
                                        <span class="badge bg-warning">{{ $case['status'] }}</span>
                                    @elseif($case['status'] === 'In Progress')
                                        <span class="badge bg-primary">{{ $case['status'] }}</span>
                                    @elseif($case['status'] === 'Closed')
                                        <span class="badge bg-success">{{ $case['status'] }}</span>
                                    @else
                                        <span class="badge bg-secondary">{{ $case['status'] }}</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar 
                                                @if($case['progress'] < 30) bg-danger
                                                @elseif($case['progress'] < 70) bg-warning
                                                @else bg-success
                                                @endif" 
                                                style="width: {{ $case['progress'] }}%"></div>
                                        </div>
                                        <small class="text-muted">{{ $case['progress'] }}%</small>
                                    </div>
                                </td>
                                <td>{{ $case['filed_date']->format('d/m/Y') }}</td>
                                <td>
                                    @if($case['next_hearing'])
                                        {{ $case['next_hearing']->format('d/m/Y') }}
                                        <small class="text-muted d-block">{{ $case['next_hearing']->diffForHumans() }}</small>
                                    @else
                                        <span class="text-muted">No hearing scheduled</span>
                                    @endif
                                </td>
                                <td>{{ $case['judge'] }}</td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i data-feather="more-horizontal" class="icon-xs"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#!">
                                                <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                            </a></li>
                                            <li><a class="dropdown-item" href="#!">
                                                <i data-feather="edit" class="icon-xs me-2"></i>Update Status
                                            </a></li>
                                            <li><a class="dropdown-item" href="#!">
                                                <i data-feather="calendar" class="icon-xs me-2"></i>Schedule Hearing
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="#!">
                                                <i data-feather="file-text" class="icon-xs me-2"></i>Generate Report
                                            </a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-muted">
                                        <i data-feather="search" class="icon-lg mb-2"></i>
                                        <p class="mb-0">No cases found matching your search criteria.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
@endpush
