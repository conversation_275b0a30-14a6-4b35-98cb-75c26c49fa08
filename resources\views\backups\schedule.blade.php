@extends('layouts.app')

@section('title', 'Backup Schedule - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Backup Schedule</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'Backup Management', 'url' => route('backups.index')],
                ['title' => 'Backup Schedule']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleModal">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Add Schedule
            </button>
        </div>
    </div>

    <!-- Schedule Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">4</h4>
                    <p class="mb-0">Active Schedules</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-success">15</h4>
                    <p class="mb-0">Successful Runs</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning">1</h4>
                    <p class="mb-0">Failed Runs</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-info">2</h4>
                    <p class="mb-0">Next 24 Hours</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scheduled Backups -->
    <div class="card mb-4">
        <div class="card-header">
            <h4 class="mb-0">Scheduled Backup Jobs</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Schedule Name</th>
                            <th>Type</th>
                            <th>Frequency</th>
                            <th>Next Run</th>
                            <th>Last Run</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <strong>Daily Full Backup</strong>
                                <br><small class="text-muted">Complete system backup</small>
                            </td>
                            <td><span class="badge bg-primary">Full</span></td>
                            <td>Daily at 2:00 AM</td>
                            <td>
                                16/12/2024 02:00 AM
                                <br><small class="text-success">In 6 hours</small>
                            </td>
                            <td>
                                15/12/2024 02:00 AM
                                <br><small class="text-success">Success</small>
                            </td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit Schedule
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="play" class="icon-xs me-2"></i>Run Now
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-warning" href="#!">
                                            <i data-feather="pause" class="icon-xs me-2"></i>Disable
                                        </a></li>
                                        <li><a class="dropdown-item text-danger" href="#!">
                                            <i data-feather="trash-2" class="icon-xs me-2"></i>Delete
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>Weekly Database Backup</strong>
                                <br><small class="text-muted">Database only backup</small>
                            </td>
                            <td><span class="badge bg-info">Database</span></td>
                            <td>Weekly on Sunday at 3:00 AM</td>
                            <td>
                                22/12/2024 03:00 AM
                                <br><small class="text-info">In 6 days</small>
                            </td>
                            <td>
                                15/12/2024 03:00 AM
                                <br><small class="text-success">Success</small>
                            </td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit Schedule
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="play" class="icon-xs me-2"></i>Run Now
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-warning" href="#!">
                                            <i data-feather="pause" class="icon-xs me-2"></i>Disable
                                        </a></li>
                                        <li><a class="dropdown-item text-danger" href="#!">
                                            <i data-feather="trash-2" class="icon-xs me-2"></i>Delete
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>Monthly Archive Backup</strong>
                                <br><small class="text-muted">Long-term archive backup</small>
                            </td>
                            <td><span class="badge bg-primary">Full</span></td>
                            <td>Monthly on 1st at 1:00 AM</td>
                            <td>
                                01/01/2025 01:00 AM
                                <br><small class="text-info">In 17 days</small>
                            </td>
                            <td>
                                01/12/2024 01:00 AM
                                <br><small class="text-success">Success</small>
                            </td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit Schedule
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="play" class="icon-xs me-2"></i>Run Now
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-warning" href="#!">
                                            <i data-feather="pause" class="icon-xs me-2"></i>Disable
                                        </a></li>
                                        <li><a class="dropdown-item text-danger" href="#!">
                                            <i data-feather="trash-2" class="icon-xs me-2"></i>Delete
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr class="table-warning">
                            <td>
                                <strong>Emergency Backup</strong>
                                <br><small class="text-muted">On-demand emergency backup</small>
                            </td>
                            <td><span class="badge bg-warning">Files</span></td>
                            <td>Manual trigger only</td>
                            <td>
                                <span class="text-muted">Manual only</span>
                            </td>
                            <td>
                                10/12/2024 08:15 PM
                                <br><small class="text-warning">Failed</small>
                            </td>
                            <td><span class="badge bg-warning">Disabled</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit Schedule
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="play" class="icon-xs me-2"></i>Run Now
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-success" href="#!">
                                            <i data-feather="play-circle" class="icon-xs me-2"></i>Enable
                                        </a></li>
                                        <li><a class="dropdown-item text-danger" href="#!">
                                            <i data-feather="trash-2" class="icon-xs me-2"></i>Delete
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Backup Calendar -->
    <div class="card">
        <div class="card-header">
            <h4 class="mb-0">Backup Calendar</h4>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i data-feather="calendar" class="icon-sm me-2"></i>
                <strong>Upcoming Backups:</strong> View scheduled backup times to plan system maintenance.
            </div>
            <div class="row">
                <div class="col-md-6">
                    <h6>Next 7 Days</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Daily Full Backup</strong>
                                <br><small class="text-muted">Tomorrow at 2:00 AM</small>
                            </div>
                            <span class="badge bg-primary">Full</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Daily Full Backup</strong>
                                <br><small class="text-muted">17/12/2024 at 2:00 AM</small>
                            </div>
                            <span class="badge bg-primary">Full</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Weekly Database Backup</strong>
                                <br><small class="text-muted">22/12/2024 at 3:00 AM</small>
                            </div>
                            <span class="badge bg-info">Database</span>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Backup Settings</h6>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="email_notifications" checked>
                        <label class="form-check-label" for="email_notifications">
                            Email notifications for backup completion
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="failure_alerts" checked>
                        <label class="form-check-label" for="failure_alerts">
                            Alert on backup failures
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="auto_cleanup">
                        <label class="form-check-label" for="auto_cleanup">
                            Auto-cleanup old backups (30+ days)
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Schedule Modal -->
    <div class="modal fade" id="scheduleModal" tabindex="-1" aria-labelledby="scheduleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scheduleModalLabel">
                        <i data-feather="clock" class="icon-sm me-2"></i>
                        Add Backup Schedule
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="schedule_name" class="form-label">Schedule Name</label>
                                <input type="text" class="form-control" id="schedule_name" placeholder="e.g., Daily Backup">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="backup_type" class="form-label">Backup Type</label>
                                <select class="form-select" id="backup_type">
                                    <option value="full">Full Backup</option>
                                    <option value="database">Database Only</option>
                                    <option value="files">Files Only</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="frequency" class="form-label">Frequency</label>
                                <select class="form-select" id="frequency">
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="time" class="form-label">Time</label>
                                <input type="time" class="form-control" id="time" value="02:00">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">
                        <i data-feather="save" class="icon-xs me-2"></i>
                        Create Schedule
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
@endpush
