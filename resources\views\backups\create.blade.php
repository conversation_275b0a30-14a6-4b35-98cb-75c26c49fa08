@extends('layouts.app')

@section('title', 'Create Backup - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Create New Backup</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'Backup Management', 'url' => route('backups.index')],
                ['title' => 'Create Backup']
            ]" />
        </div>
        <div>
            <a href="{{ route('backups.index') }}" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Backups
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('backups.store') }}">
        @csrf
        
        <!-- Backup Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="settings" class="icon-sm me-2"></i>
                    Backup Configuration
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="backup_name" class="form-label">Backup Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="backup_name" name="backup_name" required
                               placeholder="e.g., Manual_Backup_2024_12_15" value="{{ old('backup_name') }}">
                        @error('backup_name')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="backup_type" class="form-label">Backup Type <span class="text-danger">*</span></label>
                        <select class="form-select" id="backup_type" name="backup_type" required>
                            <option value="">Select backup type</option>
                            <option value="full" {{ old('backup_type') == 'full' ? 'selected' : '' }}>Full Backup (Database + Files)</option>
                            <option value="database" {{ old('backup_type') == 'database' ? 'selected' : '' }}>Database Only</option>
                            <option value="files" {{ old('backup_type') == 'files' ? 'selected' : '' }}>Files Only</option>
                        </select>
                        @error('backup_type')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Optional description for this backup...">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup Options -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="check-square" class="icon-sm me-2"></i>
                    Backup Options
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Database Options</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="include_users" name="include_users" checked>
                            <label class="form-check-label" for="include_users">
                                Include User Data
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="include_cases" name="include_cases" checked>
                            <label class="form-check-label" for="include_cases">
                                Include Case Data
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="include_criminals" name="include_criminals" checked>
                            <label class="form-check-label" for="include_criminals">
                                Include Criminal Profiles
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="include_evidence" name="include_evidence" checked>
                            <label class="form-check-label" for="include_evidence">
                                Include Evidence Records
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="include_settings" name="include_settings" checked>
                            <label class="form-check-label" for="include_settings">
                                Include System Settings
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>File Options</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="include_uploads" name="include_uploads" checked>
                            <label class="form-check-label" for="include_uploads">
                                Include Uploaded Files
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="include_evidence_files" name="include_evidence_files" checked>
                            <label class="form-check-label" for="include_evidence_files">
                                Include Evidence Files
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="include_documents" name="include_documents" checked>
                            <label class="form-check-label" for="include_documents">
                                Include Document Library
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="include_logs" name="include_logs">
                            <label class="form-check-label" for="include_logs">
                                Include System Logs
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="compress_backup" name="compress_backup" checked>
                            <label class="form-check-label" for="compress_backup">
                                Compress Backup (Recommended)
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup Schedule -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="clock" class="icon-sm me-2"></i>
                    Backup Schedule
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="schedule_type" class="form-label">Schedule Type</label>
                        <select class="form-select" id="schedule_type" name="schedule_type">
                            <option value="immediate" selected>Run Immediately</option>
                            <option value="scheduled">Schedule for Later</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3" id="schedule_time_container" style="display: none;">
                        <label for="schedule_time" class="form-label">Schedule Time</label>
                        <input type="datetime-local" class="form-control" id="schedule_time" name="schedule_time">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="email_notification" name="email_notification">
                            <label class="form-check-label" for="email_notification">
                                Send email notification when backup is complete
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="info" class="icon-sm me-2"></i>
                    Backup Summary
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i data-feather="info" class="icon-sm me-2"></i>
                    <strong>Estimated backup size:</strong> This will be calculated based on your selections.
                    <br><strong>Estimated time:</strong> Backup time depends on data size and system performance.
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>What will be backed up:</h6>
                        <ul id="backup_summary" class="list-unstyled">
                            <li><i data-feather="check" class="icon-xs text-success me-2"></i>Database tables</li>
                            <li><i data-feather="check" class="icon-xs text-success me-2"></i>Uploaded files</li>
                            <li><i data-feather="check" class="icon-xs text-success me-2"></i>System configuration</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Backup Details:</h6>
                        <ul class="list-unstyled">
                            <li><strong>Format:</strong> Compressed ZIP archive</li>
                            <li><strong>Encryption:</strong> AES-256 encryption</li>
                            <li><strong>Storage:</strong> Local server storage</li>
                            <li><strong>Retention:</strong> 30 days (configurable)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-end gap-2 mb-4">
            <a href="{{ route('backups.index') }}" class="btn btn-outline-secondary">
                <i data-feather="x" class="icon-xs me-2"></i>
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i data-feather="save" class="icon-xs me-2"></i>
                Create Backup
            </button>
        </div>
    </form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Handle schedule type change
    const scheduleType = document.getElementById('schedule_type');
    const scheduleTimeContainer = document.getElementById('schedule_time_container');
    
    scheduleType.addEventListener('change', function() {
        if (this.value === 'scheduled') {
            scheduleTimeContainer.style.display = 'block';
            document.getElementById('schedule_time').required = true;
        } else {
            scheduleTimeContainer.style.display = 'none';
            document.getElementById('schedule_time').required = false;
        }
    });
    
    // Handle backup type change
    const backupType = document.getElementById('backup_type');
    const databaseOptions = document.querySelectorAll('input[type="checkbox"][id^="include_"]');
    const fileOptions = document.querySelectorAll('input[type="checkbox"][id^="include_"][id*="files"], input[type="checkbox"][id^="include_"][id*="uploads"], input[type="checkbox"][id^="include_"][id*="documents"], input[type="checkbox"][id^="include_"][id*="logs"]');
    
    backupType.addEventListener('change', function() {
        if (this.value === 'database') {
            // Enable database options, disable file options
            fileOptions.forEach(option => {
                option.disabled = true;
                option.checked = false;
            });
            databaseOptions.forEach(option => {
                if (!fileOptions.includes(option)) {
                    option.disabled = false;
                }
            });
        } else if (this.value === 'files') {
            // Enable file options, disable database options
            databaseOptions.forEach(option => {
                if (!fileOptions.includes(option)) {
                    option.disabled = true;
                    option.checked = false;
                }
            });
            fileOptions.forEach(option => {
                option.disabled = false;
            });
        } else {
            // Enable all options for full backup
            databaseOptions.forEach(option => option.disabled = false);
            fileOptions.forEach(option => option.disabled = false);
        }
    });
    
    // Auto-generate backup name
    const backupName = document.getElementById('backup_name');
    if (!backupName.value) {
        const now = new Date();
        const dateStr = now.getFullYear() + '_' + 
                       String(now.getMonth() + 1).padStart(2, '0') + '_' + 
                       String(now.getDate()).padStart(2, '0');
        const timeStr = String(now.getHours()).padStart(2, '0') + '_' + 
                       String(now.getMinutes()).padStart(2, '0');
        backupName.value = `Manual_Backup_${dateStr}_${timeStr}`;
    }
});
</script>
@endpush
