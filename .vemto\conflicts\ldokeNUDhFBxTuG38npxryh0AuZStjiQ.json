{"conflicts": [{"id": "b3a39235-4ae5-485a-91f7-c5c53bb6af50", "currentContent": "<?php\n\nnamespace Database\\Factories;\n\nuse App\\Models\\User;\nuse Illuminate\\Database\\Eloquent\\Factories\\Factory;\n\n/**\n * @extends \\Illuminate\\Database\\Eloquent\\Factories\\Factory<\\App\\Models\\Team>\n */\nclass TeamFactory extends Factory\n{\n    /**\n     * Define the model's default state.\n     *\n     * @return array<string, mixed>\n     */\n    public function definition(): array\n    {\n        return [\n            'name' => $this->faker->unique()->company(),\n            'user_id' => User::factory(),\n            'personal_team' => true,\n        ];\n    }\n}\n", "newContent": "<?php\n\nnamespace Database\\Factories;\n\nuse App\\Models\\Team;\nuse Illuminate\\Support\\Str;\nuse Illuminate\\Database\\Eloquent\\Factories\\Factory;\n\nclass TeamFactory extends Factory\n{\n    /**\n     * The name of the factory's corresponding model.\n     *\n     * @var string\n     */\n    protected $model = Team::class;\n\n    /**\n     * Define the model's default state.\n     *\n     * @return array<string, mixed>\n     */\n    public function definition(): array\n    {\n        return [\n            'user_id' => fake()->randomNumber(),\n            'name' => fake()->name(),\n            'personal_team' => fake()->boolean(),\n        ];\n    }\n}\n"}]}