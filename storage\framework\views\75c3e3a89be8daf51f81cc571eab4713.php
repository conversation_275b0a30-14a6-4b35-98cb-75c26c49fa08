<?php $__env->startSection('title', 'Case Statistics  - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Case Statistics</h1>
            <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => ['items' => [
                ['title' => 'Reports'],
                ['title' => 'Case Statistics']
            ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                ['title' => 'Reports'],
                ['title' => 'Case Statistics']
            ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
        </div>
        <div class="d-flex gap-2">
            <a href="#!" class="btn btn-outline-primary">
                <i data-feather="download" class="icon-xs me-2"></i>
                Export Report
            </a>
            <a href="#!" class="btn btn-primary">
                <i data-feather="refresh-cw" class="icon-xs me-2"></i>
                Refresh Data
            </a>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">1,456</h4>
                    <p class="mb-0 small">Total Cases</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-success">1,134</h4>
                    <p class="mb-0 small">Solved Cases</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning">234</h4>
                    <p class="mb-0 small">Active Cases</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-danger">88</h4>
                    <p class="mb-0 small">Cold Cases</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-info">78%</h4>
                    <p class="mb-0 small">Solve Rate</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-secondary">15</h4>
                    <p class="mb-0 small">Avg Days</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Case Resolution Timeline -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i data-feather="clock" class="icon-sm me-2"></i>
                        Case Resolution Timeline
                    </h4>
                </div>
                <div class="card-body">
                    <canvas id="resolutionTimelineChart" height="300"></canvas>
                </div>
            </div>

            <!-- Case Status Distribution -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i data-feather="pie-chart" class="icon-sm me-2"></i>
                        Case Status Distribution
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <canvas id="caseStatusChart" height="250"></canvas>
                        </div>
                        <div class="col-md-6">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Status</th>
                                            <th>Count</th>
                                            <th>Percentage</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><span class="badge bg-success me-2"></span>Solved</td>
                                            <td>1,134</td>
                                            <td>77.9%</td>
                                        </tr>
                                        <tr>
                                            <td><span class="badge bg-warning me-2"></span>Active</td>
                                            <td>234</td>
                                            <td>16.1%</td>
                                        </tr>
                                        <tr>
                                            <td><span class="badge bg-danger me-2"></span>Cold</td>
                                            <td>88</td>
                                            <td>6.0%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Officer Performance -->
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i data-feather="users" class="icon-sm me-2"></i>
                        Officer Performance Statistics
                    </h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Officer</th>
                                    <th>Total Cases</th>
                                    <th>Solved</th>
                                    <th>Active</th>
                                    <th>Solve Rate</th>
                                    <th>Avg Resolution Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Inspector Banda</strong></td>
                                    <td>156</td>
                                    <td>134</td>
                                    <td>22</td>
                                    <td><span class="badge bg-success">86%</span></td>
                                    <td>12 days</td>
                                </tr>
                                <tr>
                                    <td><strong>Sergeant Mwale</strong></td>
                                    <td>123</td>
                                    <td>98</td>
                                    <td>25</td>
                                    <td><span class="badge bg-success">80%</span></td>
                                    <td>15 days</td>
                                </tr>
                                <tr>
                                    <td><strong>Constable Phiri</strong></td>
                                    <td>89</td>
                                    <td>67</td>
                                    <td>22</td>
                                    <td><span class="badge bg-warning">75%</span></td>
                                    <td>18 days</td>
                                </tr>
                                <tr>
                                    <td><strong>Inspector Kumwenda</strong></td>
                                    <td>145</td>
                                    <td>125</td>
                                    <td>20</td>
                                    <td><span class="badge bg-success">86%</span></td>
                                    <td>11 days</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Monthly Comparison -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="calendar" class="icon-sm me-2"></i>
                        Monthly Comparison
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-0">This Month</h6>
                            <h4 class="text-primary">234 cases</h4>
                        </div>
                        <div class="text-end">
                            <small class="text-success">+12% vs last month</small>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-0">Last Month</h6>
                            <h4 class="text-secondary">209 cases</h4>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">Same Month Last Year</h6>
                            <h4 class="text-secondary">198 cases</h4>
                        </div>
                        <div class="text-end">
                            <small class="text-success">+18% vs last year</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Case Priority Distribution -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="alert-triangle" class="icon-sm me-2"></i>
                        Case Priority Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Critical</span>
                            <span><strong>45 cases</strong></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-danger" style="width: 19%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>High</span>
                            <span><strong>78 cases</strong></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: 33%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Medium</span>
                            <span><strong>89 cases</strong></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: 38%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Low</span>
                            <span><strong>22 cases</strong></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 10%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Statistics -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="trending-up" class="icon-sm me-2"></i>
                        Quick Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h5 class="text-primary">15.2</h5>
                            <small class="text-muted">Avg Resolution Days</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h5 class="text-success">78%</h5>
                            <small class="text-muted">Overall Solve Rate</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning">234</h5>
                            <small class="text-muted">Cases This Month</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-info">45</h5>
                            <small class="text-muted">Officers Active</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Case Resolution Timeline Chart
    const resolutionCtx = document.getElementById('resolutionTimelineChart').getContext('2d');
    new Chart(resolutionCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Cases Opened',
                data: [120, 135, 110, 145, 160, 155, 170, 165, 150, 175, 180, 234],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }, {
                label: 'Cases Solved',
                data: [95, 125, 98, 135, 145, 140, 155, 150, 135, 160, 165, 183],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Case Status Chart
    const statusCtx = document.getElementById('caseStatusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Solved', 'Active', 'Cold'],
            datasets: [{
                data: [1134, 234, 88],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/reports/statistics.blade.php ENDPATH**/ ?>