<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class CaseController extends Controller
{
    /**
     * Display active cases.
     */
    public function active()
    {
        return view('cases.active');
    }

    /**
     * Show the form for creating a new case.
     */
    public function create()
    {
        return view('cases.create');
    }

    /**
     * Store a newly created case in storage.
     */
    public function store(Request $request)
    {
        // Validate the request
        $request->validate([
            'case_title' => 'required|string|max:255',
            'case_type' => 'required|string|max:255',
            'priority' => 'required|string|max:255',
            'case_description' => 'nullable|string',
            'incident_date' => 'nullable|date',
            'reporting_officer' => 'nullable|string|max:255',
        ]);

        // For now, just redirect back with success message
        // In a real application, you would save to database here

        return redirect()->route('cases.active')->with('success', 'Case created successfully!');
    }

    /**
     * Display case search page.
     */
    public function search()
    {
        return view('cases.search');
    }

    /**
     * Display case reports page.
     */
    public function reports()
    {
        return view('cases.reports');
    }

    /**
     * Display the specified case.
     */
    public function show(string $id)
    {
        return view('cases.show', compact('id'));
    }

    /**
     * Show the form for editing the specified case.
     */
    public function edit(string $id)
    {
        return view('cases.edit', compact('id'));
    }

    /**
     * Update the specified case in storage.
     */
    public function update(Request $request, string $id)
    {
        return redirect()->route('cases.active')->with('success', 'Case updated successfully!');
    }

    /**
     * Remove the specified case from storage.
     */
    public function destroy(string $id)
    {
        return redirect()->route('cases.active')->with('success', 'Case deleted successfully!');
    }
}
