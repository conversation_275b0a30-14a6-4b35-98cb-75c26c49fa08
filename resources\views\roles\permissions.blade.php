@extends('layouts.app')

@section('title', 'Permission Management - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Permission Management</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'Role Management', 'url' => route('roles.index')],
                ['title' => 'Permission Management']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#createPermissionModal">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Create Permission
            </button>
            <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Roles
            </a>
        </div>
    </div>

    <!-- Permission Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">25</h4>
                    <p class="mb-0">Total Permissions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-success">20</h4>
                    <p class="mb-0">Active Permissions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-info">5</h4>
                    <p class="mb-0">Categories</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning">8</h4>
                    <p class="mb-0">Roles Using</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Permission Categories -->
    <div class="row">
        <!-- Case Management Permissions -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0 text-primary">
                        <i data-feather="folder" class="icon-sm me-2"></i>
                        Case Management
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Permission</th>
                                    <th>Description</th>
                                    <th>Roles</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>view_cases</code></td>
                                    <td>View criminal cases</td>
                                    <td><span class="badge bg-primary">6 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('view_cases')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>create_cases</code></td>
                                    <td>Create new cases</td>
                                    <td><span class="badge bg-primary">4 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('create_cases')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>edit_cases</code></td>
                                    <td>Edit existing cases</td>
                                    <td><span class="badge bg-primary">4 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('edit_cases')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>delete_cases</code></td>
                                    <td>Delete cases</td>
                                    <td><span class="badge bg-warning">2 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('delete_cases')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>assign_cases</code></td>
                                    <td>Assign cases to officers</td>
                                    <td><span class="badge bg-info">3 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('assign_cases')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Evidence Management Permissions -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0 text-info">
                        <i data-feather="archive" class="icon-sm me-2"></i>
                        Evidence Management
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Permission</th>
                                    <th>Description</th>
                                    <th>Roles</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>view_evidence</code></td>
                                    <td>View evidence items</td>
                                    <td><span class="badge bg-primary">5 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('view_evidence')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>add_evidence</code></td>
                                    <td>Add new evidence</td>
                                    <td><span class="badge bg-primary">4 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('add_evidence')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>edit_evidence</code></td>
                                    <td>Edit evidence details</td>
                                    <td><span class="badge bg-primary">3 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('edit_evidence')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>chain_custody</code></td>
                                    <td>Manage chain of custody</td>
                                    <td><span class="badge bg-info">3 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('chain_custody')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Criminal Profiles Permissions -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0 text-warning">
                        <i data-feather="users" class="icon-sm me-2"></i>
                        Criminal Profiles
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Permission</th>
                                    <th>Description</th>
                                    <th>Roles</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>view_criminals</code></td>
                                    <td>View criminal profiles</td>
                                    <td><span class="badge bg-primary">5 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('view_criminals')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>create_criminals</code></td>
                                    <td>Create criminal profiles</td>
                                    <td><span class="badge bg-primary">4 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('create_criminals')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>edit_criminals</code></td>
                                    <td>Edit criminal profiles</td>
                                    <td><span class="badge bg-primary">3 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('edit_criminals')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>delete_criminals</code></td>
                                    <td>Delete criminal profiles</td>
                                    <td><span class="badge bg-danger">2 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('delete_criminals')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Administration Permissions -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0 text-danger">
                        <i data-feather="settings" class="icon-sm me-2"></i>
                        System Administration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Permission</th>
                                    <th>Description</th>
                                    <th>Roles</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>manage_users</code></td>
                                    <td>Manage system users</td>
                                    <td><span class="badge bg-danger">2 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('manage_users')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>manage_roles</code></td>
                                    <td>Manage roles & permissions</td>
                                    <td><span class="badge bg-danger">2 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('manage_roles')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>system_settings</code></td>
                                    <td>Access system settings</td>
                                    <td><span class="badge bg-danger">2 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('system_settings')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>manage_backups</code></td>
                                    <td>Manage system backups</td>
                                    <td><span class="badge bg-warning">3 roles</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editPermission('manage_backups')">
                                            <i data-feather="edit" class="icon-xs"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Permission Modal -->
    <div class="modal fade" id="createPermissionModal" tabindex="-1" aria-labelledby="createPermissionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createPermissionModalLabel">
                        <i data-feather="plus" class="icon-sm me-2"></i>
                        Create New Permission
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createPermissionForm">
                        <div class="mb-3">
                            <label for="permission_name" class="form-label">Permission Name</label>
                            <input type="text" class="form-control" id="permission_name" placeholder="e.g., view_reports">
                            <small class="text-muted">Use lowercase with underscores (snake_case)</small>
                        </div>
                        <div class="mb-3">
                            <label for="permission_description" class="form-label">Description</label>
                            <input type="text" class="form-control" id="permission_description" placeholder="e.g., View system reports">
                        </div>
                        <div class="mb-3">
                            <label for="permission_category" class="form-label">Category</label>
                            <select class="form-select" id="permission_category">
                                <option value="case_management">Case Management</option>
                                <option value="evidence_management">Evidence Management</option>
                                <option value="criminal_profiles">Criminal Profiles</option>
                                <option value="reports">Reports & Analytics</option>
                                <option value="system_administration">System Administration</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="createPermission()">
                        <i data-feather="save" class="icon-xs me-2"></i>
                        Create Permission
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});

function editPermission(permissionName) {
    alert(`Editing permission: ${permissionName}`);
}

function createPermission() {
    const name = document.getElementById('permission_name').value;
    const description = document.getElementById('permission_description').value;
    const category = document.getElementById('permission_category').value;
    
    if (!name || !description) {
        alert('Please fill in all required fields.');
        return;
    }
    
    // In a real application, this would submit to the server
    alert(`Creating permission: ${name} - ${description} in category: ${category}`);
    
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('createPermissionModal')).hide();
    
    // Reset form
    document.getElementById('createPermissionForm').reset();
}
</script>
@endpush
