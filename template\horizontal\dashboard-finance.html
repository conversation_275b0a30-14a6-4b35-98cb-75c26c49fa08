<!DOCTYPE html>
<html lang="en" data-layout="horizontal">


<!-- Mirrored from dashui.codescandy.com/dashuipro/horizontal/dashboard-finance.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:30:56 GMT -->
<head>
  <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
<meta name="author" content="Codescandy" />

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M8S4MT3EYG"></script>
<script>
	window.dataLayer = window.dataLayer || [];
	function gtag() {
		dataLayer.push(arguments);
	}
	gtag('js', new Date());

	gtag('config', 'G-M8S4MT3EYG');
</script>

<!-- Favicon icon-->
<link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon/favicon.ico" />

<!-- Color modes -->
<script src="../assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="../assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />
<link href="../assets/libs/%40mdi/font/css/materialdesignicons.min.css" rel="stylesheet" />
<link href="../assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />

<!-- Theme CSS -->
<link rel="stylesheet" href="../assets/css/theme.min.css">

  <title>Finance | Dash UI - Bootstrap 5 Admin Dashboard Template</title>
</head>

<body>
  <main id="main-wrapper" class="main-wrapper">

    <div class="header">
	<!-- navbar -->
	<div class="navbar-custom navbar navbar-expand-lg">
		<div class="container-fluid px-0">
			<a class="navbar-brand d-block d-md-none" href="../index.html">
				<img src="../assets/images/brand/logo/logo-2.svg" alt="Image" />
			</a>

			<a id="nav-toggle" href="#!" class="ms-auto ms-md-0 me-0 me-lg-3">
				<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor" class="bi bi-text-indent-left text-muted" viewBox="0 0 16 16">
					<path
						d="M2 3.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm.646 2.146a.5.5 0 0 1 .708 0l2 2a.5.5 0 0 1 0 .708l-2 2a.5.5 0 0 1-.708-.708L4.293 8 2.646 6.354a.5.5 0 0 1 0-.708zM7 6.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm-5 3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"
					/>
				</svg>
			</a>

			<div class="d-none d-md-none d-lg-block">
				<!-- Form -->
				<form action="#">
					<div class="input-group">
						<input class="form-control rounded-3 bg-transparent ps-9" type="search" value="" id="searchInput" placeholder="Search" />
						<span class="">
							<button class="btn position-absolute start-0" type="button">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="15"
									height="15"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
									class="feather feather-search text-dark"
								>
									<circle cx="11" cy="11" r="8"></circle>
									<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
								</svg>
							</button>
						</span>
					</div>
				</form>
			</div>
			<!--Navbar nav -->
			<ul class="navbar-nav navbar-right-wrap ms-lg-auto d-flex nav-top-wrap align-items-center ms-4 ms-lg-0">
				<li>
					<div class="dropdown">
						<button class="btn btn-ghost btn-icon rounded-circle" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
							<i class="bi theme-icon-active"></i>
							<span class="visually-hidden bs-theme-text">Toggle theme</span>
						</button>
						<ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
									<i class="bi theme-icon bi-sun-fill"></i>
									<span class="ms-2">Light</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
									<i class="bi theme-icon bi-moon-stars-fill"></i>
									<span class="ms-2">Dark</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="auto" aria-pressed="true">
									<i class="bi theme-icon bi-circle-half"></i>
									<span class="ms-2">Auto</span>
								</button>
							</li>
						</ul>
					</div>
				</li>

				<li class="dropdown stopevent ms-2">
					<a class="btn btn-ghost btn-icon rounded-circle" href="#!" role="button" id="dropdownNotification" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<i class="icon-xs" data-feather="bell"></i>
					</a>
					<div class="dropdown-menu dropdown-menu-lg dropdown-menu-end" aria-labelledby="dropdownNotification">
						<div>
							<div class="border-bottom px-3 pt-2 pb-3 d-flex justify-content-between align-items-center">
								<p class="mb-0 text-dark fw-medium fs-4">Notifications</p>
								<a href="#!" class="text-muted">
									<span>
										<i class="me-1 icon-xs" data-feather="settings"></i>
									</span>
								</a>
							</div>
							<div data-simplebar style="height: 250px">
								<!-- List group -->
								<ul class="list-group list-group-flush notification-list-scroll">
									<!-- List group item -->
									<li class="list-group-item bg-light">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Rishi Chopra</h5>
											<p class="mb-0">Mauris blandit erat id nunc blandit, ac eleifend dolor pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Neha Kannned</h5>
											<p class="mb-0">Proin at elit vel est condimentum elementum id in ante. Maecenas et sapien metus.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Nirmala Chauhan</h5>
											<p class="mb-0">Morbi maximus urna lobortis elit sollicitudin sollicitudieget elit vel pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Sina Ray</h5>
											<p class="mb-0">Sed aliquam augue sit amet mauris volutpat hendrerit sed nunc eu diam.</p>
										</a>
									</li>
								</ul>
							</div>
							<div class="border-top px-3 py-2 text-center">
								<a href="#!" class="text-inherit">View all Notifications</a>
							</div>
						</div>
					</div>
				</li>
				<!-- List -->
				<li class="dropdown ms-2">
					<a class="rounded-circle" href="#!" role="button" id="dropdownUser" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<div class="avatar avatar-md avatar-indicators avatar-online">
							<img alt="avatar" src="../assets/images/avatar/avatar-11.jpg" class="rounded-circle" />
						</div>
					</a>
					<div class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownUser">
						<div class="px-4 pb-0 pt-2">
							<div class="lh-1">
								<h5 class="mb-1">John E. Grainger</h5>
								<a href="#!" class="text-inherit fs-6">View my profile</a>
							</div>
							<div class="dropdown-divider mt-3 mb-2"></div>
						</div>

						<ul class="list-unstyled">
							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="user"></i>
									Edit Profile
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="activity"></i>
									Activity Log
								</a>
							</li>

							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="settings"></i>
									Settings
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="../index.html">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="power"></i>
									Sign Out
								</a>
							</li>
						</ul>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>

    <!-- navbar horizontal -->
 <!-- navbar -->
<div class="navbar-horizontal nav-dashboard">
	<div class="container-fluid">
		<nav class="navbar navbar-expand-lg navbar-default navbar-dropdown p-0 py-lg-2">
			<div class="d-flex d-lg-block justify-content-between align-items-center w-100 w-lg-0 py-2 px-4 px-md-2 px-lg-0">
				<span class="d-lg-none">Menu</span>
				<!-- Button -->
				<button
					class="navbar-toggler collapsed ms-2"
					type="button"
					data-bs-toggle="collapse"
					data-bs-target="#navbar-default"
					aria-controls="navbar-default"
					aria-expanded="false"
					aria-label="Toggle navigation"
				>
					<span class="icon-bar top-bar mt-0"></span>
					<span class="icon-bar middle-bar"></span>
					<span class="icon-bar bottom-bar"></span>
				</button>
			</div>
			<!-- Collapse -->
			<div class="collapse navbar-collapse px-6 px-lg-0" id="navbar-default">
				<ul class="navbar-nav">
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarDashboard" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-bs-display="static">Dashboard</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarDashboard">
							<li>
								<a class="dropdown-item" href="index.html">Project</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-analytics.html">Analytics</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-ecommerce.html">Ecommerce</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-crm.html">CRM</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-finance.html">Finance</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-blog.html">Blog</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarApps" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Apps</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarApps">
							<li>
								<a class="dropdown-item" href="calendar.html">Calendar</a>
							</li>
							<li>
								<a class="dropdown-item" href="apps-file-manager.html">File Manager</a>
							</li>

							<li>
								<a class="dropdown-item" href="chat-app.html">Chat</a>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Kanban</a>
								<ul class="dropdown-menu">
									<li>
										<a href="task-kanban-grid.html" class="dropdown-item">Board</a>
									</li>
									<li>
										<a href="task-kanban-list.html" class="dropdown-item">List</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Email</a>
								<ul class="dropdown-menu">
									<li>
										<a href="mail.html" class="dropdown-item">Inbox</a>
									</li>
									<li>
										<a href="mail-details.html" class="dropdown-item">Details</a>
									</li>
									<li>
										<a href="mail-draft.html" class="dropdown-item">Draft</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Ecommerce</a>
								<ul class="dropdown-menu">
									<li>
										<a href="ecommerce-products.html" class="dropdown-item">Products</a>
									</li>
									<li>
										<a href="ecommerce-products-details.html" class="dropdown-item">Prouduct Details</a>
									</li>
									<li>
										<a href="ecommerce-product-edit.html" class="dropdown-item">Add Product</a>
									</li>

									<li>
										<a href="ecommerce-order-list.html" class="dropdown-item">Orders</a>
									</li>
									<li>
										<a href="ecommerce-order-detail.html" class="dropdown-item">Order Details</a>
									</li>
									<li>
										<a href="ecommerce-cart.html" class="dropdown-item">Shopping Cart</a>
									</li>
									<li>
										<a href="ecommerce-checkout.html" class="dropdown-item">Checkout</a>
									</li>
									<li>
										<a href="ecommerce-customer.html" class="dropdown-item">Customers</a>
									</li>
									<li>
										<a href="ecommerce-seller.html" class="dropdown-item">Seller</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Project</a>
								<ul class="dropdown-menu">
									<li class="nav-item">
										<a class="dropdown-item" href="project-grid.html">Grid</a>
									</li>
									<li class="nav-item">
										<a class="dropdown-item" href="project-list.html">List</a>
									</li>

									<li class="dropdown-submenu dropend">
										<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Single</a>
										<ul class="dropdown-menu">
											<li class="nav-item">
												<a class="dropdown-item" href="project-overview.html">Overview</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="project-task.html">Task</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="project-budget.html">Budget</a>
											</li>

											<li class="nav-item">
												<a class="dropdown-item" href="project-files.html">File</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="project-team.html">Team</a>
											</li>
										</ul>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">CRM</a>
								<ul class="dropdown-menu">
									<li>
										<a href="crm-company.html" class="dropdown-item">Company</a>
									</li>
									<li>
										<a href="crm-contacts.html" class="dropdown-item">Contacts</a>
									</li>
									<li>
										<a class="dropdown-item" href="deals.html">
											Deals
											<span class="badge text-bg-primary ms-2">New</span>
										</a>
									</li>
									<li>
										<a class="dropdown-item" href="deals-single.html">
											Deals Single
											<span class="badge text-bg-primary ms-2">New</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Invoice</a>
								<ul class="dropdown-menu">
									<li>
										<a href="invoice-list.html" class="dropdown-item">List</a>
									</li>
									<li>
										<a href="invoice-detail.html" class="dropdown-item">Details</a>
									</li>
									<li>
										<a href="invoice-generator.html" class="dropdown-item">Create Invoice</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Profile</a>
								<ul class="dropdown-menu">
									<li>
										<a href="profile-overview.html" class="dropdown-item">Overview</a>
									</li>
									<li>
										<a href="profile-project.html" class="dropdown-item">Project</a>
									</li>
									<li>
										<a href="profile-file.html" class="dropdown-item">Files</a>
									</li>
									<li>
										<a href="profile-team.html" class="dropdown-item">Team</a>
									</li>
									<li>
										<a href="profile-followers.html" class="dropdown-item">Followers</a>
									</li>
									<li>
										<a href="profile-activity.html" class="dropdown-item">Activity</a>
									</li>
									<li>
										<a class="dropdown-item" href="profile-settings.html">Settings</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Blog</a>
								<ul class="dropdown-menu">
									<li>
										<a class="dropdown-item" href="blog-author.html">Author</a>
									</li>
									<li>
										<a class="dropdown-item" href="blog-author-detail.html">Detail</a>
									</li>
									<li>
										<a class="dropdown-item" href="create-blog-post.html">Create Post</a>
									</li>
								</ul>
							</li>
						</ul>
					</li>
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarAuthentication" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Authentication</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarAuthentication">
							<li>
								<a class="dropdown-item" href="sign-in.html">Sign In</a>
							</li>
							<li>
								<a class="dropdown-item" href="sign-up.html">Sign Up</a>
							</li>
							<li>
								<a class="dropdown-item" href="forget-password.html">Forgot Password</a>
							</li>
							<li>
								<a class="dropdown-item" href="maintenance.html">maintenance</a>
							</li>
							<li>
								<a class="dropdown-item" href="404-error.html">404 Error</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="layoutsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">Layouts</a>
						<ul class="dropdown-menu dropdown-menu-start" aria-labelledby="layoutsDropdown">
							<li><span class="dropdown-header">Layouts</span></li>
							<li class="nav-item">
								<a class="dropdown-item" href="../index.html">Default</a>
							</li>

							<li class="nav-item">
								<a class="dropdown-item" href="index.html">Horizontal</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarPages" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Pages</a>
						<ul class="dropdown-menu" aria-labelledby="navbarPages">
							<li>
								<a class="dropdown-item" href="pricing.html">Pricing</a>
							</li>
							<li>
								<a class="dropdown-item" href="starter.html">Starter</a>
							</li>

							<li>
								<a class="dropdown-item" href="maintenance.html">Maintenance</a>
							</li>
							<li>
								<a class="dropdown-item" href="404-error.html">404 Error</a>
							</li>
						</ul>
					</li>
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarBaseUI" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Components</a>
						<div class="dropdown-menu dropdown-menu-xl" aria-labelledby="navbarBaseUI">
							<div class="row">
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="components/accordions.html" class="dropdown-item">Accordions</a>
										</li>
										<li class="nav-item">
											<a class="dropdown-item" href="components/alerts.html">Alert</a>
										</li>

										<li class="nav-item">
											<a href="components/badge.html" class="dropdown-item">Badge</a>
										</li>

										<li class="nav-item">
											<a href="components/breadcrumb.html" class="dropdown-item">Breadcrumb</a>
										</li>
										<li class="nav-item">
											<a href="components/buttons.html" class="dropdown-item">Buttons</a>
										</li>
										<li class="nav-item">
											<a href="components/button-group.html" class="dropdown-item">Button group</a>
										</li>
										<li class="nav-item">
											<a href="components/card.html" class="dropdown-item">Card</a>
										</li>
										<li class="nav-item">
											<a href="components/carousel.html" class="dropdown-item">Carousel</a>
										</li>
										<li class="nav-item">
											<a href="components/close-button.html" class="dropdown-item">Close Button</a>
										</li>
									</ul>
								</div>
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="components/collapse.html" class="dropdown-item">Collapse</a>
										</li>
										<li class="nav-item">
											<a href="components/dropdowns.html" class="dropdown-item">Dropdowns</a>
										</li>
										<li class="nav-item">
											<a href="components/forms.html" class="dropdown-item">Forms</a>
										</li>

										<li class="nav-item">
											<a href="components/list-group.html" class="dropdown-item">List group</a>
										</li>
										<li class="nav-item">
											<a href="components/modal.html" class="dropdown-item">Modal</a>
										</li>
										<li class="nav-item">
											<a href="components/navs-tabs.html" class="dropdown-item">Navs and tabs</a>
										</li>
										<li class="nav-item">
											<a href="components/navbar.html" class="dropdown-item">Navbar</a>
										</li>
										<li class="nav-item">
											<a href="components/offcanvas.html" class="dropdown-item">Offcanvas</a>
										</li>
										<li class="nav-item">
											<a href="components/pagination.html" class="dropdown-item">Pagination</a>
										</li>
									</ul>
								</div>
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="components/placeholders.html" class="dropdown-item">Placeholders</a>
										</li>
										<li class="nav-item">
											<a href="components/popovers.html" class="dropdown-item">Popovers</a>
										</li>
										<li class="nav-item">
											<a href="components/progress.html" class="dropdown-item">Progress</a>
										</li>
										<li class="nav-item">
											<a href="components/scrollspy.html" class="dropdown-item">Scrollspy</a>
										</li>
										<li class="nav-item">
											<a href="components/spinners.html" class="dropdown-item">Spinners</a>
										</li>
										<li class="nav-item">
											<a href="components/tables.html" class="dropdown-item">Tables</a>
										</li>
										<li class="nav-item">
											<a href="components/toasts.html" class="dropdown-item">Toasts</a>
										</li>
										<li class="nav-item">
											<a href="components/tooltips.html" class="dropdown-item">Tooltips</a>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<i data-feather="more-horizontal" class="icon-xxs"></i>
						</a>
						<div class="dropdown-menu dropdown-menu-md" aria-labelledby="navbarDropdown">
							<div class="list-group">
								<a class="list-group-item list-group-item-action border-0" href="../docs/index.html">
									<div class="d-flex align-items-center">
										<i data-feather="file-text" class="icon-sm text-primary"></i>

										<div class="ms-3">
											<h5 class="mb-0">Documentations</h5>
											<p class="mb-0 fs-6">Browse the all documentation</p>
										</div>
									</div>
								</a>
								<a class="list-group-item list-group-item-action border-0" href="../docs/changelog.html">
									<div class="d-flex align-items-center">
										<i data-feather="layers" class="icon-sm text-primary"></i>
										<div class="ms-3">
											<h5 class="mb-0">
												Changelog
												<span class="text-primary ms-1">v1.0.0</span>
											</h5>
											<p class="mb-0 fs-6">See what's new</p>
										</div>
									</div>
								</a>
							</div>
						</div>
					</li>
				</ul>
			</div>
		</nav>
	</div>
</div>


    <!-- page content -->
   <div id="app-content">
      <div class="app-content-area">
        <div class="container-fluid">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-12">
              <!-- Page header -->
              <div class="d-flex justify-content-between align-items-center mb-5">
                <h3 class="mb-0 ">Finance</h3>
                <a href="#!" class="btn btn-primary">Button</a>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-xxl-6 col-12 mb-5">
              <div class="card h-100">
                <div class="card-body">
                  <small>Saving Account</small>
                  <div class="d-flex justify-content-between mt-3 mb-8">
                    <div>
                      <h3 class="mb-0">First Saving Account</h3>
                      <small>**** **** **** 2345</small>
                    </div>
                    <div class="text-end">
                      <h3 class="mb-0">
                        <span class="text-muted me-1">$</span>68,345.23
                      </h3>
                      <small>Available Funds</small>
                    </div>
                  </div>
                  <div class="d-flex justify-content-between">
                    <div class="d-flex">
                      <div class="d-flex align-items-center">
                        <div class="icon-md icon-shape bg-primary-soft rounded-3 text-primary">
                          <i data-feather="arrow-up" class="icon-xs"></i>
                        </div>
                        <div class="ms-2 lh-1">
                          <h4 class="mb-0">3,456.87</h4>
                          <small>Income</small>
                        </div>
                      </div>
                      <div class="d-flex align-items-center ms-6">
                        <div class="icon-md icon-shape bg-danger-soft text-danger rounded-3">
                          <i data-feather="arrow-down" class="icon-xs"></i>
                        </div>
                        <div class="ms-2 lh-1">
                          <h4 class="mb-0">1,538.23</h4>
                          <small>Expenses</small>
                        </div>
                      </div>
                    </div>
                    <div class="d-none d-md-block">
                      <a href="#!" class="btn btn-primary"> + Add Money</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xxl-6 col-12 mb-5">
              <div class="row">
                <div class="col-lg-4 col-12 mb-5 mb-lg-0">
                  <div class="card h-100 ">
                    <div class="card-body ">
                      <div class="mb-9">
                        <div class="icon-shape icon-xxl rounded-circle bg-primary-soft">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                            class="bi bi-wallet text-primary" viewBox="0 0 16 16">
                            <path
                              d="M0 3a2 2 0 0 1 2-2h13.5a.5.5 0 0 1 0 1H15v2a1 1 0 0 1 1 1v8.5a1.5 1.5 0 0 1-1.5 1.5h-12A2.5 2.5 0 0 1 0 12.5V3zm1 1.732V12.5A1.5 1.5 0 0 0 2.5 14h12a.5.5 0 0 0 .5-.5V5H2a1.99 1.99 0 0 1-1-.268zM1 3a1 1 0 0 0 1 1h12V2H2a1 1 0 0 0-1 1z" />
                          </svg>

                        </div>
                      </div>
                      <span>Total Balance</span>
                      <h3 class="mb-0 fw-bold">$ 6,234.78</h3>
                    </div>
                  </div>

                </div>
                <div class="col-lg-4 col-12 mb-5 mb-lg-0">
                  <div class="card h-100">
                    <div class="card-body">
                      <div class="mb-9">
                        <div class="icon-shape icon-xxl rounded-circle bg-danger-soft">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor "
                            class="bi bi-piggy-bank text-danger" viewBox="0 0 16 16">
                            <path
                              d="M5 6.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0zm1.138-1.496A6.613 6.613 0 0 1 7.964 4.5c.666 0 1.303.097 1.893.273a.5.5 0 0 0 .286-.958A7.602 7.602 0 0 0 7.964 3.5c-.734 0-1.441.103-2.102.292a.5.5 0 1 0 .276.962z" />
                            <path fill-rule="evenodd"
                              d="M7.964 1.527c-2.977 0-5.571 1.704-6.32 4.125h-.55A1 1 0 0 0 .11 6.824l.254 1.46a1.5 1.5 0 0 0 1.478 1.243h.263c.3.513.688.978 1.145 1.382l-.729 2.477a.5.5 0 0 0 .48.641h2a.5.5 0 0 0 .471-.332l.482-1.351c.635.173 1.31.267 2.011.267.707 0 1.388-.095 2.028-.272l.543 1.372a.5.5 0 0 0 .465.316h2a.5.5 0 0 0 .478-.645l-.761-2.506C13.81 9.895 14.5 8.559 14.5 7.069c0-.145-.007-.29-.02-.431.261-.11.508-.266.705-.444.315.306.815.306.815-.417 0 .223-.5.223-.461-.026a.95.95 0 0 0 .09-.255.7.7 0 0 0-.202-.645.58.58 0 0 0-.707-.098.735.735 0 0 0-.375.562c-.024.243.082.48.32.654a2.112 2.112 0 0 1-.259.153c-.534-2.664-3.284-4.595-6.442-4.595zM2.516 6.26c.455-2.066 2.667-3.733 5.448-3.733 3.146 0 5.536 2.114 5.536 4.542 0 1.254-.624 2.41-1.67 3.248a.5.5 0 0 0-.165.535l.66 2.175h-.985l-.59-1.487a.5.5 0 0 0-.629-.288c-.661.23-1.39.359-2.157.359a6.558 6.558 0 0 1-2.157-.359.5.5 0 0 0-.635.304l-.525 1.471h-.979l.633-2.15a.5.5 0 0 0-.17-.534 4.649 4.649 0 0 1-1.284-1.541.5.5 0 0 0-.446-.275h-.56a.5.5 0 0 1-.492-.414l-.254-1.46h.933a.5.5 0 0 0 .488-.393zm12.621-.857a.565.565 0 0 1-.098.21.704.704 0 0 1-.044-.025c-.146-.09-.157-.175-.152-.223a.236.236 0 0 1 .117-.173c.049-.027.08-.021.113.012a.202.202 0 0 1 .064.199z" />
                          </svg>
                        </div>
                      </div>
                      <span>Total Spending</span>
                      <h3>$ 8,123.82</h3>
                    </div>
                  </div>

                </div>
                <div class="col-lg-4 col-12 mb-5 mb-lg-0">
                  <div class="card h-100">
                    <div class="card-body">
                      <div class="mb-9">
                        <div class="icon-shape icon-xxl rounded-circle bg-success-soft">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                            class="bi bi-cash text-success" viewBox="0 0 16 16">
                            <path d="M8 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" />
                            <path
                              d="M0 4a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V4zm3 0a2 2 0 0 1-2 2v4a2 2 0 0 1 2 2h10a2 2 0 0 1 2-2V6a2 2 0 0 1-2-2H3z" />
                          </svg>
                        </div>
                      </div>
                      <span>Total Saved</span>
                      <h3>$ 68,345.23</h3>
                    </div>
                  </div>

                </div>
              </div>

            </div>
          </div>
          <div class="row">
            <div class="col-xxl-3 col-lg-6 mb-5">
              <div class="position-relative">
                <div class="card-selected card-lift">
                  <div>
                    <input class="form-check-input mt-4 position-absolute start-0 top-0 z-1 ms-4 mt-4" type="radio" name="flexRadioDefault" id="flexRadioDefault1" />
                    <div class="card">
                      <div class="card-body">
                        <div class="d-flex justify-content-end mb-6">
                          <h4 class="text-color-change">CARD</h4>
                        </div>
                        <div class="mb-6">
                          <small>**** **** **** 2345</small>
                        </div>
                        <div class="d-flex justify-content-between">
                          <div>
                            <small>Available Funds</small>
                            <h4 class="text-color-change mb-0">
                              $ 68,345.23
                            </h4>
                          </div>
                          <div class="d-flex">
                            <div>
                              <small>Expires</small>
                              <h4 class="text-color-change mb-0">12/25</h4>
                            </div>
                            <div class="ms-6">
                              <small>CVV</small>
                              <h4 class="text-color-change mb-0">123</h4>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xxl-3 col-lg-6 mb-5">
              <div class="position-relative">
                <div class="card-selected card-lift">
                  <div>
                    <input class="form-check-input mt-4 position-absolute start-0 top-0 z-1 ms-4 mt-4" type="radio" name="flexRadioDefault" id="flexRadioDefault2" />
                    <div class="card">
                      <div class="card-body">
                        <div class="d-flex justify-content-end mb-6">
                          <h4 class="text-color-change">CARD</h4>
                        </div>
                        <div class="mb-6">
                          <small>**** **** **** 9472</small>
                        </div>
                        <div class="d-flex justify-content-between">
                          <div>
                            <small>Available Funds</small>
                            <h4 class="text-color-change mb-0">$ 8,567.43</h4>
                          </div>
                          <div class="d-flex">
                            <div>
                              <small>Expires</small>
                              <h4 class="text-color-change mb-0">12/23</h4>
                            </div>
                            <div class="ms-6">
                              <small>CVV</small>
                              <h4 class="text-color-change mb-0">235</h4>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xxl-3 col-lg-6 mb-5">
              <div class="position-relative">
                <div class="card-selected card-lift">
                  <div>
                    <input class="form-check-input mt-4 position-absolute start-0 top-0 z-1 ms-4 mt-4" type="radio" name="flexRadioDefault" id="flexRadioDefault3" checked />
                    <div class="card">
                      <div class="card-body">
                        <div class="d-flex justify-content-end mb-6">
                          <h4 class="text-color-change">CARD</h4>
                        </div>
                        <div class="mb-6">
                          <small>**** **** **** 1241</small>
                        </div>
                        <div class="d-flex justify-content-between">
                          <div>
                            <small>Available Funds</small>
                            <h4 class="text-color-change mb-0">$ 6,234.78</h4>
                          </div>
                          <div class="d-flex">
                            <div>
                              <small>Expires</small>
                              <h4 class="text-color-change mb-0">12/24</h4>
                            </div>
                            <div class="ms-6">
                              <small>CVV</small>
                              <h4 class="text-color-change mb-0">456</h4>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xxl-3 col-lg-6 mb-5">
              <div class="position-relative">
                <div class="card-selected card-lift">
                  <div>
                    <input class="form-check-input mt-4 position-absolute start-0 top-0 z-1 ms-4 mt-4" type="radio" name="flexRadioDefault" id="flexRadioDefault4" />
                    <div class="card">
                      <div class="card-body">
                        <div class="d-flex justify-content-end mb-6">
                          <h4 class="text-color-change">CARD</h4>
                        </div>
                        <div class="mb-6">
                          <small>**** **** **** 8470</small>
                        </div>
                        <div class="d-flex justify-content-between">
                          <div>
                            <small>Available Funds</small>
                            <h4 class="text-color-change mb-0">$ 9,231.22</h4>
                          </div>
                          <div class="d-flex">
                            <div>
                              <small>Expires</small>
                              <h4 class="text-color-change mb-0">12/20</h4>
                            </div>
                            <div class="ms-6">
                              <small>CVV</small>
                              <h4 class="text-color-change mb-0">845</h4>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-xl-4 col-12 mb-5 mb-xl-0">
              <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h4 class="mb-0">Cashflow</h4>
                  <a href="#!" class="btn btn-primary btn-sm">Check Details
                    <i data-feather="arrow-right" class="icon-xs">
                    </i></a>
                </div>
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <span>Daily</span>
                      <h4 class="mb-0 mt-1">$ 368.49</h4>
                    </div>
                    <div>
                      <span>Weekly</span>
                      <h4 class="mb-0 mt-1">$ 2,598.45</h4>
                    </div>
                    <div>
                      <span>Monthly</span>
                      <h4 class="mb-0 mt-1">$ 9,600.00</h4>
                    </div>
                  </div>
                  <div id="cashFlowChart" class="my-10 justify-content-center d-flex"> </div>
                  <div class="row justify-content-center">
                    <div class="col-md-4 col-6">
                      <div class="rounded-3 d-flex bg-warning-soft text-warning p-4">
                        <div>
                          <i data-feather="arrow-down" class="icon-xs"></i>
                        </div>
                        <div class="ms-2 lh-1">
                          <h4 class="mb-0 text-warning">1,538.23</h4>
                          <small>Expenses</small>
                        </div>
                      </div>
                    </div>
                    <div class=" col-md-4 col-6">

                      <div class="rounded-3 d-flex bg-primary-soft  p-4 text-primary ">
                        <div>
                          <i data-feather="arrow-up" class="text-primary icon-xs"></i>
                        </div>
                        <div class="ms-2 lh-1">
                          <h4 class="mb-0 text-primary">3,456.87</h4>
                          <small>Income</small>
                        </div>
                      </div>
                    </div>


                  </div>


                </div>
              </div>
            </div>

            <div class="col-xl-8 col-12 mb-5 mb-xl-0">
              <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h4 class="mb-0">Latest Transactions</h4>
                  <a href="#!" class="btn btn-primary btn-sm">All transactions
                    <i data-feather="arrow-right" class="icon-xs">
                    </i></a>
                </div>
                <div class="card-body">
                  <div class="table-responsive table-card">
                    <table class="table text-nowrap table-centered mb-0">
                      <thead class="table-light">
                        <tr>
                          <th scope="col">Date</th>
                          <th scope="col">Type</th>
                          <th scope="col">Payment</th>
                          <th scope="col">Amount</th>
                          <th></th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>

                          <td>25/07/2023</td>
                          <td>Clothes</td>
                          <td>Card payment</td>
                          <td><span class="text-danger">- $15.00</span></td>
                          <td>
                            <div class="dropdown dropstart">
                              <a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                  stroke-linejoin="round" class="feather feather-more-vertical">
                                  <circle cx="12" cy="12" r="1"></circle>
                                  <circle cx="12" cy="5" r="1"></circle>
                                  <circle cx="12" cy="19" r="1"></circle>
                                </svg>
                              </a>
                              <ul class="dropdown-menu">
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Another action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Something else here</a>
                                </li>
                              </ul>
                            </div>
                          </td>
                        </tr>
                        <tr>

                          <td>20/07/2023</td>
                          <td>Food</td>
                          <td>Transfer</td>
                          <td><span class="text-danger">- $15.00</span></td>
                          <td>
                            <div class="dropdown dropstart">
                              <a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                  stroke-linejoin="round" class="feather feather-more-vertical">
                                  <circle cx="12" cy="12" r="1"></circle>
                                  <circle cx="12" cy="5" r="1"></circle>
                                  <circle cx="12" cy="19" r="1"></circle>
                                </svg>
                              </a>
                              <ul class="dropdown-menu">
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Another action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Something else here</a>
                                </li>
                              </ul>
                            </div>
                          </td>
                        </tr>
                        <tr>

                          <td>16/07/2023</td>
                          <td>Medical Checkup</td>
                          <td>Salary</td>
                          <td><span class="text-success">$9000.00</span></td>
                          <td>
                            <div class="dropdown dropstart">
                              <a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                  stroke-linejoin="round" class="feather feather-more-vertical">
                                  <circle cx="12" cy="12" r="1"></circle>
                                  <circle cx="12" cy="5" r="1"></circle>
                                  <circle cx="12" cy="19" r="1"></circle>
                                </svg>
                              </a>
                              <ul class="dropdown-menu">
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Another action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Something else here</a>
                                </li>
                              </ul>
                            </div>
                          </td>
                        </tr>
                        <tr>

                          <td>12/07/2023</td>
                          <td>Clothes</td>
                          <td>Freelancing</td>
                          <td><span class="text-success">$1300.00</span></td>
                          <td>
                            <div class="dropdown dropstart">
                              <a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                  stroke-linejoin="round" class="feather feather-more-vertical">
                                  <circle cx="12" cy="12" r="1"></circle>
                                  <circle cx="12" cy="5" r="1"></circle>
                                  <circle cx="12" cy="19" r="1"></circle>
                                </svg>
                              </a>
                              <ul class="dropdown-menu">
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Another action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Something else here</a>
                                </li>
                              </ul>
                            </div>
                          </td>
                        </tr>
                        <tr>

                          <td>06/07/2023</td>
                          <td>Financial</td>
                          <td>Card payment</td>
                          <td><span class="text-danger">- $25.00</span></td>
                          <td>
                            <div class="dropdown dropstart">
                              <a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                  stroke-linejoin="round" class="feather feather-more-vertical">
                                  <circle cx="12" cy="12" r="1"></circle>
                                  <circle cx="12" cy="5" r="1"></circle>
                                  <circle cx="12" cy="19" r="1"></circle>
                                </svg>
                              </a>
                              <ul class="dropdown-menu">
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Another action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Something else here</a>
                                </li>
                              </ul>
                            </div>
                          </td>
                        </tr>
                        <tr>

                          <td>06/06/2023</td>
                          <td>Subscriptions</td>
                          <td>Card payment</td>
                          <td><span class="text-danger">- $115.00</span></td>
                          <td>
                            <div class="dropdown dropstart">
                              <a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                  stroke-linejoin="round" class="feather feather-more-vertical">
                                  <circle cx="12" cy="12" r="1"></circle>
                                  <circle cx="12" cy="5" r="1"></circle>
                                  <circle cx="12" cy="19" r="1"></circle>
                                </svg>
                              </a>
                              <ul class="dropdown-menu">
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Another action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Something else here</a>
                                </li>
                              </ul>
                            </div>
                          </td>
                        </tr>
                        <tr>

                          <td>06/05/2023</td>
                          <td>Rent</td>
                          <td>Card payment</td>
                          <td><span class="text-danger">- $46.00</span></td>
                          <td>
                            <div class="dropdown dropstart">
                              <a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                  stroke-linejoin="round" class="feather feather-more-vertical">
                                  <circle cx="12" cy="12" r="1"></circle>
                                  <circle cx="12" cy="5" r="1"></circle>
                                  <circle cx="12" cy="19" r="1"></circle>
                                </svg>
                              </a>
                              <ul class="dropdown-menu">
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Another action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Something else here</a>
                                </li>
                              </ul>
                            </div>
                          </td>
                        </tr>
                        <tr>

                          <td>03/05/2023</td>
                          <td>Maintenance</td>
                          <td>Card payment</td>
                          <td><span class="text-danger">- $15.00</span></td>
                          <td>
                            <div class="dropdown dropstart">
                              <a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                  stroke-linejoin="round" class="feather feather-more-vertical">
                                  <circle cx="12" cy="12" r="1"></circle>
                                  <circle cx="12" cy="5" r="1"></circle>
                                  <circle cx="12" cy="19" r="1"></circle>
                                </svg>
                              </a>
                              <ul class="dropdown-menu">
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Another action</a></li>
                                <li><a class="dropdown-item d-flex align-items-center" href="#!">Something else here</a>
                                </li>
                              </ul>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>


                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Scripts -->
  <!-- apexchart js -->

  <!-- Libs JS -->

<script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../assets/libs/feather-icons/dist/feather.min.js"></script>
<script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>

<!-- Theme JS -->
<script src="../assets/js/theme.min.js"></script>

  <script src="../assets/libs/apexcharts/dist/apexcharts.min.js"></script>
  <script src="../assets/js/vendors/chart.js"></script>


</body>


<!-- Mirrored from dashui.codescandy.com/dashuipro/horizontal/dashboard-finance.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:30:57 GMT -->
</html>