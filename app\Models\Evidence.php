<?php

namespace App\Models;

use App\Traits\HasUserTracking;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Evidence extends Model implements HasMedia
{
    use HasFactory;
    use HasUserTracking;
    use LogsActivity;
    use InteractsWithMedia;
    use HasSlug;
    use SoftDeletes;

    protected $table = 'evidence';

    protected $fillable = [
        'evidence_number',
        'title',
        'description',
        'type',
        'category',
        'case_id',
        'criminal_id',
        'collected_at',
        'collected_by',
        'collection_location',
        'collection_method',
        'collection_notes',
        'quantity',
        'weight',
        'dimensions',
        'color',
        'material',
        'condition',
        'storage_location',
        'storage_container',
        'storage_conditions',
        'storage_notes',
        'current_custodian_id',
        'custody_received_at',
        'custody_notes',
        'status',
        'disposition',
        'disposition_date',
        'disposition_notes',
        'is_admissible',
        'admissibility_notes',
        'requires_special_handling',
        'special_handling_instructions',
        'requires_analysis',
        'analysis_type',
        'analysis_status',
        'analysis_requested_at',
        'analysis_completed_at',
        'analysis_results',
        'analyzed_by',
        'estimated_value',
        'serial_number',
        'brand_model',
        'additional_notes',
    ];

    protected $casts = [
        'collected_at' => 'datetime',
        'custody_received_at' => 'datetime',
        'disposition_date' => 'date',
        'analysis_requested_at' => 'datetime',
        'analysis_completed_at' => 'datetime',
        'estimated_value' => 'decimal:2',
        'is_admissible' => 'boolean',
        'requires_special_handling' => 'boolean',
        'requires_analysis' => 'boolean',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('evidence_number')
            ->saveSlugsTo('slug');
    }

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'evidence_number', 'title', 'type', 'status', 'current_custodian_id',
                'storage_location', 'analysis_status'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Register media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('evidence_photos')
            ->acceptsMimeTypes(['image/jpeg', 'image/png']);

        $this->addMediaCollection('evidence_documents')
            ->acceptsMimeTypes(['application/pdf', 'application/msword']);

        $this->addMediaCollection('analysis_reports')
            ->acceptsMimeTypes(['application/pdf', 'application/msword']);
    }

    /**
     * Register media conversions.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(300)
            ->sharpen(10);

        $this->addMediaConversion('preview')
            ->width(800)
            ->height(600)
            ->quality(90);
    }

    // Relationships
    public function case()
    {
        return $this->belongsTo(Cases::class, 'case_id');
    }

    public function criminal()
    {
        return $this->belongsTo(Criminal::class, 'criminal_id');
    }

    public function currentCustodian()
    {
        return $this->belongsTo(User::class, 'current_custodian_id');
    }

    public function analyzedBy()
    {
        return $this->belongsTo(User::class, 'analyzed_by');
    }

    // Scopes
    public function scopeInStorage($query)
    {
        return $query->where('status', 'In Storage');
    }

    public function scopeRequiresAnalysis($query)
    {
        return $query->where('requires_analysis', true)
                    ->where('analysis_status', 'Pending');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByCustodian($query, $custodianId)
    {
        return $query->where('current_custodian_id', $custodianId);
    }

    // Accessors
    public function getDaysInStorageAttribute()
    {
        return $this->custody_received_at->diffInDays(now());
    }

    public function getIsOverdueForAnalysisAttribute()
    {
        if (!$this->requires_analysis || $this->analysis_status !== 'Pending') {
            return false;
        }

        return $this->analysis_requested_at &&
               $this->analysis_requested_at->diffInDays(now()) > 7;
    }
}
