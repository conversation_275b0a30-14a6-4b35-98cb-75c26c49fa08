<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('badge_number')->unique()->nullable()->after('email');
            $table->string('phone')->nullable()->after('badge_number');
            $table->string('department')->nullable()->after('phone');
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending'])->default('pending')->after('department');
            $table->timestamp('last_login')->nullable()->after('status');
            $table->boolean('is_online')->default(false)->after('last_login');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'badge_number',
                'phone',
                'department',
                'status',
                'last_login',
                'is_online'
            ]);
        });
    }
};
