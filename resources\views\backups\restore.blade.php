@extends('layouts.app')

@section('title', 'Restore Backup - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Restore Backup</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'Backup Management', 'url' => route('backups.index')],
                ['title' => 'Restore Backup']
            ]" />
        </div>
        <div>
            <a href="{{ route('backups.index') }}" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Backups
            </a>
        </div>
    </div>

    <!-- Warning Alert -->
    <div class="alert alert-warning">
        <div class="d-flex align-items-center">
            <i data-feather="alert-triangle" class="icon-lg me-3"></i>
            <div>
                <h5 class="alert-heading mb-1">Important Warning</h5>
                <p class="mb-0">Restoring a backup will overwrite current system data. This action cannot be undone. Please ensure you have a current backup before proceeding.</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Available Backups -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i data-feather="hard-drive" class="icon-sm me-2"></i>
                        Available Backups
                    </h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="40"></th>
                                    <th>Backup Name</th>
                                    <th>Type</th>
                                    <th>Size</th>
                                    <th>Created</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="table-success">
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="backup_selection" value="1" id="backup1">
                                        </div>
                                    </td>
                                    <td>
                                        <label for="backup1" class="form-label mb-0">
                                            <strong>Daily_Full_Backup_2024_12_15</strong>
                                            <br><small class="text-muted">Complete system backup</small>
                                        </label>
                                    </td>
                                    <td><span class="badge bg-primary">Full</span></td>
                                    <td>2.4 GB</td>
                                    <td>15/12/2024 02:00 AM</td>
                                    <td><span class="badge bg-success">Verified</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="backup_selection" value="2" id="backup2">
                                        </div>
                                    </td>
                                    <td>
                                        <label for="backup2" class="form-label mb-0">
                                            <strong>Weekly_Database_Backup_2024_12_08</strong>
                                            <br><small class="text-muted">Database only backup</small>
                                        </label>
                                    </td>
                                    <td><span class="badge bg-info">Database</span></td>
                                    <td>450 MB</td>
                                    <td>08/12/2024 03:00 AM</td>
                                    <td><span class="badge bg-success">Verified</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="backup_selection" value="3" id="backup3">
                                        </div>
                                    </td>
                                    <td>
                                        <label for="backup3" class="form-label mb-0">
                                            <strong>Manual_Backup_2024_12_10</strong>
                                            <br><small class="text-muted">Manual backup before system update</small>
                                        </label>
                                    </td>
                                    <td><span class="badge bg-primary">Full</span></td>
                                    <td>2.1 GB</td>
                                    <td>10/12/2024 10:30 AM</td>
                                    <td><span class="badge bg-success">Verified</span></td>
                                </tr>
                                <tr class="table-warning">
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="backup_selection" value="4" id="backup4" disabled>
                                        </div>
                                    </td>
                                    <td>
                                        <label for="backup4" class="form-label mb-0 text-muted">
                                            <strong>Emergency_Backup_2024_12_05</strong>
                                            <br><small class="text-muted">Emergency backup - corrupted</small>
                                        </label>
                                    </td>
                                    <td><span class="badge bg-warning">Files</span></td>
                                    <td>1.8 GB</td>
                                    <td>05/12/2024 08:15 PM</td>
                                    <td><span class="badge bg-danger">Corrupted</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Restore Options -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i data-feather="settings" class="icon-sm me-2"></i>
                        Restore Options
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>What to Restore</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="restore_database" checked>
                                <label class="form-check-label" for="restore_database">
                                    Database Tables
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="restore_files" checked>
                                <label class="form-check-label" for="restore_files">
                                    System Files
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="restore_uploads" checked>
                                <label class="form-check-label" for="restore_uploads">
                                    Uploaded Files
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="restore_evidence" checked>
                                <label class="form-check-label" for="restore_evidence">
                                    Evidence Files
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="restore_settings">
                                <label class="form-check-label" for="restore_settings">
                                    System Settings
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Restore Behavior</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="restore_mode" value="full" id="restore_full" checked>
                                <label class="form-check-label" for="restore_full">
                                    Complete Restore (Overwrite All)
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="restore_mode" value="selective" id="restore_selective">
                                <label class="form-check-label" for="restore_selective">
                                    Selective Restore (Choose Components)
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="restore_mode" value="merge" id="restore_merge">
                                <label class="form-check-label" for="restore_merge">
                                    Merge with Existing Data
                                </label>
                            </div>
                            
                            <div class="mt-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="create_backup_before">
                                    <label class="form-check-label" for="create_backup_before">
                                        Create backup before restore
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="verify_backup_integrity" checked>
                                    <label class="form-check-label" for="verify_backup_integrity">
                                        Verify backup integrity first
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Restore Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="info" class="icon-sm me-2"></i>
                        Restore Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div id="restore-summary">
                        <p class="text-muted">Select a backup to see restore details.</p>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="activity" class="icon-sm me-2"></i>
                        System Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Database Status</span>
                        <span class="badge bg-success">Online</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Active Users</span>
                        <span class="badge bg-info">15 users</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Disk Space</span>
                        <span class="badge bg-warning">78% used</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Last Backup</span>
                        <span class="badge bg-primary">2 hours ago</span>
                    </div>
                </div>
            </div>

            <!-- Restore Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="play" class="icon-sm me-2"></i>
                        Restore Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-info" onclick="verifySelectedBackup()" disabled id="verifyBtn">
                            <i data-feather="check-circle" class="icon-xs me-2"></i>
                            Verify Backup
                        </button>
                        <button class="btn btn-outline-secondary" onclick="previewRestore()" disabled id="previewBtn">
                            <i data-feather="eye" class="icon-xs me-2"></i>
                            Preview Changes
                        </button>
                        <hr>
                        <button class="btn btn-warning" onclick="startRestore()" disabled id="restoreBtn">
                            <i data-feather="rotate-ccw" class="icon-xs me-2"></i>
                            Start Restore
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmRestoreModal" tabindex="-1" aria-labelledby="confirmRestoreModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="confirmRestoreModalLabel">
                        <i data-feather="alert-triangle" class="icon-sm me-2"></i>
                        Confirm Restore Operation
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <strong>Warning:</strong> This action will permanently overwrite current system data.
                    </div>
                    
                    <h6>Restore Details:</h6>
                    <ul id="restore-details">
                        <!-- Will be populated by JavaScript -->
                    </ul>
                    
                    <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" id="confirm_understanding" required>
                        <label class="form-check-label" for="confirm_understanding">
                            I understand that this action cannot be undone and will overwrite current data.
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" onclick="executeRestore()" disabled id="executeRestoreBtn">
                        <i data-feather="rotate-ccw" class="icon-xs me-2"></i>
                        Execute Restore
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Handle backup selection
    document.querySelectorAll('input[name="backup_selection"]').forEach(radio => {
        radio.addEventListener('change', function() {
            updateRestoreSummary();
            enableRestoreActions();
        });
    });
    
    // Handle confirmation checkbox
    document.getElementById('confirm_understanding').addEventListener('change', function() {
        document.getElementById('executeRestoreBtn').disabled = !this.checked;
    });
});

function updateRestoreSummary() {
    const selectedBackup = document.querySelector('input[name="backup_selection"]:checked');
    if (!selectedBackup) return;
    
    const backupRow = selectedBackup.closest('tr');
    const backupName = backupRow.querySelector('strong').textContent;
    const backupType = backupRow.querySelector('.badge').textContent;
    const backupSize = backupRow.cells[3].textContent;
    const backupDate = backupRow.cells[4].textContent;
    
    const summaryHtml = `
        <h6>Selected Backup</h6>
        <p><strong>${backupName}</strong></p>
        <ul class="list-unstyled">
            <li><strong>Type:</strong> ${backupType}</li>
            <li><strong>Size:</strong> ${backupSize}</li>
            <li><strong>Created:</strong> ${backupDate}</li>
        </ul>
        <hr>
        <h6>Estimated Time</h6>
        <p>45-60 minutes</p>
        <h6>System Downtime</h6>
        <p class="text-warning">System will be unavailable during restore</p>
    `;
    
    document.getElementById('restore-summary').innerHTML = summaryHtml;
}

function enableRestoreActions() {
    document.getElementById('verifyBtn').disabled = false;
    document.getElementById('previewBtn').disabled = false;
    document.getElementById('restoreBtn').disabled = false;
}

function verifySelectedBackup() {
    alert('Verifying backup integrity... This may take a few minutes.');
}

function previewRestore() {
    alert('Generating restore preview... This will show what changes will be made.');
}

function startRestore() {
    const selectedBackup = document.querySelector('input[name="backup_selection"]:checked');
    if (!selectedBackup) {
        alert('Please select a backup to restore.');
        return;
    }
    
    // Populate modal with restore details
    const backupRow = selectedBackup.closest('tr');
    const backupName = backupRow.querySelector('strong').textContent;
    
    const details = [
        `Backup: ${backupName}`,
        'Database will be restored',
        'System files will be restored',
        'Uploaded files will be restored',
        'Evidence files will be restored'
    ];
    
    const detailsList = document.getElementById('restore-details');
    detailsList.innerHTML = details.map(detail => `<li>${detail}</li>`).join('');
    
    // Show confirmation modal
    new bootstrap.Modal(document.getElementById('confirmRestoreModal')).show();
}

function executeRestore() {
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('confirmRestoreModal')).hide();
    
    // Start restore process
    alert('Restore process started. The system will be unavailable during this operation. You will be notified when complete.');
}
</script>
@endpush
