@extends('layouts.app')

@section('title', 'User Details - ' . config('app.name'))

@push('styles')
<style>
    .user-avatar {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        font-weight: bold;
        color: white;
        margin: 0 auto;
    }

    .status-indicator {
        position: absolute;
        bottom: 5px;
        right: 5px;
        width: 20px;
        height: 20px;
        border: 3px solid white;
        border-radius: 50%;
    }

    .status-online {
        background-color: #28a745;
    }

    .status-offline {
        background-color: #6c757d;
    }

    .action-btn {
        transition: all 0.3s ease;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .info-item {
        border-bottom: 1px solid #eee;
        padding: 0.75rem 0;
    }

    .info-item:last-child {
        border-bottom: none;
    }
</style>
@endpush

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ $user->name }}</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'User Management', 'url' => route('users.index')],
                ['title' => $user->name]
            ]" />
        </div>
        <div class="d-flex gap-2">
            @if($user->id !== auth()->id())
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i data-feather="more-horizontal" class="icon-xs me-2"></i>
                    Actions
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#!" onclick="resetUserPassword({{ $user->id }})">
                        <i data-feather="key" class="icon-xs me-2"></i>Reset Password
                    </a></li>
                    <li><a class="dropdown-item" href="#!" onclick="toggleUserStatus({{ $user->id }}, '{{ $user->status }}')">
                        @if($user->status === 'active')
                            <i data-feather="user-x" class="icon-xs me-2"></i>Deactivate User
                        @else
                            <i data-feather="user-check" class="icon-xs me-2"></i>Activate User
                        @endif
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="#!" onclick="deleteUser({{ $user->id }})">
                        <i data-feather="trash-2" class="icon-xs me-2"></i>Delete User
                    </a></li>
                </ul>
            </div>
            @endif
            <a href="{{ route('users.edit', $user->id) }}" class="btn btn-primary">
                <i data-feather="edit" class="icon-xs me-2"></i>
                Edit User
            </a>
            <a href="{{ route('users.index') }}" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Users
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <!-- User Profile Card -->
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div class="position-relative d-inline-block mb-3">
                        @php
                            $initials = collect(explode(' ', $user->name))->map(fn($name) => strtoupper(substr($name, 0, 1)))->take(2)->implode('');
                        @endphp
                        <div class="user-avatar rounded-circle">
                            {{ $initials }}
                        </div>
                        <div class="status-indicator {{ $user->is_online ? 'status-online' : 'status-offline' }} rounded-circle"></div>
                    </div>
                    <h4 class="mb-1">{{ $user->name }}</h4>
                    <p class="text-muted mb-2">{{ $user->email }}</p>
                    @if($user->department)
                        <p class="text-muted mb-3">{{ $user->department }}</p>
                    @endif
                    <div class="d-flex justify-content-center gap-2 flex-wrap">
                        @php
                            $statusColors = [
                                'active' => 'bg-success',
                                'inactive' => 'bg-secondary',
                                'suspended' => 'bg-danger',
                                'pending' => 'bg-warning'
                            ];
                            $statusColor = $statusColors[$user->status] ?? 'bg-secondary';
                        @endphp
                        <span class="badge {{ $statusColor }}">{{ ucfirst($user->status) }}</span>
                        @if($user->is_online)
                            <span class="badge bg-success">Online</span>
                        @endif
                        @if($user->roles->isNotEmpty())
                            @foreach($user->roles as $role)
                                @php
                                    $roleColors = [
                                        'Super Administrator' => 'bg-danger',
                                        'Administrator' => 'bg-warning',
                                        'Inspector' => 'bg-primary',
                                        'Officer' => 'bg-success',
                                        'Forensic Analyst' => 'bg-info',
                                        'Court Liaison' => 'bg-secondary',
                                        'Viewer' => 'bg-light text-dark'
                                    ];
                                    $roleColor = $roleColors[$role->name] ?? 'bg-secondary';
                                @endphp
                                <span class="badge {{ $roleColor }}">{{ $role->name }}</span>
                            @endforeach
                        @endif
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($user->id !== auth()->id())
                        <button class="btn btn-outline-primary btn-sm action-btn" onclick="resetUserPassword({{ $user->id }})">
                            <i data-feather="key" class="icon-xs me-2"></i>
                            Reset Password
                        </button>
                        <button class="btn btn-outline-{{ $user->status === 'active' ? 'warning' : 'success' }} btn-sm action-btn"
                                onclick="toggleUserStatus({{ $user->id }}, '{{ $user->status }}')">
                            @if($user->status === 'active')
                                <i data-feather="user-x" class="icon-xs me-2"></i>
                                Deactivate User
                            @else
                                <i data-feather="user-check" class="icon-xs me-2"></i>
                                Activate User
                            @endif
                        </button>
                        @endif
                        <a href="mailto:{{ $user->email }}" class="btn btn-outline-info btn-sm action-btn">
                            <i data-feather="mail" class="icon-xs me-2"></i>
                            Send Email
                        </a>
                        <a href="{{ route('users.edit', $user->id) }}" class="btn btn-outline-secondary btn-sm action-btn">
                            <i data-feather="edit" class="icon-xs me-2"></i>
                            Edit Profile
                        </a>
                        @if($user->id !== auth()->id())
                        <button class="btn btn-outline-danger btn-sm action-btn" onclick="deleteUser({{ $user->id }})">
                            <i data-feather="trash-2" class="icon-xs me-2"></i>
                            Delete User
                        </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- User Information Tabs -->
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#personal" role="tab">
                                <i data-feather="user" class="icon-xs me-2"></i>
                                Personal Info
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#professional" role="tab">
                                <i data-feather="briefcase" class="icon-xs me-2"></i>
                                Professional
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#permissions" role="tab">
                                <i data-feather="shield" class="icon-xs me-2"></i>
                                Permissions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#activity" role="tab">
                                <i data-feather="activity" class="icon-xs me-2"></i>
                                Activity Log
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- Personal Information Tab -->
                        <div class="tab-pane fade show active" id="personal" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Full Name</label>
                                        <p class="mb-0">{{ $user->name }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Email Address</label>
                                        <p class="mb-0">
                                            <a href="mailto:{{ $user->email }}" class="text-decoration-none">{{ $user->email }}</a>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Phone Number</label>
                                        <p class="mb-0">
                                            @if($user->phone)
                                                <a href="tel:{{ $user->phone }}" class="text-decoration-none">{{ $user->phone }}</a>
                                            @else
                                                <span class="text-muted">Not provided</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Badge Number</label>
                                        <p class="mb-0">
                                            @if($user->badge_number)
                                                <code class="bg-light px-2 py-1 rounded">{{ $user->badge_number }}</code>
                                            @else
                                                <span class="text-muted">Not assigned</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Account Created</label>
                                        <p class="mb-0">{{ $user->created_at->format('d/m/Y H:i') }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Last Updated</label>
                                        <p class="mb-0">{{ $user->updated_at->format('d/m/Y H:i') }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Email Verified</label>
                                        <p class="mb-0">
                                            @if($user->email_verified_at)
                                                <span class="badge bg-success">Verified</span>
                                                <small class="text-muted d-block">{{ $user->email_verified_at->format('d/m/Y H:i') }}</small>
                                            @else
                                                <span class="badge bg-warning">Not Verified</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Account Status</label>
                                        <p class="mb-0">
                                            <span class="badge {{ $statusColor }}">{{ ucfirst($user->status) }}</span>
                                            @if($user->is_online)
                                                <span class="badge bg-success ms-1">Online</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Information Tab -->
                        <div class="tab-pane fade" id="professional" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Department</label>
                                        <p class="mb-0">
                                            @if($user->department)
                                                {{ $user->department }}
                                            @else
                                                <span class="text-muted">Not assigned</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Assigned Roles</label>
                                        <p class="mb-0">
                                            @if($user->roles->isNotEmpty())
                                                @foreach($user->roles as $role)
                                                    <span class="badge {{ $roleColors[$role->name] ?? 'bg-secondary' }} me-1">{{ $role->name }}</span>
                                                @endforeach
                                            @else
                                                <span class="text-muted">No roles assigned</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Last Login</label>
                                        <p class="mb-0">
                                            @if($user->last_login)
                                                {{ $user->last_login->format('d/m/Y H:i') }}
                                                <small class="text-muted d-block">{{ $user->last_login->diffForHumans() }}</small>
                                            @else
                                                <span class="text-muted">Never logged in</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="form-label fw-bold text-muted small">Online Status</label>
                                        <p class="mb-0">
                                            @if($user->is_online)
                                                <span class="badge bg-success">
                                                    <i data-feather="circle" class="icon-xs me-1"></i>
                                                    Online
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">
                                                    <i data-feather="circle" class="icon-xs me-1"></i>
                                                    Offline
                                                </span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Permissions Tab -->
                        <div class="tab-pane fade" id="permissions" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Assigned Roles</h6>
                                    <div class="mb-3">
                                        <span class="badge bg-primary me-2">Inspector</span>
                                        <span class="badge bg-info">Case Manager</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>System Access</h6>
                                    <div class="mb-3">
                                        <span class="badge bg-success me-2">Active</span>
                                        <span class="badge bg-warning">2FA Enabled</span>
                                    </div>
                                </div>
                            </div>
                            <h6>Permissions</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>View Cases</li>
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>Create Cases</li>
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>Edit Cases</li>
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>Manage Evidence</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>View Reports</li>
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>Generate Reports</li>
                                        <li><i data-feather="x" class="icon-xs text-danger me-2"></i>Manage Users</li>
                                        <li><i data-feather="x" class="icon-xs text-danger me-2"></i>System Settings</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Activity Log Tab -->
                        <div class="tab-pane fade" id="activity" role="tabpanel">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Logged in to system</h6>
                                        <p class="mb-1 text-muted">User logged in from IP: *************</p>
                                        <small class="text-muted">15/12/2024 08:30 AM</small>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Created new case</h6>
                                        <p class="mb-1 text-muted">Case CS2024001 - Theft investigation</p>
                                        <small class="text-muted">15/12/2024 09:15 AM</small>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-info"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Updated evidence</h6>
                                        <p class="mb-1 text-muted">Added new evidence item EV2024001</p>
                                        <small class="text-muted">15/12/2024 11:45 AM</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmationModalLabel">
                        <i data-feather="alert-triangle" class="icon-sm me-2"></i>
                        Confirm Action
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p id="confirmationMessage">Are you sure you want to perform this action?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirmActionBtn">
                        <span id="confirmBtnText">Confirm</span>
                        <span id="confirmBtnLoader" class="d-none">
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            Processing...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="successModalLabel">
                        <i data-feather="check-circle" class="icon-sm me-2"></i>
                        Success
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p id="successMessage">Action completed successfully!</p>
                    <div id="tempPasswordSection" class="d-none">
                        <div class="alert alert-info">
                            <strong>Temporary Password:</strong>
                            <code id="tempPassword"></code>
                            <button type="button" class="btn btn-sm btn-outline-info ms-2" onclick="copyToClipboard()">
                                <i data-feather="copy" class="icon-xs"></i> Copy
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div class="modal fade" id="errorModal" tabindex="-1" aria-labelledby="errorModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="errorModalLabel">
                        <i data-feather="x-circle" class="icon-sm me-2"></i>
                        Error
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p id="errorMessage">An error occurred while processing your request.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
let currentAction = null;
let currentUserId = null;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();

    // Confirmation modal action
    document.getElementById('confirmActionBtn').addEventListener('click', function() {
        executeAction();
    });
});

function showConfirmationModal(message, action, userId) {
    document.getElementById('confirmationMessage').textContent = message;
    document.getElementById('confirmBtnText').textContent = 'Confirm';
    document.getElementById('confirmBtnLoader').classList.add('d-none');
    document.getElementById('confirmActionBtn').disabled = false;

    currentAction = action;
    currentUserId = userId;

    const modal = new bootstrap.Modal(document.getElementById('confirmationModal'));
    modal.show();
}

function showSuccessModal(message, tempPassword = null) {
    document.getElementById('successMessage').textContent = message;

    const tempPasswordSection = document.getElementById('tempPasswordSection');
    if (tempPassword) {
        document.getElementById('tempPassword').textContent = tempPassword;
        tempPasswordSection.classList.remove('d-none');
    } else {
        tempPasswordSection.classList.add('d-none');
    }

    const modal = new bootstrap.Modal(document.getElementById('successModal'));
    modal.show();

    // Auto-reload after 2 seconds
    setTimeout(() => {
        location.reload();
    }, 2000);
}

function showErrorModal(message) {
    document.getElementById('errorMessage').textContent = message;
    const modal = new bootstrap.Modal(document.getElementById('errorModal'));
    modal.show();
}

function copyToClipboard() {
    const tempPassword = document.getElementById('tempPassword').textContent;
    navigator.clipboard.writeText(tempPassword).then(() => {
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i data-feather="check" class="icon-xs"></i> Copied!';
        setTimeout(() => {
            btn.innerHTML = originalText;
            feather.replace();
        }, 1000);
    });
}

// Individual user actions
function resetUserPassword(userId) {
    showConfirmationModal(
        'Are you sure you want to reset this user\'s password? A temporary password will be generated.',
        'reset_password',
        userId
    );
}

function toggleUserStatus(userId, currentStatus) {
    const action = currentStatus === 'active' ? 'deactivate' : 'activate';
    showConfirmationModal(
        `Are you sure you want to ${action} this user?`,
        'toggle_status',
        userId
    );
}

function deleteUser(userId) {
    showConfirmationModal(
        'Are you sure you want to delete this user? This action cannot be undone.',
        'delete',
        userId
    );
}

// Execute the confirmed action
function executeAction() {
    const confirmBtn = document.getElementById('confirmActionBtn');
    const confirmBtnText = document.getElementById('confirmBtnText');
    const confirmBtnLoader = document.getElementById('confirmBtnLoader');

    // Show loading state
    confirmBtnText.classList.add('d-none');
    confirmBtnLoader.classList.remove('d-none');
    confirmBtn.disabled = true;

    let url, method;

    switch(currentAction) {
        case 'reset_password':
            url = `/users/${currentUserId}/reset-password`;
            method = 'POST';
            break;
        case 'toggle_status':
            url = `/users/${currentUserId}/toggle-status`;
            method = 'POST';
            break;
        case 'delete':
            url = `/users/${currentUserId}`;
            method = 'DELETE';
            break;
    }

    fetch(url, {
        method: method,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        // Hide confirmation modal
        bootstrap.Modal.getInstance(document.getElementById('confirmationModal')).hide();

        if (data.success) {
            if (currentAction === 'delete') {
                // Redirect to users index after successful deletion
                window.location.href = '/users';
            } else {
                showSuccessModal(data.message, data.temp_password);
            }
        } else {
            showErrorModal(data.message);
        }
    })
    .catch(error => {
        // Hide confirmation modal
        bootstrap.Modal.getInstance(document.getElementById('confirmationModal')).hide();
        showErrorModal('Error: ' + error.message);
    })
    .finally(() => {
        // Reset button state
        confirmBtnText.classList.remove('d-none');
        confirmBtnLoader.classList.add('d-none');
        confirmBtn.disabled = false;
    });
}
</script>
@endpush

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
@endpush
