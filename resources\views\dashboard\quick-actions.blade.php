@extends('layouts.app')

@section('title', 'Quick Actions  - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Quick Actions</h1>
            <x-breadcrumb :items="[
                ['title' => 'Dashboard', 'url' => route('dashboard')],
                ['title' => 'Quick Actions']
            ]" />
        </div>
    </div>

    <!-- Quick Actions Grid -->
    <div class="row">
        <!-- Criminal Management Actions -->
        <div class="col-lg-6 col-md-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="mb-0">Criminal Management</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="{{ route('criminals.create') }}" class="btn btn-outline-primary d-flex align-items-center">
                            <i data-feather="user-plus" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Add New Criminal</div>
                                <small class="text-muted">Register a new criminal profile</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('criminals.index') }}" class="btn btn-outline-info d-flex align-items-center">
                            <i data-feather="search" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Search Criminals</div>
                                <small class="text-muted">Find existing criminal records</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('criminals.list') }}" class="btn btn-outline-secondary d-flex align-items-center">
                            <i data-feather="users" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">View All Criminals</div>
                                <small class="text-muted">Browse criminal database</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Case Management Actions -->
        <div class="col-lg-6 col-md-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="mb-0">Case Management</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="{{ route('cases.create') }}" class="btn btn-outline-warning d-flex align-items-center">
                            <i data-feather="folder-plus" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Create New Case</div>
                                <small class="text-muted">Start a new investigation</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('cases.active') }}" class="btn btn-outline-success d-flex align-items-center">
                            <i data-feather="folder" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Active Cases</div>
                                <small class="text-muted">View ongoing investigations</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('cases.search') }}" class="btn btn-outline-info d-flex align-items-center">
                            <i data-feather="search" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Search Cases</div>
                                <small class="text-muted">Find specific case files</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Evidence Management Actions -->
        <div class="col-lg-6 col-md-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="mb-0">Evidence Management</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="{{ route('evidence.create') }}" class="btn btn-outline-primary d-flex align-items-center">
                            <i data-feather="upload" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Submit Evidence</div>
                                <small class="text-muted">Add new evidence to case</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('evidence.index') }}" class="btn btn-outline-info d-flex align-items-center">
                            <i data-feather="archive" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Evidence Vault</div>
                                <small class="text-muted">Browse evidence storage</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('evidence.custody') }}" class="btn btn-outline-warning d-flex align-items-center">
                            <i data-feather="shield" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Chain of Custody</div>
                                <small class="text-muted">Track evidence handling</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Court Management Actions -->
        <div class="col-lg-6 col-md-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="mb-0">Court Management</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="{{ route('court.hearings') }}" class="btn btn-outline-success d-flex align-items-center">
                            <i data-feather="calendar" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Schedule Hearing</div>
                                <small class="text-muted">Book court appearances</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('court.calendar') }}" class="btn btn-outline-info d-flex align-items-center">
                            <i data-feather="clock" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Court Calendar</div>
                                <small class="text-muted">View upcoming hearings</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('court.status') }}" class="btn btn-outline-warning d-flex align-items-center">
                            <i data-feather="briefcase" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Case Status</div>
                                <small class="text-muted">Track court proceedings</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports and Analytics -->
        <div class="col-lg-6 col-md-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="mb-0">Reports & Analytics</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="{{ route('reports.crime') }}" class="btn btn-outline-primary d-flex align-items-center">
                            <i data-feather="bar-chart-2" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Crime Reports</div>
                                <small class="text-muted">Generate crime statistics</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('reports.statistics') }}" class="btn btn-outline-info d-flex align-items-center">
                            <i data-feather="pie-chart" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Case Statistics</div>
                                <small class="text-muted">Analyze case data</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('reports.analytics') }}" class="btn btn-outline-success d-flex align-items-center">
                            <i data-feather="trending-up" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Analytics Dashboard</div>
                                <small class="text-muted">Advanced data insights</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Management -->
        <div class="col-lg-6 col-md-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="mb-0">Document Management</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="{{ route('documents.upload') }}" class="btn btn-outline-primary d-flex align-items-center">
                            <i data-feather="upload-cloud" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Upload Documents</div>
                                <small class="text-muted">Add files to case</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('documents.library') }}" class="btn btn-outline-info d-flex align-items-center">
                            <i data-feather="file-text" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Document Library</div>
                                <small class="text-muted">Browse all documents</small>
                            </div>
                        </a>
                        
                        <a href="{{ route('documents.templates') }}" class="btn btn-outline-secondary d-flex align-items-center">
                            <i data-feather="copy" class="icon-sm me-3"></i>
                            <div class="text-start">
                                <div class="fw-bold">Templates</div>
                                <small class="text-muted">Use document templates</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Actions -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card border-danger">
                <div class="card-header bg-light-danger">
                    <h4 class="mb-0 text-danger">
                        <i data-feather="alert-triangle" class="icon-sm me-2"></i>
                        Emergency Actions
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <button type="button" class="btn btn-danger w-100" data-bs-toggle="modal" data-bs-target="#emergencyReportModal">
                                <i data-feather="phone" class="icon-sm me-2"></i>
                                Report Emergency
                            </button>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button type="button" class="btn btn-warning w-100">
                                <i data-feather="alert-circle" class="icon-sm me-2"></i>
                                Issue Alert
                            </button>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button type="button" class="btn btn-info w-100">
                                <i data-feather="radio" class="icon-sm me-2"></i>
                                Contact Dispatch
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
@endpush
