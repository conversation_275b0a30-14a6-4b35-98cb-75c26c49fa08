@extends('layouts.app')

@section('title', 'Edit User - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Edit User</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'User Management', 'url' => route('users.index')],
                ['title' => 'Edit User']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('users.show', $id) }}" class="btn btn-outline-info">
                <i data-feather="eye" class="icon-xs me-2"></i>
                View User
            </a>
            <a href="{{ route('users.index') }}" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Users
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('users.update', $id) }}" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        
        <!-- Personal Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="user" class="icon-sm me-2"></i>
                    Personal Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required
                               value="Inspector Kondwani Banda">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" required
                               value="<EMAIL>">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="text" class="form-control" id="phone" name="phone"
                               value="+265 999 123 456">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="badge_number" class="form-label">Badge Number <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="badge_number" name="badge_number" required
                               value="PB001">
                    </div>
                </div>
            </div>
        </div>

        <!-- Professional Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="briefcase" class="icon-sm me-2"></i>
                    Professional Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="rank" class="form-label">Rank <span class="text-danger">*</span></label>
                        <select class="form-select" id="rank" name="rank" required>
                            <option value="Constable">Constable</option>
                            <option value="Senior Constable">Senior Constable</option>
                            <option value="Sergeant">Sergeant</option>
                            <option value="Inspector" selected>Inspector</option>
                            <option value="Chief Inspector">Chief Inspector</option>
                            <option value="Superintendent">Superintendent</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="department" class="form-label">Department <span class="text-danger">*</span></label>
                        <select class="form-select" id="department" name="department" required>
                            <option value="Criminal Investigation" selected>Criminal Investigation</option>
                            <option value="Traffic Police">Traffic Police</option>
                            <option value="Forensics">Forensics</option>
                            <option value="Administration">Administration</option>
                            <option value="Community Policing">Community Policing</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="station" class="form-label">Police Station</label>
                        <select class="form-select" id="station" name="station">
                            <option value="Lilongwe Central" selected>Lilongwe Central</option>
                            <option value="Blantyre Central">Blantyre Central</option>
                            <option value="Mzuzu Central">Mzuzu Central</option>
                            <option value="Zomba Central">Zomba Central</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Access -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="shield" class="icon-sm me-2"></i>
                    System Access & Permissions
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="role" class="form-label">System Role <span class="text-danger">*</span></label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="super_admin">Super Administrator</option>
                            <option value="admin">Administrator</option>
                            <option value="inspector" selected>Inspector</option>
                            <option value="officer">Officer</option>
                            <option value="forensic_analyst">Forensic Analyst</option>
                            <option value="court_liaison">Court Liaison</option>
                            <option value="viewer">Viewer</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Account Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active" selected>Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="suspended">Suspended</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-end gap-2 mb-4">
            <a href="{{ route('users.show', $id) }}" class="btn btn-outline-secondary">
                <i data-feather="x" class="icon-xs me-2"></i>
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i data-feather="save" class="icon-xs me-2"></i>
                Update User
            </button>
        </div>
    </form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
@endpush
