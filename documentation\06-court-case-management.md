# Court Case Management Task

## Overview
Implement a comprehensive Court Case Management System that tracks ongoing court cases across various court types in Malawi, manages court proceedings, and provides detailed case information including schedules, outcomes, and legal proceedings.

## Requirements (from System-Specification-Document.md)

### Court Cases in Progress Module

#### Court Types and Hearings
- **Magistrates Court**: e.g., 50 hearings awaiting
- **Crown Court**: e.g., 30 hearings awaiting
- **Supreme Court**: e.g., 20 hearings awaiting
- **Juvenile Court**: e.g., 80 hearings awaiting
- **Tribunal Hearing**: e.g., 100 hearings awaiting

#### Features
- Display number of hearings awaiting in each court type
- Real-time updates of court schedules
- Integration with Court Details Page
- Visual representation with charts and graphs

### Court Details Page

#### Header Section
- **Case Number**: Unique identifier (alphanumeric)
- **Crime Type**: e.g., Theft, Assault, Homicide
- **Court Name**: e.g., Lilongwe Magistrates Court
- **Judge's Name**: e.g., <PERSON><PERSON>

#### Case Details Section
- **Case Status**: e.g., On Trial, Postponed, Case Closed
- **Trial Date**: DD/MM/YYYY format
- **Trial Time**: HH:MM format
- **Location**: e.g., Lilongwe High Court, Courtroom 3

#### Categories and Sub-Categories

##### Case Information
- **Case Type**: e.g., Criminal, Civil, Appeal
- **Case Category**: e.g., Murder, Theft
- **Case Description**: Detailed text description

##### Court Schedule
- **Hearing Date**: DD/MM/YYYY format
- **Hearing Time**: HH:MM format
- **Courtroom**: e.g., Courtroom 3

##### Case Outcome
- **Verdict**: e.g., Guilty, Not Guilty, Dismissed
- **Sentence**: e.g., 5 years imprisonment, MWK 100,000 fine
- **Appeal Status**: e.g., Pending, Denied, Granted

### Additional Court Scenarios

#### Postponed Case
- **Postponed Date**: DD/MM/YYYY format
- **Postponed Time**: HH:MM format
- **Reason for Postponement**: Text explanation

#### Case Closed
- **Case Closed Date**: DD/MM/YYYY format
- **Case Closed Reason**: Text explanation
- **Final Verdict**: Text description

#### Released on Parole
- **Parole Date**: DD/MM/YYYY format
- **Parole Conditions**: e.g., regular check-ins
- **Parole Expiration Date**: DD/MM/YYYY format

#### Case Dismissed
- **Dismissed Date**: DD/MM/YYYY format
- **Dismissed Reason**: Text explanation

#### Death Sentence
- **Death Sentence Date**: DD/MM/YYYY format
- **Execution Date**: DD/MM/YYYY format (optional)
- **Stay of Execution**: Text description (optional)

#### Bail Management
- **Bail Amount**: in MWK (Malawian Kwacha)
- **Bail Conditions**: e.g., curfew, regular check-ins
- **Bail Expiration Date**: DD/MM/YYYY format

#### Plea Bargain
- **Plea Bargain Date**: DD/MM/YYYY format
- **Plea Bargain Terms**: e.g., guilty plea to reduced charge

#### Appeal Process
- **Appeal Date**: DD/MM/YYYY format
- **Appeal Status**: e.g., Pending, Denied, Granted
- **Appeal Court**: e.g., Supreme Court

#### Probation
- **Probation Date**: DD/MM/YYYY format
- **Probation Conditions**: e.g., community service
- **Probation Expiration Date**: DD/MM/YYYY format

#### Restitution
- **Restitution Amount**: in MWK
- **Restitution Payment Schedule**: Text description

#### Court-Ordered Treatment
- **Treatment Type**: e.g., substance abuse treatment
- **Treatment Provider**: e.g., Lilongwe Clinic
- **Treatment Schedule**: Text description

### Additional Fields
- **Defendant's Attorney**: Name and contact information
- **Prosecutor**: Name and contact information
- **Judge's Notes**: Text field for judicial notes
- **Court Transcript**: Text or file attachment
- **Evidence List**: Linked to Evidence Details Page

## Technical Implementation

### Database Schema

#### Court Cases Table
```sql
CREATE TABLE court_cases (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    case_id BIGINT NOT NULL,
    court_type ENUM('Magistrates', 'Crown', 'Supreme', 'Juvenile', 'Tribunal') NOT NULL,
    court_name VARCHAR(200),
    judge_name VARCHAR(200),
    case_status ENUM('Scheduled', 'On Trial', 'Postponed', 'Closed', 'Dismissed') DEFAULT 'Scheduled',
    trial_date DATE,
    trial_time TIME,
    courtroom VARCHAR(100),
    case_type ENUM('Criminal', 'Civil', 'Appeal') DEFAULT 'Criminal',
    case_category VARCHAR(100),
    case_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (case_id) REFERENCES cases(id) ON DELETE CASCADE
);
```

#### Court Hearings Table
```sql
CREATE TABLE court_hearings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    court_case_id BIGINT NOT NULL,
    hearing_date DATE NOT NULL,
    hearing_time TIME NOT NULL,
    courtroom VARCHAR(100),
    hearing_type ENUM('Initial', 'Preliminary', 'Trial', 'Sentencing', 'Appeal') NOT NULL,
    status ENUM('Scheduled', 'Completed', 'Postponed', 'Cancelled') DEFAULT 'Scheduled',
    postponement_reason TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (court_case_id) REFERENCES court_cases(id) ON DELETE CASCADE
);
```

#### Court Outcomes Table
```sql
CREATE TABLE court_outcomes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    court_case_id BIGINT NOT NULL,
    verdict ENUM('Guilty', 'Not Guilty', 'Dismissed', 'No Contest') NOT NULL,
    sentence TEXT,
    sentence_date DATE,
    fine_amount DECIMAL(12,2), -- In MWK
    imprisonment_duration VARCHAR(100),
    appeal_status ENUM('None', 'Pending', 'Denied', 'Granted') DEFAULT 'None',
    appeal_date DATE,
    appeal_court VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (court_case_id) REFERENCES court_cases(id) ON DELETE CASCADE
);
```

#### Bail Information Table
```sql
CREATE TABLE bail_information (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    court_case_id BIGINT NOT NULL,
    bail_amount DECIMAL(12,2), -- In MWK
    bail_status ENUM('Granted', 'Denied', 'Pending', 'Revoked') NOT NULL,
    bail_conditions TEXT,
    bail_date DATE,
    bail_expiration_date DATE,
    surety_name VARCHAR(200),
    surety_contact VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (court_case_id) REFERENCES court_cases(id) ON DELETE CASCADE
);
```

#### Parole Information Table
```sql
CREATE TABLE parole_information (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    court_case_id BIGINT NOT NULL,
    parole_date DATE NOT NULL,
    parole_conditions TEXT,
    parole_expiration_date DATE,
    parole_officer VARCHAR(200),
    parole_officer_contact VARCHAR(50),
    status ENUM('Active', 'Completed', 'Violated', 'Revoked') DEFAULT 'Active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (court_case_id) REFERENCES court_cases(id) ON DELETE CASCADE
);
```

### Laravel Models

#### CourtCase Model
```php
class CourtCase extends Model
{
    protected $fillable = [
        'case_id', 'court_type', 'court_name', 'judge_name', 'case_status',
        'trial_date', 'trial_time', 'courtroom', 'case_type', 'case_category',
        'case_description'
    ];

    protected $casts = [
        'trial_date' => 'date',
        'trial_time' => 'datetime:H:i'
    ];

    // Relationships
    public function case() {
        return $this->belongsTo(CriminalCase::class, 'case_id');
    }

    public function hearings() {
        return $this->hasMany(CourtHearing::class);
    }

    public function outcome() {
        return $this->hasOne(CourtOutcome::class);
    }

    public function bailInformation() {
        return $this->hasOne(BailInformation::class);
    }

    public function paroleInformation() {
        return $this->hasOne(ParoleInformation::class);
    }

    // Scopes
    public function scopeByCourtType($query, $type) {
        return $query->where('court_type', $type);
    }

    public function scopeScheduled($query) {
        return $query->where('case_status', 'Scheduled');
    }

    public function scopeUpcoming($query, $days = 7) {
        return $query->where('trial_date', '>=', now())
                    ->where('trial_date', '<=', now()->addDays($days));
    }
}
```