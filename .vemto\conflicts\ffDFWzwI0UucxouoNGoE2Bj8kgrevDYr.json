{"conflicts": [{"id": "ec6d92a2-8ace-45f6-a24d-90c4958cee63", "currentContent": "<?php\n\nnamespace Database\\Seeders;\n\nuse App\\Models\\User;\n// use Illuminate\\Database\\Console\\Seeds\\WithoutModelEvents;\nuse Illuminate\\Database\\Seeder;\n\nclass DatabaseSeeder extends Seeder\n{\n    /**\n     * Seed the application's database.\n     */\n    public function run(): void\n    {\n        // User::factory(10)->withPersonalTeam()->create();\n\n        User::factory()->withPersonalTeam()->create([\n            'name' => 'Test User',\n            'email' => '<EMAIL>',\n        ]);\n    }\n}\n", "newContent": "<?php\n\nnamespace Database\\Seeders;\n\nuse Illuminate\\Database\\Seeder;\n// use Illuminate\\Database\\Console\\Seeds\\WithoutModelEvents;\n\nuse App\\Models\\User;\n\nclass DatabaseSeeder extends Seeder\n{\n    /**\n     * Seed the application's database.\n     */\n    public function run(): void\n    {\n        User::factory()\n            ->count(1)\n            ->create([\n                'email' => '<EMAIL>',\n                'password' => \\Hash::make('admin'),\n            ]);\n\n        $this->call(MembershipSeeder::class);\n        $this->call(TeamSeeder::class);\n        $this->call(TeamInvitationSeeder::class);\n    }\n}\n"}]}