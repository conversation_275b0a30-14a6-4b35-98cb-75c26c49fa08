@extends('layouts.app')

@section('title', 'Active Users - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Active Users</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'User Management', 'url' => route('users.index')],
                ['title' => 'Active Users']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('users.create') }}" class="btn btn-primary">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Add New User
            </a>
        </div>
    </div>

    <!-- Active Users Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-success">42</h4>
                    <p class="mb-0">Active Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">38</h4>
                    <p class="mb-0">Online Today</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-info">15</h4>
                    <p class="mb-0">Currently Online</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning">5</h4>
                    <p class="mb-0">Password Expiring</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Users List -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Active System Users</h4>
            <div class="d-flex gap-2">
                <a href="{{ route('users.index') }}" class="btn btn-sm btn-outline-secondary">
                    <i data-feather="users" class="icon-xs me-2"></i>
                    All Users
                </a>
                <a href="{{ route('users.inactive') }}" class="btn btn-sm btn-outline-warning">
                    <i data-feather="user-x" class="icon-xs me-2"></i>
                    Inactive Users
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Role</th>
                            <th>Department</th>
                            <th>Last Login</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-success text-white me-3">KB</div>
                                    <div>
                                        <strong>Inspector Kondwani Banda</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                        <span class="badge bg-success ms-2">Online</span>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-primary">Inspector</span></td>
                            <td>Criminal Investigation</td>
                            <td>
                                15/12/2024 08:30 AM
                                <br><small class="text-success">Currently active</small>
                            </td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('users.show', 1) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('users.edit', 1) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit User
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-warning" href="#!">
                                            <i data-feather="user-x" class="icon-xs me-2"></i>Deactivate
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-primary text-white me-3">CM</div>
                                    <div>
                                        <strong>Sergeant Chikondi Mwale</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-info">Sergeant</span></td>
                            <td>Traffic Police</td>
                            <td>
                                15/12/2024 07:45 AM
                                <br><small class="text-muted">2 hours ago</small>
                            </td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('users.show', 2) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('users.edit', 2) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit User
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-warning" href="#!">
                                            <i data-feather="user-x" class="icon-xs me-2"></i>Deactivate
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-warning text-dark me-3">TP</div>
                                    <div>
                                        <strong>Constable Thokozani Phiri</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                        <span class="badge bg-warning ms-2">Password Expiring</span>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-success">Constable</span></td>
                            <td>Community Policing</td>
                            <td>
                                14/12/2024 04:20 PM
                                <br><small class="text-muted">Yesterday</small>
                            </td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('users.show', 3) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('users.edit', 3) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit User
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-info" href="#!">
                                            <i data-feather="key" class="icon-xs me-2"></i>Reset Password
                                        </a></li>
                                        <li><a class="dropdown-item text-warning" href="#!">
                                            <i data-feather="user-x" class="icon-xs me-2"></i>Deactivate
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
@endpush
