@extends('layouts.app')

@section('title', 'System Settings  - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">System Settings</h1>
            <x-breadcrumb :items="[
                ['title' => 'Administration'],
                ['title' => 'System Settings']
            ]" />
        </div>
    </div>

    <div class="row">
        <div class="col-lg-3">
            <!-- Settings Navigation -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Settings Categories</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#general" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                            <i data-feather="settings" class="icon-xs me-2"></i>General Settings
                        </a>
                        <a href="#security" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i data-feather="shield" class="icon-xs me-2"></i>Security Settings
                        </a>
                        <a href="#notifications" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i data-feather="bell" class="icon-xs me-2"></i>Notifications
                        </a>
                        <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i data-feather="database" class="icon-xs me-2"></i>Backup & Recovery
                        </a>
                        <a href="#audit" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i data-feather="file-text" class="icon-xs me-2"></i>Audit Logs
                        </a>
                        <a href="#system" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i data-feather="monitor" class="icon-xs me-2"></i>System Information
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-9">
            <div class="tab-content">
                <!-- General Settings -->
                <div class="tab-pane fade show active" id="general">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i data-feather="settings" class="icon-sm me-2"></i>
                                General Settings
                            </h4>
                        </div>
                        <div class="card-body">
                            <form>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="system_name" class="form-label">System Name</label>
                                        <input type="text" class="form-control" id="system_name" 
                                               value="{{ config('app.name') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="organization" class="form-label">Organization</label>
                                        <input type="text" class="form-control" id="organization" 
                                               value="Malawi Police Service">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="timezone" class="form-label">Timezone</label>
                                        <select class="form-select" id="timezone">
                                            <option value="Africa/Blantyre" selected>Africa/Blantyre (CAT)</option>
                                            <option value="UTC">UTC</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="date_format" class="form-label">Date Format</label>
                                        <select class="form-select" id="date_format">
                                            <option value="d/m/Y" selected>DD/MM/YYYY</option>
                                            <option value="m/d/Y">MM/DD/YYYY</option>
                                            <option value="Y-m-d">YYYY-MM-DD</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="language" class="form-label">Default Language</label>
                                        <select class="form-select" id="language">
                                            <option value="en" selected>English</option>
                                            <option value="ny">Chichewa</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="records_per_page" class="form-label">Records Per Page</label>
                                        <select class="form-select" id="records_per_page">
                                            <option value="10">10</option>
                                            <option value="25" selected>25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="system_description" class="form-label">System Description</label>
                                    <textarea class="form-control" id="system_description" rows="3">
Criminal Justice Management System for law enforcement agencies in Malawi. Streamlines case management, evidence tracking, and court proceedings.
                                    </textarea>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i data-feather="save" class="icon-xs me-2"></i>
                                    Save General Settings
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="tab-pane fade" id="security">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i data-feather="shield" class="icon-sm me-2"></i>
                                Security Settings
                            </h4>
                        </div>
                        <div class="card-body">
                            <form>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="session_timeout" class="form-label">Session Timeout (minutes)</label>
                                        <input type="number" class="form-control" id="session_timeout" value="30">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="max_login_attempts" class="form-label">Max Login Attempts</label>
                                        <input type="number" class="form-control" id="max_login_attempts" value="5">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="password_min_length" class="form-label">Minimum Password Length</label>
                                        <input type="number" class="form-control" id="password_min_length" value="8">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="password_expiry" class="form-label">Password Expiry (days)</label>
                                        <input type="number" class="form-control" id="password_expiry" value="90">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Password Requirements</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="require_uppercase" checked>
                                        <label class="form-check-label" for="require_uppercase">
                                            Require uppercase letters
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="require_lowercase" checked>
                                        <label class="form-check-label" for="require_lowercase">
                                            Require lowercase letters
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="require_numbers" checked>
                                        <label class="form-check-label" for="require_numbers">
                                            Require numbers
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="require_special" checked>
                                        <label class="form-check-label" for="require_special">
                                            Require special characters
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable_2fa">
                                        <label class="form-check-label" for="enable_2fa">
                                            <strong>Enable Two-Factor Authentication</strong>
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i data-feather="save" class="icon-xs me-2"></i>
                                    Save Security Settings
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="tab-pane fade" id="notifications">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i data-feather="bell" class="icon-sm me-2"></i>
                                Notification Settings
                            </h4>
                        </div>
                        <div class="card-body">
                            <form>
                                <div class="mb-4">
                                    <h5>Email Notifications</h5>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="email_new_cases" checked>
                                        <label class="form-check-label" for="email_new_cases">
                                            New case assignments
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="email_court_dates" checked>
                                        <label class="form-check-label" for="email_court_dates">
                                            Upcoming court dates
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="email_evidence_updates">
                                        <label class="form-check-label" for="email_evidence_updates">
                                            Evidence analysis results
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h5>System Notifications</h5>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="system_maintenance" checked>
                                        <label class="form-check-label" for="system_maintenance">
                                            System maintenance alerts
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="security_alerts" checked>
                                        <label class="form-check-label" for="security_alerts">
                                            Security alerts
                                        </label>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="smtp_host" class="form-label">SMTP Host</label>
                                        <input type="text" class="form-control" id="smtp_host" value="mail.police.mw">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="smtp_port" class="form-label">SMTP Port</label>
                                        <input type="number" class="form-control" id="smtp_port" value="587">
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i data-feather="save" class="icon-xs me-2"></i>
                                    Save Notification Settings
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Backup & Recovery -->
                <div class="tab-pane fade" id="backup">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i data-feather="database" class="icon-sm me-2"></i>
                                Backup & Recovery
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Automatic Backups</h5>
                                    <form>
                                        <div class="mb-3">
                                            <label for="backup_frequency" class="form-label">Backup Frequency</label>
                                            <select class="form-select" id="backup_frequency">
                                                <option value="daily" selected>Daily</option>
                                                <option value="weekly">Weekly</option>
                                                <option value="monthly">Monthly</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="backup_time" class="form-label">Backup Time</label>
                                            <input type="time" class="form-control" id="backup_time" value="02:00">
                                        </div>
                                        <div class="mb-3">
                                            <label for="retention_days" class="form-label">Retention Period (days)</label>
                                            <input type="number" class="form-control" id="retention_days" value="30">
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i data-feather="save" class="icon-xs me-2"></i>
                                            Save Backup Settings
                                        </button>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <h5>Manual Backup</h5>
                                    <p class="text-muted">Create an immediate backup of the system database and files.</p>
                                    <button class="btn btn-outline-primary mb-3">
                                        <i data-feather="download" class="icon-xs me-2"></i>
                                        Create Backup Now
                                    </button>

                                    <h5>Recent Backups</h5>
                                    <div class="list-group">
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>backup_2024_12_15.sql</strong>
                                                <br><small class="text-muted">15/12/2024 02:00 AM</small>
                                            </div>
                                            <span class="badge bg-success">245 MB</span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>backup_2024_12_14.sql</strong>
                                                <br><small class="text-muted">14/12/2024 02:00 AM</small>
                                            </div>
                                            <span class="badge bg-success">243 MB</span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>backup_2024_12_13.sql</strong>
                                                <br><small class="text-muted">13/12/2024 02:00 AM</small>
                                            </div>
                                            <span class="badge bg-success">241 MB</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Audit Logs -->
                <div class="tab-pane fade" id="audit">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i data-feather="file-text" class="icon-sm me-2"></i>
                                Audit Logs
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Timestamp</th>
                                            <th>User</th>
                                            <th>Action</th>
                                            <th>Resource</th>
                                            <th>IP Address</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>15/12/2024 14:30</td>
                                            <td>Inspector Banda</td>
                                            <td>CREATE</td>
                                            <td>Criminal Profile (CRM20240001)</td>
                                            <td>*************</td>
                                            <td><span class="badge bg-success">Success</span></td>
                                        </tr>
                                        <tr>
                                            <td>15/12/2024 14:25</td>
                                            <td>Sergeant Mwale</td>
                                            <td>UPDATE</td>
                                            <td>Case (CS2024001)</td>
                                            <td>192.168.1.101</td>
                                            <td><span class="badge bg-success">Success</span></td>
                                        </tr>
                                        <tr>
                                            <td>15/12/2024 14:20</td>
                                            <td>Unknown User</td>
                                            <td>LOGIN</td>
                                            <td>Authentication</td>
                                            <td>192.168.1.200</td>
                                            <td><span class="badge bg-danger">Failed</span></td>
                                        </tr>
                                        <tr>
                                            <td>15/12/2024 14:15</td>
                                            <td>Constable Phiri</td>
                                            <td>VIEW</td>
                                            <td>Evidence (EV2024001)</td>
                                            <td>192.168.1.102</td>
                                            <td><span class="badge bg-success">Success</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="tab-pane fade" id="system">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">
                                <i data-feather="monitor" class="icon-sm me-2"></i>
                                System Information
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Application Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Application Name:</strong></td>
                                            <td>{{ config('app.name') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Version:</strong></td>
                                            <td>1.0.0</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Laravel Version:</strong></td>
                                            <td>10.x</td>
                                        </tr>
                                        <tr>
                                            <td><strong>PHP Version:</strong></td>
                                            <td>8.2.0</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Database:</strong></td>
                                            <td>MySQL 8.0</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5>Server Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Server OS:</strong></td>
                                            <td>Ubuntu 22.04 LTS</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Web Server:</strong></td>
                                            <td>Apache 2.4</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Memory Usage:</strong></td>
                                            <td>2.1 GB / 8 GB</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Disk Usage:</strong></td>
                                            <td>45 GB / 100 GB</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Uptime:</strong></td>
                                            <td>15 days, 6 hours</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
@endpush
