<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('evidence', function (Blueprint $table) {
            $table->id();

            // Unique identifier
            $table->string('evidence_number', 30)->unique();
            $table->string('slug')->unique();

            // Basic Information
            $table->string('title', 200);
            $table->text('description');
            $table->enum('type', [
                'Physical Evidence', 'Digital Evidence', 'Documentary Evidence',
                'Biological Evidence', 'Ballistic Evidence', 'Photographic Evidence',
                'Audio Evidence', 'Video Evidence', 'Other'
            ]);
            $table->enum('category', [
                'Weapon', 'Drug', 'Document', 'Electronics', 'Clothing',
                'Vehicle', 'Money', 'Jewelry', 'Blood Sample', 'DNA Sample',
                'Fingerprint', 'Photograph', 'Video Recording', 'Audio Recording',
                'Other'
            ]);

            // Case Association
            $table->unsignedBigInteger('case_id')->nullable();
            $table->unsignedBigInteger('criminal_id')->nullable();

            // Collection Information
            $table->datetime('collected_at');
            $table->string('collected_by', 200);
            $table->text('collection_location');
            $table->text('collection_method')->nullable();
            $table->text('collection_notes')->nullable();

            // Physical Properties
            $table->string('quantity', 100)->nullable();
            $table->string('weight', 100)->nullable();
            $table->string('dimensions', 200)->nullable();
            $table->string('color', 100)->nullable();
            $table->string('material', 200)->nullable();
            $table->string('condition', 200)->nullable();

            // Storage Information
            $table->string('storage_location', 200);
            $table->string('storage_container', 200)->nullable();
            $table->enum('storage_conditions', ['Room Temperature', 'Refrigerated', 'Frozen', 'Dry Storage', 'Special']);
            $table->text('storage_notes')->nullable();

            // Chain of Custody
            $table->foreignId('current_custodian_id')->constrained('users')->onDelete('restrict');
            $table->datetime('custody_received_at');
            $table->text('custody_notes')->nullable();

            // Status and Disposition
            $table->enum('status', [
                'Collected', 'In Storage', 'In Analysis', 'Analyzed',
                'In Court', 'Released', 'Destroyed', 'Missing'
            ])->default('Collected');
            $table->enum('disposition', [
                'Pending', 'Return to Owner', 'Destroy', 'Retain', 'Transfer'
            ])->default('Pending');
            $table->date('disposition_date')->nullable();
            $table->text('disposition_notes')->nullable();

            // Legal Information
            $table->boolean('is_admissible')->default(true);
            $table->text('admissibility_notes')->nullable();
            $table->boolean('requires_special_handling')->default(false);
            $table->text('special_handling_instructions')->nullable();

            // Analysis Information
            $table->boolean('requires_analysis')->default(false);
            $table->string('analysis_type', 200)->nullable();
            $table->enum('analysis_status', [
                'Not Required', 'Pending', 'In Progress', 'Completed', 'Inconclusive'
            ])->default('Not Required');
            $table->datetime('analysis_requested_at')->nullable();
            $table->datetime('analysis_completed_at')->nullable();
            $table->text('analysis_results')->nullable();
            $table->foreignId('analyzed_by')->nullable()->constrained('users')->onDelete('set null');

            // Additional Information
            $table->decimal('estimated_value', 10, 2)->nullable();
            $table->string('serial_number', 100)->nullable();
            $table->string('brand_model', 200)->nullable();
            $table->text('additional_notes')->nullable();

            // Standard tracking fields
            $table->timestamps();
            $table->softDeletes();

            // User tracking fields
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');

            // Indexes for performance
            $table->index(['evidence_number']);
            $table->index(['case_id']);
            $table->index(['criminal_id']);
            $table->index(['type']);
            $table->index(['category']);
            $table->index(['status']);
            $table->index(['current_custodian_id']);
            $table->index(['collected_at']);
            $table->index(['created_by']);
            $table->index(['updated_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('evidence');
    }
};
