<?php $__env->startSection('title', 'User Management - ' . config('app.name')); ?>

<?php $__env->startPush('styles'); ?>
<style>
    #bulkActionsBar {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.025);
    }

    .user-checkbox:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .quick-search-form {
        transition: all 0.3s ease;
    }

    .quick-search-form:focus-within {
        transform: scale(1.02);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">User Management</h1>
            <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => ['items' => [
                ['title' => 'System'],
                ['title' => 'User Management']
            ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                ['title' => 'System'],
                ['title' => 'User Management']
            ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
        </div>
        <div class="d-flex gap-2">
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i data-feather="download" class="icon-xs me-2"></i>
                    Export
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#!" onclick="exportUsers('excel')">
                        <i data-feather="file-text" class="icon-xs me-2"></i>Export to Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#!" onclick="exportUsers('pdf')">
                        <i data-feather="file" class="icon-xs me-2"></i>Export to PDF
                    </a></li>
                    <li><a class="dropdown-item" href="#!" onclick="exportUsers('csv')">
                        <i data-feather="database" class="icon-xs me-2"></i>Export to CSV
                    </a></li>
                </ul>
            </div>
            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#importModal">
                <i data-feather="upload" class="icon-xs me-2"></i>
                Import Users
            </button>
            <a href="<?php echo e(route('users.create')); ?>" class="btn btn-primary">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Add New User
            </a>
        </div>
    </div>

    <!-- User Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary"><?php echo e($users->total()); ?></h4>
                    <p class="mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <?php
                        $activeCount = \App\Models\User::where('status', 'active')->count();
                    ?>
                    <h4 class="text-success"><?php echo e($activeCount); ?></h4>
                    <p class="mb-0">Active Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <?php
                        $inactiveCount = \App\Models\User::whereIn('status', ['inactive', 'suspended'])->count();
                    ?>
                    <h4 class="text-warning"><?php echo e($inactiveCount); ?></h4>
                    <p class="mb-0">Inactive Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <?php
                        $onlineCount = \App\Models\User::where('is_online', true)->count();
                    ?>
                    <h4 class="text-info"><?php echo e($onlineCount); ?></h4>
                    <p class="mb-0">Online Now</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i data-feather="filter" class="icon-sm me-2"></i>
                    Advanced Filters
                </h5>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i data-feather="chevron-down" class="icon-xs"></i>
                </button>
            </div>
        </div>
        <div class="collapse" id="filterCollapse">
            <div class="card-body">
                <form class="row g-3" method="GET" action="<?php echo e(route('users.index')); ?>" id="userFilterForm">
                    <div class="col-md-4">
                        <label class="form-label">Search</label>
                        <input type="text" class="form-control" name="search"
                               value="<?php echo e(request('search')); ?>"
                               placeholder="Name, email, badge number...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status">
                            <option value="">All Status</option>
                            <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                            <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                            <option value="suspended" <?php echo e(request('status') === 'suspended' ? 'selected' : ''); ?>>Suspended</option>
                            <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Department</label>
                        <select class="form-select" name="department">
                            <option value="">All Departments</option>
                            <?php
                                $departments = \App\Models\User::whereNotNull('department')->distinct()->pluck('department');
                            ?>
                            <?php $__currentLoopData = $departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $department): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($department); ?>" <?php echo e(request('department') === $department ? 'selected' : ''); ?>>
                                    <?php echo e($department); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i data-feather="search" class="icon-xs me-2"></i>
                                Search
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="<?php echo e(route('users.index')); ?>" class="btn btn-outline-secondary">
                                <i data-feather="x" class="icon-xs"></i>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Users List -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4 class="mb-0">System Users</h4>
                <div class="d-flex gap-2">
                    <!-- Quick Search -->
                    <form method="GET" action="<?php echo e(route('users.index')); ?>" class="d-flex quick-search-form">
                        <input type="text" class="form-control form-control-sm"
                               name="search" value="<?php echo e(request('search')); ?>"
                               placeholder="Quick search..." style="width: 200px;">
                        <button type="submit" class="btn btn-sm btn-outline-primary ms-2">
                            <i data-feather="search" class="icon-xs"></i>
                        </button>
                    </form>
                    <span class="text-muted small align-self-center">
                        Showing <?php echo e($users->count()); ?> of <?php echo e($users->total()); ?> users
                    </span>
                </div>
            </div>

            <!-- Bulk Actions Bar -->
            <div class="d-flex justify-content-between align-items-center" id="bulkActionsBar" style="display: none !important;">
                <div>
                    <span id="selectedCount">0</span> user(s) selected
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-success" onclick="performBulkAction('activate')">
                        <i data-feather="user-check" class="icon-xs me-1"></i>
                        Activate
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="performBulkAction('deactivate')">
                        <i data-feather="user-x" class="icon-xs me-1"></i>
                        Deactivate
                    </button>
                    <button type="button" class="btn btn-sm btn-info" onclick="performBulkAction('reset_password')">
                        <i data-feather="key" class="icon-xs me-1"></i>
                        Reset Passwords
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="performBulkAction('delete')">
                        <i data-feather="trash-2" class="icon-xs me-1"></i>
                        Delete
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th>
                                <a href="#!" class="text-decoration-none text-dark d-flex align-items-center">
                                    User
                                    <i data-feather="chevron-up" class="icon-xs ms-1"></i>
                                </a>
                            </th>
                            <th>Badge Number</th>
                            <th>Role</th>
                            <th>Department</th>
                            <th>Last Login</th>
                            <th>Status</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input user-checkbox" type="checkbox" value="<?php echo e($user->id); ?>">
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php
                                        $initials = collect(explode(' ', $user->name))->map(fn($name) => strtoupper(substr($name, 0, 1)))->take(2)->implode('');
                                        $avatarColor = $user->is_online ? 'bg-success' : 'bg-primary';
                                    ?>
                                    <div>
                                        <strong><?php echo e($user->name); ?></strong>
                                        <br><small class="text-muted"><?php echo e($user->email); ?></small>
                                        <?php if($user->is_online): ?>
                                            <span class="badge bg-success ms-2">Online</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php if($user->badge_number): ?>
                                    <code><?php echo e($user->badge_number); ?></code>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($user->roles->isNotEmpty()): ?>
                                    <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $roleColors = [
                                                'Super Administrator' => 'bg-danger',
                                                'Administrator' => 'bg-warning',
                                                'Inspector' => 'bg-primary',
                                                'Officer' => 'bg-success',
                                                'Forensic Analyst' => 'bg-info',
                                                'Court Liaison' => 'bg-secondary',
                                                'Viewer' => 'bg-light text-dark'
                                            ];
                                            $roleColor = $roleColors[$role->name] ?? 'bg-secondary';
                                        ?>
                                        <span class="badge <?php echo e($roleColor); ?>"><?php echo e($role->name); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <span class="badge bg-secondary">No Role</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($user->department ?? '-'); ?></td>
                            <td>
                                <?php if($user->last_login): ?>
                                    <?php echo e($user->last_login->format('d/m/Y H:i A')); ?>

                                    <br><small class="text-<?php echo e($user->is_online ? 'success' : 'muted'); ?>">
                                        <?php echo e($user->is_online ? 'Currently active' : $user->last_login->diffForHumans()); ?>

                                    </small>
                                <?php else: ?>
                                    <span class="text-muted">Never</span>
                                    <br><small class="text-muted">No login recorded</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                    $statusColors = [
                                        'active' => 'bg-success',
                                        'inactive' => 'bg-secondary',
                                        'suspended' => 'bg-danger',
                                        'pending' => 'bg-warning'
                                    ];
                                    $statusColor = $statusColors[$user->status] ?? 'bg-secondary';
                                ?>
                                <span class="badge <?php echo e($statusColor); ?>"><?php echo e(ucfirst($user->status)); ?></span>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="<?php echo e(route('users.show', $user->id)); ?>">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="<?php echo e(route('users.edit', $user->id)); ?>">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit User
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#!" onclick="resetUserPassword(<?php echo e($user->id); ?>)">
                                            <i data-feather="key" class="icon-xs me-2"></i>Reset Password
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!" onclick="toggleUserStatus(<?php echo e($user->id); ?>, '<?php echo e($user->status); ?>')">
                                            <?php if($user->status === 'active'): ?>
                                                <i data-feather="user-x" class="icon-xs me-2"></i>Deactivate
                                            <?php else: ?>
                                                <i data-feather="user-check" class="icon-xs me-2"></i>Activate
                                            <?php endif; ?>
                                        </a></li>
                                        <?php if($user->id !== auth()->id()): ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#!" onclick="deleteUser(<?php echo e($user->id); ?>)">
                                            <i data-feather="trash-2" class="icon-xs me-2"></i>Delete User
                                        </a></li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i data-feather="users" class="icon-lg mb-2"></i>
                                    <p>No users found.</p>
                                    <a href="<?php echo e(route('users.create')); ?>" class="btn btn-primary btn-sm">
                                        <i data-feather="plus" class="icon-xs me-2"></i>
                                        Add First User
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Pagination -->
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    Showing <strong><?php echo e($users->firstItem()); ?></strong> to <strong><?php echo e($users->lastItem()); ?></strong> of <strong><?php echo e($users->total()); ?></strong> users
                </div>
                <nav aria-label="User pagination">
                    <?php echo e($users->links('pagination::bootstrap-4')); ?>

                </nav>
            </div>
        </div>

    </div>

    <!-- Import Modal -->
    <div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importModalLabel">
                        <i data-feather="upload" class="icon-sm me-2"></i>
                        Import Users
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i data-feather="info" class="icon-sm me-2"></i>
                        <strong>Import Format:</strong> Upload an Excel (.xlsx) or CSV file with user data.
                    </div>
                    
                    <form enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="import_file" class="form-label">Select File</label>
                            <input type="file" class="form-control" id="import_file" accept=".xlsx,.xls,.csv">
                            <small class="text-muted">Supported formats: Excel (.xlsx, .xls), CSV (.csv)</small>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="update_existing">
                                <label class="form-check-label" for="update_existing">
                                    Update existing users if email/badge number matches
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_welcome_emails">
                                <label class="form-check-label" for="send_welcome_emails">
                                    Send welcome emails to new users
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <a href="#!" class="btn btn-outline-secondary btn-sm">
                                <i data-feather="download" class="icon-xs me-2"></i>
                                Download Template
                            </a>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="importUsers()">
                        <i data-feather="upload" class="icon-xs me-2"></i>
                        Import Users
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();

    // Select All functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionsBar();
        });
    }

    // Individual checkbox change
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('user-checkbox')) {
            updateBulkActionsBar();
        }
    });
});

function updateBulkActionsBar() {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    const bulkActionsBar = document.getElementById('bulkActionsBar');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedCheckboxes.length > 0) {
        bulkActionsBar.style.display = 'flex';
        selectedCount.textContent = selectedCheckboxes.length;
    } else {
        bulkActionsBar.style.display = 'none';
    }
}

// Individual user actions
function resetUserPassword(userId) {
    if (confirm('Are you sure you want to reset this user\'s password?')) {
        // Show loading state
        const button = event.target.closest('a');
        const originalText = button.innerHTML;
        button.innerHTML = '<i data-feather="loader" class="icon-xs me-2"></i>Resetting...';
        button.style.pointerEvents = 'none';

        fetch(`/users/${userId}/reset-password`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error resetting password: ' + error.message);
        })
        .finally(() => {
            // Restore button state
            button.innerHTML = originalText;
            button.style.pointerEvents = 'auto';
            feather.replace();
        });
    }
}

function toggleUserStatus(userId, currentStatus) {
    const action = currentStatus === 'active' ? 'deactivate' : 'activate';
    if (confirm(`Are you sure you want to ${action} this user?`)) {
        fetch(`/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload(); // Reload to update the UI
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error updating user status: ' + error.message);
        });
    }
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        fetch(`/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload(); // Reload to update the UI
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error deleting user: ' + error.message);
        });
    }
}

// Bulk actions
function performBulkAction(action) {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        alert('Please select users to perform bulk action.');
        return;
    }

    const userIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    const actionText = action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

    if (confirm(`Are you sure you want to ${actionText} ${userIds.length} selected user(s)?`)) {
        fetch('/users/bulk-action', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                user_ids: userIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload(); // Reload to update the UI
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error performing bulk action: ' + error.message);
        });
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/users/index.blade.php ENDPATH**/ ?>