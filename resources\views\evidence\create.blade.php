@extends('layouts.app')

@section('title', 'Add Evidence  - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Add Evidence</h1>
            <x-breadcrumb :items="[
                ['title' => 'Evidence Management', 'url' => route('evidence.index')],
                ['title' => 'Add Evidence']
            ]" />
        </div>
        <div>
            <a href="{{ route('evidence.index') }}" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Evidence List
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('evidence.store') }}" enctype="multipart/form-data">
        @csrf
        
        <!-- Evidence Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="archive" class="icon-sm me-2"></i>
                    Evidence Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="evidence_id" class="form-label">Evidence ID <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="evidence_id" name="evidence_id" required
                               placeholder="e.g., EV2024001">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="case_number" class="form-label">Related Case Number <span class="text-danger">*</span></label>
                        <select class="form-select" id="case_number" name="case_number" required>
                            <option value="">Select Case</option>
                            <option value="CS2024001">CS2024001 - Armed Robbery at Shoprite</option>
                            <option value="CS2024002">CS2024002 - Drug Trafficking Investigation</option>
                            <option value="CS2024003">CS2024003 - Domestic Violence Case</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="category" class="form-label">Evidence Category <span class="text-danger">*</span></label>
                        <select class="form-select" id="category" name="category" required>
                            <option value="">Select Category</option>
                            <option value="Physical">Physical Evidence</option>
                            <option value="Digital">Digital Evidence</option>
                            <option value="Documentary">Documentary Evidence</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="subcategory" class="form-label">Subcategory</label>
                        <select class="form-select" id="subcategory" name="subcategory">
                            <option value="">Select Subcategory</option>
                            <!-- Physical Evidence -->
                            <option value="DNA" data-category="Physical">DNA</option>
                            <option value="Fingerprints" data-category="Physical">Fingerprints</option>
                            <option value="Weapons" data-category="Physical">Weapons</option>
                            <option value="Clothing" data-category="Physical">Clothing</option>
                            <option value="Blood" data-category="Physical">Blood</option>
                            <!-- Digital Evidence -->
                            <option value="Video" data-category="Digital">Video</option>
                            <option value="Audio" data-category="Digital">Audio</option>
                            <option value="Photos" data-category="Digital">Photos</option>
                            <option value="Computer Files" data-category="Digital">Computer Files</option>
                            <!-- Documentary Evidence -->
                            <option value="Witness Statements" data-category="Documentary">Witness Statements</option>
                            <option value="Reports" data-category="Documentary">Reports</option>
                            <option value="Contracts" data-category="Documentary">Contracts</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="Low">Low</option>
                            <option value="Medium" selected>Medium</option>
                            <option value="High">High</option>
                            <option value="Critical">Critical</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">Evidence Description <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="description" name="description" rows="3" required
                                  placeholder="Detailed description of the evidence item..."></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Collection Details -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="user-check" class="icon-sm me-2"></i>
                    Collection Details
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="collection_date" class="form-label">Collection Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="collection_date" name="collection_date" 
                               value="{{ date('Y-m-d') }}" required>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="collection_time" class="form-label">Collection Time</label>
                        <input type="time" class="form-control" id="collection_time" name="collection_time">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="collector_name" class="form-label">Collector Name <span class="text-danger">*</span></label>
                        <select class="form-select" id="collector_name" name="collector_name" required>
                            <option value="">Select Collector</option>
                            <option value="1">Inspector Banda</option>
                            <option value="2">Sergeant Mwale</option>
                            <option value="3">Constable Phiri</option>
                            <option value="4">Forensic Officer Kumwenda</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="collection_location" class="form-label">Collection Location</label>
                        <input type="text" class="form-control" id="collection_location" name="collection_location"
                               placeholder="e.g., Crime scene, Suspect's house, etc.">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="collection_method" class="form-label">Collection Method</label>
                        <input type="text" class="form-control" id="collection_method" name="collection_method"
                               placeholder="e.g., Swab, Photography, Seizure, etc.">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="collection_notes" class="form-label">Collection Notes</label>
                        <textarea class="form-control" id="collection_notes" name="collection_notes" rows="3"
                                  placeholder="Additional notes about the collection process..."></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Storage Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="archive" class="icon-sm me-2"></i>
                    Storage Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="storage_location" class="form-label">Storage Location <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="storage_location" name="storage_location" required
                               placeholder="e.g., Shelf A1, Room 3, Digital Storage Server">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="storage_facility" class="form-label">Storage Facility</label>
                        <select class="form-select" id="storage_facility" name="storage_facility">
                            <option value="">Select Facility</option>
                            <option value="Lilongwe Evidence Room">Lilongwe Evidence Room</option>
                            <option value="Blantyre Property Room">Blantyre Property Room</option>
                            <option value="Forensic Lab">Forensic Lab</option>
                            <option value="Digital Storage Server">Digital Storage Server</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="storage_conditions" class="form-label">Storage Conditions</label>
                        <select class="form-select" id="storage_conditions" name="storage_conditions">
                            <option value="Room Temperature">Room Temperature</option>
                            <option value="Refrigerated">Refrigerated</option>
                            <option value="Frozen">Frozen</option>
                            <option value="Climate Controlled">Climate Controlled</option>
                            <option value="Secure Vault">Secure Vault</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="container_type" class="form-label">Container Type</label>
                        <input type="text" class="form-control" id="container_type" name="container_type"
                               placeholder="e.g., Evidence bag, Box, Tube, etc.">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="seal_number" class="form-label">Seal Number</label>
                        <input type="text" class="form-control" id="seal_number" name="seal_number"
                               placeholder="e.g., SEAL001234">
                    </div>
                </div>
            </div>
        </div>

        <!-- File Uploads -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="upload" class="icon-sm me-2"></i>
                    File Attachments
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="evidence_photos" class="form-label">Evidence Photos</label>
                        <input type="file" class="form-control" id="evidence_photos" name="evidence_photos[]" 
                               multiple accept="image/*">
                        <small class="text-muted">Upload photos of the evidence item</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="evidence_documents" class="form-label">Related Documents</label>
                        <input type="file" class="form-control" id="evidence_documents" name="evidence_documents[]" 
                               multiple accept=".pdf,.doc,.docx">
                        <small class="text-muted">Upload related documents (PDF, Word)</small>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="digital_evidence" class="form-label">Digital Evidence Files</label>
                        <input type="file" class="form-control" id="digital_evidence" name="digital_evidence[]" multiple>
                        <small class="text-muted">Upload digital evidence files (videos, audio, etc.)</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chain of Custody -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="link" class="icon-sm me-2"></i>
                    Initial Chain of Custody
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="custody_officer" class="form-label">Custody Officer</label>
                        <select class="form-select" id="custody_officer" name="custody_officer">
                            <option value="">Select Officer</option>
                            <option value="1">Evidence Custodian Banda</option>
                            <option value="2">Property Clerk Mwale</option>
                            <option value="3">Forensic Specialist Phiri</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="custody_date" class="form-label">Custody Transfer Date</label>
                        <input type="date" class="form-control" id="custody_date" name="custody_date" 
                               value="{{ date('Y-m-d') }}">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="custody_notes" class="form-label">Custody Notes</label>
                        <textarea class="form-control" id="custody_notes" name="custody_notes" rows="2"
                                  placeholder="Notes about the custody transfer..."></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-end gap-2 mb-4">
            <a href="{{ route('evidence.index') }}" class="btn btn-outline-secondary">
                <i data-feather="x" class="icon-xs me-2"></i>
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i data-feather="save" class="icon-xs me-2"></i>
                Add Evidence
            </button>
        </div>
    </form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Handle category change to filter subcategories
    document.getElementById('category').addEventListener('change', function() {
        const category = this.value;
        const subcategorySelect = document.getElementById('subcategory');
        const options = subcategorySelect.querySelectorAll('option[data-category]');
        
        // Hide all subcategory options
        options.forEach(option => {
            option.style.display = 'none';
        });
        
        // Show relevant subcategory options
        if (category) {
            const relevantOptions = subcategorySelect.querySelectorAll(`option[data-category="${category}"]`);
            relevantOptions.forEach(option => {
                option.style.display = 'block';
            });
        }
        
        // Reset subcategory selection
        subcategorySelect.value = '';
    });
});
</script>
@endpush
