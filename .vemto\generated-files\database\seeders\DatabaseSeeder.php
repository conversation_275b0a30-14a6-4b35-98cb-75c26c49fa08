<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Models\User;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::factory()
            ->count(1)
            ->create([
                'email' => '<EMAIL>',
                'password' => \Hash::make('admin'),
            ]);

        $this->call(MembershipSeeder::class);
        $this->call(TeamSeeder::class);
        $this->call(TeamInvitationSeeder::class);
    }
}
