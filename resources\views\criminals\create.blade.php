@extends('layouts.app')

@section('title', 'Add New Criminal Profile  - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Add New Criminal Profile</h1>
            <x-breadcrumb :items="[
                ['title' => 'Criminal Profiles', 'url' => route('criminals.index')],
                ['title' => 'Add New Criminal']
            ]" />
        </div>
        <div>
            <a href="{{ route('criminals.index') }}" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to List
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('criminals.store') }}" enctype="multipart/form-data">
        @csrf
        
        <!-- Personal Details Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="user" class="icon-sm me-2"></i>
                    Personal Details
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('first_name') is-invalid @enderror" 
                               id="first_name" name="first_name" value="{{ old('first_name') }}" required>
                        @error('first_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="middle_name" class="form-label">Middle Name</label>
                        <input type="text" class="form-control @error('middle_name') is-invalid @enderror" 
                               id="middle_name" name="middle_name" value="{{ old('middle_name') }}">
                        @error('middle_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                               id="last_name" name="last_name" value="{{ old('last_name') }}" required>
                        @error('last_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="alias" class="form-label">Alias/Nickname</label>
                        <input type="text" class="form-control @error('alias') is-invalid @enderror" 
                               id="alias" name="alias" value="{{ old('alias') }}">
                        @error('alias')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
                        <select class="form-select @error('gender') is-invalid @enderror" id="gender" name="gender" required>
                            <option value="">Select Gender</option>
                            <option value="Male" {{ old('gender') == 'Male' ? 'selected' : '' }}>Male</option>
                            <option value="Female" {{ old('gender') == 'Female' ? 'selected' : '' }}>Female</option>
                            <option value="Other" {{ old('gender') == 'Other' ? 'selected' : '' }}>Other</option>
                        </select>
                        @error('gender')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="date_of_birth" class="form-label">Date of Birth</label>
                        <input type="date" class="form-control @error('date_of_birth') is-invalid @enderror" 
                               id="date_of_birth" name="date_of_birth" value="{{ old('date_of_birth') }}">
                        @error('date_of_birth')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="place_of_birth" class="form-label">Place of Birth</label>
                        <input type="text" class="form-control @error('place_of_birth') is-invalid @enderror" 
                               id="place_of_birth" name="place_of_birth" value="{{ old('place_of_birth') }}">
                        @error('place_of_birth')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="nationality" class="form-label">Nationality</label>
                        <input type="text" class="form-control @error('nationality') is-invalid @enderror" 
                               id="nationality" name="nationality" value="{{ old('nationality', 'Malawian') }}">
                        @error('nationality')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="national_id" class="form-label">National ID</label>
                        <input type="text" class="form-control @error('national_id') is-invalid @enderror" 
                               id="national_id" name="national_id" value="{{ old('national_id') }}">
                        @error('national_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Physical Description Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="eye" class="icon-sm me-2"></i>
                    Physical Description
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="height" class="form-label">Height (meters)</label>
                        <input type="number" step="0.01" min="0" max="3" 
                               class="form-control @error('height') is-invalid @enderror" 
                               id="height" name="height" value="{{ old('height') }}">
                        @error('height')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="weight" class="form-label">Weight (kg)</label>
                        <input type="number" step="0.1" min="0" max="500" 
                               class="form-control @error('weight') is-invalid @enderror" 
                               id="weight" name="weight" value="{{ old('weight') }}">
                        @error('weight')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="eye_color" class="form-label">Eye Color</label>
                        <select class="form-select @error('eye_color') is-invalid @enderror" id="eye_color" name="eye_color">
                            <option value="">Select Eye Color</option>
                            <option value="Brown" {{ old('eye_color') == 'Brown' ? 'selected' : '' }}>Brown</option>
                            <option value="Black" {{ old('eye_color') == 'Black' ? 'selected' : '' }}>Black</option>
                            <option value="Blue" {{ old('eye_color') == 'Blue' ? 'selected' : '' }}>Blue</option>
                            <option value="Green" {{ old('eye_color') == 'Green' ? 'selected' : '' }}>Green</option>
                            <option value="Hazel" {{ old('eye_color') == 'Hazel' ? 'selected' : '' }}>Hazel</option>
                            <option value="Gray" {{ old('eye_color') == 'Gray' ? 'selected' : '' }}>Gray</option>
                        </select>
                        @error('eye_color')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="hair_color" class="form-label">Hair Color</label>
                        <select class="form-select @error('hair_color') is-invalid @enderror" id="hair_color" name="hair_color">
                            <option value="">Select Hair Color</option>
                            <option value="Black" {{ old('hair_color') == 'Black' ? 'selected' : '' }}>Black</option>
                            <option value="Brown" {{ old('hair_color') == 'Brown' ? 'selected' : '' }}>Brown</option>
                            <option value="Blonde" {{ old('hair_color') == 'Blonde' ? 'selected' : '' }}>Blonde</option>
                            <option value="Red" {{ old('hair_color') == 'Red' ? 'selected' : '' }}>Red</option>
                            <option value="Gray" {{ old('hair_color') == 'Gray' ? 'selected' : '' }}>Gray</option>
                            <option value="White" {{ old('hair_color') == 'White' ? 'selected' : '' }}>White</option>
                            <option value="Bald" {{ old('hair_color') == 'Bald' ? 'selected' : '' }}>Bald</option>
                        </select>
                        @error('hair_color')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="complexion" class="form-label">Complexion</label>
                        <select class="form-select @error('complexion') is-invalid @enderror" id="complexion" name="complexion">
                            <option value="">Select Complexion</option>
                            <option value="Fair" {{ old('complexion') == 'Fair' ? 'selected' : '' }}>Fair</option>
                            <option value="Medium" {{ old('complexion') == 'Medium' ? 'selected' : '' }}>Medium</option>
                            <option value="Dark" {{ old('complexion') == 'Dark' ? 'selected' : '' }}>Dark</option>
                            <option value="Light" {{ old('complexion') == 'Light' ? 'selected' : '' }}>Light</option>
                        </select>
                        @error('complexion')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="distinguishing_marks" class="form-label">Distinguishing Marks</label>
                        <textarea class="form-control @error('distinguishing_marks') is-invalid @enderror" 
                                  id="distinguishing_marks" name="distinguishing_marks" rows="3">{{ old('distinguishing_marks') }}</textarea>
                        @error('distinguishing_marks')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="scars_tattoos" class="form-label">Scars & Tattoos</label>
                        <textarea class="form-control @error('scars_tattoos') is-invalid @enderror" 
                                  id="scars_tattoos" name="scars_tattoos" rows="3">{{ old('scars_tattoos') }}</textarea>
                        @error('scars_tattoos')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="phone" class="icon-sm me-2"></i>
                    Contact Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="phone_number" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control @error('phone_number') is-invalid @enderror" 
                               id="phone_number" name="phone_number" value="{{ old('phone_number') }}" 
                               placeholder="+265 999 123 456">
                        @error('phone_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control @error('email') is-invalid @enderror" 
                               id="email" name="email" value="{{ old('email') }}">
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="current_address" class="form-label">Current Address</label>
                        <textarea class="form-control @error('current_address') is-invalid @enderror" 
                                  id="current_address" name="current_address" rows="3" 
                                  placeholder="P.O. Box 123, Lilongwe">{{ old('current_address') }}</textarea>
                        @error('current_address')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="permanent_address" class="form-label">Permanent Address</label>
                        <textarea class="form-control @error('permanent_address') is-invalid @enderror" 
                                  id="permanent_address" name="permanent_address" rows="3" 
                                  placeholder="Village, T/A, District">{{ old('permanent_address') }}</textarea>
                        @error('permanent_address')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="district" class="form-label">District</label>
                        <select class="form-select @error('district') is-invalid @enderror" id="district" name="district">
                            <option value="">Select District</option>
                            <option value="Lilongwe" {{ old('district') == 'Lilongwe' ? 'selected' : '' }}>Lilongwe</option>
                            <option value="Blantyre" {{ old('district') == 'Blantyre' ? 'selected' : '' }}>Blantyre</option>
                            <option value="Mzuzu" {{ old('district') == 'Mzuzu' ? 'selected' : '' }}>Mzuzu</option>
                            <option value="Zomba" {{ old('district') == 'Zomba' ? 'selected' : '' }}>Zomba</option>
                            <option value="Kasungu" {{ old('district') == 'Kasungu' ? 'selected' : '' }}>Kasungu</option>
                            <option value="Mangochi" {{ old('district') == 'Mangochi' ? 'selected' : '' }}>Mangochi</option>
                            <option value="Salima" {{ old('district') == 'Salima' ? 'selected' : '' }}>Salima</option>
                            <option value="Dedza" {{ old('district') == 'Dedza' ? 'selected' : '' }}>Dedza</option>
                            <option value="Ntchisi" {{ old('district') == 'Ntchisi' ? 'selected' : '' }}>Ntchisi</option>
                            <option value="Nkhotakota" {{ old('district') == 'Nkhotakota' ? 'selected' : '' }}>Nkhotakota</option>
                            <option value="Mchinji" {{ old('district') == 'Mchinji' ? 'selected' : '' }}>Mchinji</option>
                        </select>
                        @error('district')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="traditional_authority" class="form-label">Traditional Authority</label>
                        <input type="text" class="form-control @error('traditional_authority') is-invalid @enderror" 
                               id="traditional_authority" name="traditional_authority" value="{{ old('traditional_authority') }}" 
                               placeholder="e.g., Malengachanzi">
                        @error('traditional_authority')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="village" class="form-label">Village</label>
                        <input type="text" class="form-control @error('village') is-invalid @enderror" 
                               id="village" name="village" value="{{ old('village') }}" 
                               placeholder="e.g., Makuta">
                        @error('village')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Emergency Contact Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="users" class="icon-sm me-2"></i>
                    Emergency Contact
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="emergency_contact_name" class="form-label">Contact Name</label>
                        <input type="text" class="form-control @error('emergency_contact_name') is-invalid @enderror"
                               id="emergency_contact_name" name="emergency_contact_name" value="{{ old('emergency_contact_name') }}">
                        @error('emergency_contact_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="emergency_contact_phone" class="form-label">Contact Phone</label>
                        <input type="tel" class="form-control @error('emergency_contact_phone') is-invalid @enderror"
                               id="emergency_contact_phone" name="emergency_contact_phone" value="{{ old('emergency_contact_phone') }}"
                               placeholder="+265 999 123 456">
                        @error('emergency_contact_phone')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="emergency_contact_relationship" class="form-label">Relationship</label>
                        <select class="form-select @error('emergency_contact_relationship') is-invalid @enderror"
                                id="emergency_contact_relationship" name="emergency_contact_relationship">
                            <option value="">Select Relationship</option>
                            <option value="Parent" {{ old('emergency_contact_relationship') == 'Parent' ? 'selected' : '' }}>Parent</option>
                            <option value="Sibling" {{ old('emergency_contact_relationship') == 'Sibling' ? 'selected' : '' }}>Sibling</option>
                            <option value="Spouse" {{ old('emergency_contact_relationship') == 'Spouse' ? 'selected' : '' }}>Spouse</option>
                            <option value="Child" {{ old('emergency_contact_relationship') == 'Child' ? 'selected' : '' }}>Child</option>
                            <option value="Relative" {{ old('emergency_contact_relationship') == 'Relative' ? 'selected' : '' }}>Relative</option>
                            <option value="Friend" {{ old('emergency_contact_relationship') == 'Friend' ? 'selected' : '' }}>Friend</option>
                            <option value="Other" {{ old('emergency_contact_relationship') == 'Other' ? 'selected' : '' }}>Other</option>
                        </select>
                        @error('emergency_contact_relationship')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="info" class="icon-sm me-2"></i>
                    Additional Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="occupation" class="form-label">Occupation</label>
                        <input type="text" class="form-control @error('occupation') is-invalid @enderror"
                               id="occupation" name="occupation" value="{{ old('occupation') }}"
                               placeholder="e.g., Driver, Teacher, Farmer">
                        @error('occupation')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="education_level" class="form-label">Education Level</label>
                        <select class="form-select @error('education_level') is-invalid @enderror" id="education_level" name="education_level">
                            <option value="">Select Education Level</option>
                            <option value="No Formal Education" {{ old('education_level') == 'No Formal Education' ? 'selected' : '' }}>No Formal Education</option>
                            <option value="Primary" {{ old('education_level') == 'Primary' ? 'selected' : '' }}>Primary</option>
                            <option value="Secondary" {{ old('education_level') == 'Secondary' ? 'selected' : '' }}>Secondary</option>
                            <option value="Tertiary" {{ old('education_level') == 'Tertiary' ? 'selected' : '' }}>Tertiary</option>
                            <option value="University" {{ old('education_level') == 'University' ? 'selected' : '' }}>University</option>
                            <option value="Postgraduate" {{ old('education_level') == 'Postgraduate' ? 'selected' : '' }}>Postgraduate</option>
                        </select>
                        @error('education_level')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="marital_status" class="form-label">Marital Status</label>
                        <select class="form-select @error('marital_status') is-invalid @enderror" id="marital_status" name="marital_status">
                            <option value="">Select Marital Status</option>
                            <option value="Single" {{ old('marital_status') == 'Single' ? 'selected' : '' }}>Single</option>
                            <option value="Married" {{ old('marital_status') == 'Married' ? 'selected' : '' }}>Married</option>
                            <option value="Divorced" {{ old('marital_status') == 'Divorced' ? 'selected' : '' }}>Divorced</option>
                            <option value="Widowed" {{ old('marital_status') == 'Widowed' ? 'selected' : '' }}>Widowed</option>
                            <option value="Separated" {{ old('marital_status') == 'Separated' ? 'selected' : '' }}>Separated</option>
                        </select>
                        @error('marital_status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="risk_level" class="form-label">Risk Level <span class="text-danger">*</span></label>
                        <select class="form-select @error('risk_level') is-invalid @enderror" id="risk_level" name="risk_level" required>
                            <option value="">Select Risk Level</option>
                            <option value="Low" {{ old('risk_level') == 'Low' ? 'selected' : '' }}>Low</option>
                            <option value="Medium" {{ old('risk_level') == 'Medium' ? 'selected' : '' }}>Medium</option>
                            <option value="High" {{ old('risk_level') == 'High' ? 'selected' : '' }}>High</option>
                            <option value="Critical" {{ old('risk_level') == 'Critical' ? 'selected' : '' }}>Critical</option>
                        </select>
                        @error('risk_level')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-check mt-4">
                            <input class="form-check-input @error('is_wanted') is-invalid @enderror"
                                   type="checkbox" id="is_wanted" name="is_wanted" value="1"
                                   {{ old('is_wanted') ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_wanted">
                                <strong>Wanted Criminal</strong>
                            </label>
                            @error('is_wanted')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-check mt-4">
                            <input class="form-check-input @error('is_repeat_offender') is-invalid @enderror"
                                   type="checkbox" id="is_repeat_offender" name="is_repeat_offender" value="1"
                                   {{ old('is_repeat_offender') ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_repeat_offender">
                                <strong>Repeat Offender</strong>
                            </label>
                            @error('is_repeat_offender')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="known_associates" class="form-label">Known Associates</label>
                        <textarea class="form-control @error('known_associates') is-invalid @enderror"
                                  id="known_associates" name="known_associates" rows="3"
                                  placeholder="List known associates, gang affiliations, etc.">{{ old('known_associates') }}</textarea>
                        @error('known_associates')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="notes" class="form-label">Special Instructions & Notes</label>
                        <textarea class="form-control @error('notes') is-invalid @enderror"
                                  id="notes" name="notes" rows="3"
                                  placeholder="Medical conditions, allergies, special instructions, etc.">{{ old('notes') }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-end gap-2 mb-4">
            <a href="{{ route('criminals.index') }}" class="btn btn-outline-secondary">
                <i data-feather="x" class="icon-xs me-2"></i>
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i data-feather="save" class="icon-xs me-2"></i>
                Save Criminal Profile
            </button>
        </div>
    </form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Auto-format phone number
    const phoneInput = document.getElementById('phone_number');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.startsWith('265')) {
                value = '+' + value;
            } else if (value.startsWith('0')) {
                value = '+265' + value.substring(1);
            } else if (!value.startsWith('+265') && value.length > 0) {
                value = '+265' + value;
            }
            e.target.value = value;
        });
    }
});
</script>
@endpush
