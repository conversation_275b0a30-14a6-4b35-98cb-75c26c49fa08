<!-- Sidebar -->
<div class="app-menu">
    <div class="navbar-vertical navbar nav-dashboard">
        <div class="h-100" data-simplebar>
            <!-- Brand logo -->
            <a class="navbar-brand d-flex align-items-center" href="{{ route('dashboard') }}" style="padding: 1rem;" title="{{ config('app.name') }}">
                <img src="{{ asset('images/logo.png') }}" alt="Logo" class="me-2" style="width: 35px; height: 35px; object-fit: contain;">
                <span class="fw-bold text-dark" style="font-size: 1.2rem; letter-spacing: 1px;">{{ app_initials() }}</span>
            </a>
            
            <!-- Navbar nav -->
            <ul class="navbar-nav flex-column" id="sideNavbar">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link has-arrow {{ request()->routeIs('dashboard*') ? 'active' : '' }}" 
                       href="#!" 
                       data-bs-toggle="collapse" 
                       data-bs-target="#navDashboard" 
                       aria-expanded="{{ request()->routeIs('dashboard*') ? 'true' : 'false' }}" 
                       aria-controls="navDashboard">
                        <i data-feather="home" class="nav-icon me-2 icon-xxs"></i>
                        Dashboard
                    </a>
                    <div id="navDashboard" class="collapse {{ request()->routeIs('dashboard*') ? 'show' : '' }}" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                                    Analytics Overview
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('dashboard.statistics') ? 'active' : '' }}" href="{{ route('dashboard.statistics') }}">
                                    Crime Statistics
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('dashboard.quick-actions') ? 'active' : '' }}" href="{{ route('dashboard.quick-actions') }}">
                                    Quick Actions
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Criminal Profiles -->
                <li class="nav-item">
                    <a class="nav-link has-arrow {{ request()->routeIs('criminals*') ? 'active' : '' }}" 
                       href="#!" 
                       data-bs-toggle="collapse" 
                       data-bs-target="#navCriminals" 
                       aria-expanded="{{ request()->routeIs('criminals*') ? 'true' : 'false' }}" 
                       aria-controls="navCriminals">
                        <i data-feather="users" class="nav-icon me-2 icon-xxs"></i>
                        Criminal Profiles
                    </a>
                    <div id="navCriminals" class="collapse {{ request()->routeIs('criminals*') ? 'show' : '' }}" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('criminals.index') ? 'active' : '' }}" href="{{ route('criminals.index') }}">
                                    Search Criminals
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('criminals.create') ? 'active' : '' }}" href="{{ route('criminals.create') }}">
                                    Add New Criminal
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('criminals.list') ? 'active' : '' }}" href="{{ route('criminals.list') }}">
                                    Criminal List
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Case Management -->
                <li class="nav-item">
                    <a class="nav-link has-arrow {{ request()->routeIs('cases*') ? 'active' : '' }}" 
                       href="#!" 
                       data-bs-toggle="collapse" 
                       data-bs-target="#navCases" 
                       aria-expanded="{{ request()->routeIs('cases*') ? 'true' : 'false' }}" 
                       aria-controls="navCases">
                        <i data-feather="folder" class="nav-icon me-2 icon-xxs"></i>
                        Case Management
                    </a>
                    <div id="navCases" class="collapse {{ request()->routeIs('cases*') ? 'show' : '' }}" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('cases.active') ? 'active' : '' }}" href="{{ route('cases.active') }}">
                                    Active Cases
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('cases.create') ? 'active' : '' }}" href="{{ route('cases.create') }}">
                                    New Case
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('cases.search') ? 'active' : '' }}" href="{{ route('cases.search') }}">
                                    Case Search
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('cases.reports') ? 'active' : '' }}" href="{{ route('cases.reports') }}">
                                    Case Reports
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Evidence Management -->
                <li class="nav-item">
                    <a class="nav-link has-arrow {{ request()->routeIs('evidence*') ? 'active' : '' }}" 
                       href="#!" 
                       data-bs-toggle="collapse" 
                       data-bs-target="#navEvidence" 
                       aria-expanded="{{ request()->routeIs('evidence*') ? 'true' : 'false' }}" 
                       aria-controls="navEvidence">
                        <i data-feather="archive" class="nav-icon me-2 icon-xxs"></i>
                        Evidence Management
                    </a>
                    <div id="navEvidence" class="collapse {{ request()->routeIs('evidence*') ? 'show' : '' }}" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('evidence.index') ? 'active' : '' }}" href="{{ route('evidence.index') }}">
                                    Evidence List
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('evidence.create') ? 'active' : '' }}" href="{{ route('evidence.create') }}">
                                    Add Evidence
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('evidence.custody') ? 'active' : '' }}" href="{{ route('evidence.custody') }}">
                                    Chain of Custody
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('evidence.forensic') ? 'active' : '' }}" href="{{ route('evidence.forensic') }}">
                                    Forensic Analysis
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Court Cases -->
                <li class="nav-item">
                    <a class="nav-link has-arrow {{ request()->routeIs('court*') ? 'active' : '' }}" 
                       href="#!" 
                       data-bs-toggle="collapse" 
                       data-bs-target="#navCourt" 
                       aria-expanded="{{ request()->routeIs('court*') ? 'true' : 'false' }}" 
                       aria-controls="navCourt">
                        <i data-feather="briefcase" class="nav-icon me-2 icon-xxs"></i>
                        Court Cases
                    </a>
                    <div id="navCourt" class="collapse {{ request()->routeIs('court*') ? 'show' : '' }}" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('court.hearings') ? 'active' : '' }}" href="{{ route('court.hearings') }}">
                                    Scheduled Hearings
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('court.status') ? 'active' : '' }}" href="{{ route('court.status') }}">
                                    Case Status
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('court.calendar') ? 'active' : '' }}" href="{{ route('court.calendar') }}">
                                    Court Calendar
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('court.verdicts') ? 'active' : '' }}" href="{{ route('court.verdicts') }}">
                                    Verdicts
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Documents -->
                <li class="nav-item">
                    <a class="nav-link has-arrow {{ request()->routeIs('documents*') ? 'active' : '' }}" 
                       href="#!" 
                       data-bs-toggle="collapse" 
                       data-bs-target="#navDocuments" 
                       aria-expanded="{{ request()->routeIs('documents*') ? 'true' : 'false' }}" 
                       aria-controls="navDocuments">
                        <i data-feather="file-text" class="nav-icon me-2 icon-xxs"></i>
                        Documents
                    </a>
                    <div id="navDocuments" class="collapse {{ request()->routeIs('documents*') ? 'show' : '' }}" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('documents.library') ? 'active' : '' }}" href="{{ route('documents.library') }}">
                                    Document Library
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('documents.upload') ? 'active' : '' }}" href="{{ route('documents.upload') }}">
                                    Upload Documents
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('documents.templates') ? 'active' : '' }}" href="{{ route('documents.templates') }}">
                                    Templates
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('documents.shared') ? 'active' : '' }}" href="{{ route('documents.shared') }}">
                                    Shared Files
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Reports -->
                <li class="nav-item">
                    <a class="nav-link has-arrow {{ request()->routeIs('reports*') ? 'active' : '' }}" 
                       href="#!" 
                       data-bs-toggle="collapse" 
                       data-bs-target="#navReports" 
                       aria-expanded="{{ request()->routeIs('reports*') ? 'true' : 'false' }}" 
                       aria-controls="navReports">
                        <i data-feather="bar-chart-2" class="nav-icon me-2 icon-xxs"></i>
                        Reports
                    </a>
                    <div id="navReports" class="collapse {{ request()->routeIs('reports*') ? 'show' : '' }}" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('reports.crime') ? 'active' : '' }}" href="{{ route('reports.crime') }}">
                                    Crime Reports
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('reports.statistics') ? 'active' : '' }}" href="{{ route('reports.statistics') }}">
                                    Case Statistics
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('reports.analytics') ? 'active' : '' }}" href="{{ route('reports.analytics') }}">
                                    Analytics
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Divider -->
                <li class="nav-item">
                    <div class="navbar-heading">System</div>
                </li>

                <!-- User Management -->
                <li class="nav-item">
                    <a class="nav-link has-arrow {{ request()->routeIs('users*') ? 'active' : '' }}"
                       href="#!"
                       data-bs-toggle="collapse"
                       data-bs-target="#navUsers"
                       aria-expanded="{{ request()->routeIs('users*') ? 'true' : 'false' }}"
                       aria-controls="navUsers">
                        <i data-feather="users" class="nav-icon me-2 icon-xxs"></i>
                        User Management
                    </a>
                    <div id="navUsers" class="collapse {{ request()->routeIs('users*') ? 'show' : '' }}" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('users.index') ? 'active' : '' }}" href="{{ route('users.index') }}">
                                    All Users
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('users.create') ? 'active' : '' }}" href="{{ route('users.create') }}">
                                    Add New User
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Role Management -->
                <li class="nav-item">
                    <a class="nav-link has-arrow {{ request()->routeIs('roles*') ? 'active' : '' }}"
                       href="#!"
                       data-bs-toggle="collapse"
                       data-bs-target="#navRoles"
                       aria-expanded="{{ request()->routeIs('roles*') ? 'true' : 'false' }}"
                       aria-controls="navRoles">
                        <i data-feather="shield" class="nav-icon me-2 icon-xxs"></i>
                        Role Management
                    </a>
                    <div id="navRoles" class="collapse {{ request()->routeIs('roles*') ? 'show' : '' }}" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('roles.index') ? 'active' : '' }}" href="{{ route('roles.index') }}">
                                    All Roles
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('roles.create') ? 'active' : '' }}" href="{{ route('roles.create') }}">
                                    Create Role
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('roles.permissions') ? 'active' : '' }}" href="{{ route('roles.permissions') }}">
                                    Permissions
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('roles.assignments') ? 'active' : '' }}" href="{{ route('roles.assignments') }}">
                                    Role Assignments
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Backup Management -->
                <li class="nav-item">
                    <a class="nav-link has-arrow {{ request()->routeIs('backups*') ? 'active' : '' }}"
                       href="#!"
                       data-bs-toggle="collapse"
                       data-bs-target="#navBackups"
                       aria-expanded="{{ request()->routeIs('backups*') ? 'true' : 'false' }}"
                       aria-controls="navBackups">
                        <i data-feather="hard-drive" class="nav-icon me-2 icon-xxs"></i>
                        Backup Management
                    </a>
                    <div id="navBackups" class="collapse {{ request()->routeIs('backups*') ? 'show' : '' }}" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('backups.index') ? 'active' : '' }}" href="{{ route('backups.index') }}">
                                    All Backups
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('backups.create') ? 'active' : '' }}" href="{{ route('backups.create') }}">
                                    Create Backup
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('backups.schedule') ? 'active' : '' }}" href="{{ route('backups.schedule') }}">
                                    Backup Schedule
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('backups.restore') ? 'active' : '' }}" href="{{ route('backups.restore') }}">
                                    Restore Backup
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Settings -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('settings*') ? 'active' : '' }}" href="{{ route('settings.index') }}">
                        <i data-feather="settings" class="nav-icon me-2 icon-xxs"></i>
                        Settings
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
