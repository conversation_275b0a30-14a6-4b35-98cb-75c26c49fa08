@extends('layouts.app')

@section('title', 'Dashboard - ' . config('app.name'))
@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Dashboard</h1>
            <x-breadcrumb :items="[
                ['title' => 'Dashboard']
            ]" />
        </div>
        <div>
            <button type="button" class="btn btn-primary">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Quick Actions
            </button>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div class="row">
        <!-- Statistics Cards -->
        <div class="col-xl-3 col-lg-6 col-md-12 col-12 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($statistics['total_criminals']) }}</h4>
                            <p class="mb-0 text-muted">Total Criminals</p>
                            <small class="text-success">
                                <i data-feather="trending-up" class="icon-xs"></i>
                                +5.2% from last month
                            </small>
                        </div>
                        <div class="icon-shape icon-md bg-light-primary text-primary rounded-2">
                            <i data-feather="users" class="icon-sm"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-12 col-12 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($statistics['active_cases']) }}</h4>
                            <p class="mb-0 text-muted">Active Cases</p>
                            <small class="text-warning">
                                <i data-feather="clock" class="icon-xs"></i>
                                {{ $statistics['new_cases_24h'] }} new today
                            </small>
                        </div>
                        <div class="icon-shape icon-md bg-light-warning text-warning rounded-2">
                            <i data-feather="folder" class="icon-sm"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-12 col-12 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($statistics['evidence_items']) }}</h4>
                            <p class="mb-0 text-muted">Evidence Items</p>
                            <small class="text-info">
                                <i data-feather="shield" class="icon-xs"></i>
                                Secure storage
                            </small>
                        </div>
                        <div class="icon-shape icon-md bg-light-info text-info rounded-2">
                            <i data-feather="archive" class="icon-sm"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-12 col-12 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($statistics['court_hearings']) }}</h4>
                            <p class="mb-0 text-muted">Court Hearings</p>
                            <small class="text-success">
                                <i data-feather="calendar" class="icon-xs"></i>
                                Next 7 days
                            </small>
                        </div>
                        <div class="icon-shape icon-md bg-light-success text-success rounded-2">
                            <i data-feather="briefcase" class="icon-sm"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities and Quick Actions -->
    <div class="row">
        <div class="col-lg-8 col-md-12 col-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="mb-0">Recent Activities</h4>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        @foreach($recentActivities as $activity)
                        <div class="list-group-item d-flex justify-content-between align-items-start border-0">
                            <div class="d-flex align-items-start">
                                <div class="icon-shape icon-sm bg-light-{{ $activity['color'] }} text-{{ $activity['color'] }} rounded-circle me-3 mt-1">
                                    <i data-feather="{{ $activity['icon'] }}" class="icon-xs"></i>
                                </div>
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">{{ $activity['title'] }}</div>
                                    <small class="text-muted">{{ $activity['description'] }}</small>
                                </div>
                            </div>
                            <small class="text-muted">{{ $activity['time'] }}</small>
                        </div>
                        @endforeach

                        <div class="list-group-item border-0 pt-3">
                            <a href="#!" class="btn btn-outline-primary btn-sm w-100">
                                View All Activities
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-12 col-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="mb-0">Quick Actions</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('criminals.create') }}" class="btn btn-outline-primary">
                            <i data-feather="user-plus" class="icon-xs me-2"></i>
                            Add Criminal Profile
                        </a>
                        <a href="{{ route('cases.create') }}" class="btn btn-outline-warning">
                            <i data-feather="folder-plus" class="icon-xs me-2"></i>
                            Create New Case
                        </a>
                        <a href="{{ route('evidence.create') }}" class="btn btn-outline-info">
                            <i data-feather="upload" class="icon-xs me-2"></i>
                            Submit Evidence
                        </a>
                        <a href="{{ route('court.hearings') }}" class="btn btn-outline-success">
                            <i data-feather="calendar" class="icon-xs me-2"></i>
                            Schedule Hearing
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics and Priority Alerts -->
    <div class="row">
        <!-- Crime Analytics Chart -->
        <div class="col-lg-8 col-md-12 col-12 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Crime Trends</h4>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            Last 30 Days
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-period="7days">Last 7 Days</a></li>
                            <li><a class="dropdown-item" href="#" data-period="30days">Last 30 Days</a></li>
                            <li><a class="dropdown-item" href="#" data-period="90days">Last 90 Days</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="crimeTrendsChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Priority Alerts -->
        <div class="col-lg-4 col-md-12 col-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="mb-0">Priority Alerts</h4>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-start border-0 px-0">
                            <div class="d-flex align-items-start">
                                <div class="icon-shape icon-sm bg-light-danger text-danger rounded-circle me-3">
                                    <i data-feather="alert-triangle" class="icon-xs"></i>
                                </div>
                                <div>
                                    <div class="fw-bold text-danger">High Priority Case</div>
                                    <small class="text-muted">Homicide investigation - Lilongwe</small>
                                </div>
                            </div>
                            <span class="badge bg-danger">Urgent</span>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-start border-0 px-0">
                            <div class="d-flex align-items-start">
                                <div class="icon-shape icon-sm bg-light-warning text-warning rounded-circle me-3">
                                    <i data-feather="clock" class="icon-xs"></i>
                                </div>
                                <div>
                                    <div class="fw-bold text-warning">Court Reminder</div>
                                    <small class="text-muted">Hearing tomorrow at 9:00 AM</small>
                                </div>
                            </div>
                            <span class="badge bg-warning">Tomorrow</span>
                        </div>

                        <div class="list-group-item d-flex justify-content-between align-items-start border-0 px-0">
                            <div class="d-flex align-items-start">
                                <div class="icon-shape icon-sm bg-light-info text-info rounded-circle me-3">
                                    <i data-feather="search" class="icon-xs"></i>
                                </div>
                                <div>
                                    <div class="fw-bold text-info">Forensic Results</div>
                                    <small class="text-muted">DNA analysis completed</small>
                                </div>
                            </div>
                            <span class="badge bg-info">Ready</span>
                        </div>

                        <div class="list-group-item border-0 px-0 pt-3">
                            <a href="#!" class="btn btn-outline-primary btn-sm w-100">
                                View All Alerts
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Crime Categories and Location Statistics -->
    <div class="row">
        <!-- Crime Categories Chart -->
        <div class="col-lg-6 col-md-12 col-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="mb-0">Crime Categories</h4>
                </div>
                <div class="card-body">
                    <canvas id="crimeCategoriesChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Crime by Location -->
        <div class="col-lg-6 col-md-12 col-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="mb-0">Crime by Location</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Location</th>
                                    <th>Cases</th>
                                    <th>Trend</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Lilongwe</td>
                                    <td><span class="badge bg-primary">89</span></td>
                                    <td><span class="text-success"><i data-feather="trending-up" class="icon-xs"></i> +12%</span></td>
                                </tr>
                                <tr>
                                    <td>Blantyre</td>
                                    <td><span class="badge bg-primary">67</span></td>
                                    <td><span class="text-danger"><i data-feather="trending-down" class="icon-xs"></i> -5%</span></td>
                                </tr>
                                <tr>
                                    <td>Mzuzu</td>
                                    <td><span class="badge bg-primary">45</span></td>
                                    <td><span class="text-success"><i data-feather="trending-up" class="icon-xs"></i> +8%</span></td>
                                </tr>
                                <tr>
                                    <td>Zomba</td>
                                    <td><span class="badge bg-primary">34</span></td>
                                    <td><span class="text-muted"><i data-feather="minus" class="icon-xs"></i> 0%</span></td>
                                </tr>
                                <tr>
                                    <td>Kasungu</td>
                                    <td><span class="badge bg-primary">23</span></td>
                                    <td><span class="text-success"><i data-feather="trending-up" class="icon-xs"></i> +3%</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script src="{{ asset('assets/libs/apexcharts/dist/apexcharts.min.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();

    // Crime Trends Chart
    const crimeTrendsCtx = document.getElementById('crimeTrendsChart');
    if (crimeTrendsCtx) {
        // This will be populated with actual data via AJAX
        loadCrimeTrendsChart('30days');
    }

    // Crime Categories Chart
    const crimeCategoriesCtx = document.getElementById('crimeCategoriesChart');
    if (crimeCategoriesCtx) {
        loadCrimeCategoriesChart();
    }

    // Period selector for crime trends
    document.querySelectorAll('[data-period]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            const period = this.getAttribute('data-period');
            loadCrimeTrendsChart(period);
        });
    });
});

function loadCrimeTrendsChart(period) {
    // Fetch data from the analytics endpoint
    fetch(`{{ route('dashboard.analytics') }}?period=${period}`)
        .then(response => response.json())
        .then(data => {
            // Implementation for Chart.js will be added here
            console.log('Crime trends data:', data.crime_trends);
        })
        .catch(error => {
            console.error('Error loading crime trends:', error);
        });
}

function loadCrimeCategoriesChart() {
    // Fetch data from the analytics endpoint
    fetch(`{{ route('dashboard.analytics') }}`)
        .then(response => response.json())
        .then(data => {
            // Implementation for Chart.js will be added here
            console.log('Crime categories data:', data.crime_categories);
        })
        .catch(error => {
            console.error('Error loading crime categories:', error);
        });
}
</script>
@endpush
