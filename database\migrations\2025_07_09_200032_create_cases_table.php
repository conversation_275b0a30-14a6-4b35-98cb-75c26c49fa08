<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cases', function (Blueprint $table) {
            $table->id();

            // Unique identifier
            $table->string('case_number', 30)->unique();
            $table->string('slug')->unique();

            // Basic Information
            $table->string('title', 300);
            $table->text('description');
            $table->enum('type', [
                'Theft', 'Robbery', 'Burglary', 'Assault', 'Murder', 'Rape',
                'Drug Offense', 'Fraud', 'Embezzlement', 'Domestic Violence',
                'Traffic Offense', 'Cybercrime', 'Corruption', 'Other'
            ]);
            $table->enum('severity', ['Minor', 'Moderate', 'Serious', 'Critical']);
            $table->enum('priority', ['Low', 'Medium', 'High', 'Urgent']);

            // Location Information
            $table->text('incident_location');
            $table->string('district', 100);
            $table->string('traditional_authority', 100)->nullable();
            $table->string('village', 100)->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();

            // Date and Time Information
            $table->datetime('incident_date');
            $table->datetime('reported_date');
            $table->datetime('investigation_started_date')->nullable();

            // People Involved
            $table->foreignId('reporting_officer_id')->constrained('users')->onDelete('restrict');
            $table->foreignId('investigating_officer_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('supervisor_id')->nullable()->constrained('users')->onDelete('set null');

            // Complainant Information
            $table->string('complainant_name', 200);
            $table->string('complainant_phone', 20)->nullable();
            $table->string('complainant_email', 100)->nullable();
            $table->text('complainant_address')->nullable();
            $table->string('complainant_id_number', 20)->nullable();

            // Status and Progress
            $table->enum('status', [
                'Reported', 'Under Investigation', 'Pending Evidence', 'Pending Arrest',
                'Suspect Arrested', 'Case Closed', 'Transferred', 'Cold Case'
            ])->default('Reported');
            $table->integer('progress_percentage')->default(0);
            $table->text('status_notes')->nullable();

            // Court Information
            $table->boolean('court_case_filed')->default(false);
            $table->string('court_case_number', 50)->nullable();
            $table->string('court_name', 200)->nullable();
            $table->date('court_date')->nullable();

            // Financial Information
            $table->decimal('estimated_loss', 12, 2)->nullable();
            $table->decimal('recovered_amount', 12, 2)->nullable();
            $table->string('currency', 10)->default('MWK');

            // Additional Information
            $table->text('modus_operandi')->nullable();
            $table->text('evidence_summary')->nullable();
            $table->text('witness_summary')->nullable();
            $table->text('investigation_notes')->nullable();
            $table->boolean('media_attention')->default(false);
            $table->boolean('high_profile')->default(false);

            // Closure Information
            $table->date('closed_date')->nullable();
            $table->enum('closure_reason', [
                'Solved', 'Insufficient Evidence', 'Suspect Not Found',
                'Withdrawn by Complainant', 'Transferred', 'Other'
            ])->nullable();
            $table->text('closure_notes')->nullable();

            // Standard tracking fields
            $table->timestamps();
            $table->softDeletes();

            // User tracking fields
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');

            // Indexes for performance
            $table->index(['case_number']);
            $table->index(['type']);
            $table->index(['status']);
            $table->index(['priority']);
            $table->index(['district']);
            $table->index(['incident_date']);
            $table->index(['reported_date']);
            $table->index(['reporting_officer_id']);
            $table->index(['investigating_officer_id']);
            $table->index(['created_by']);
            $table->index(['updated_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cases');
    }
};
