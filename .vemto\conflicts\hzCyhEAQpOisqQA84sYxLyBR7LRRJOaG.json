{"conflicts": [{"id": "370982c4-17be-49b2-8caa-9392caf2a4f0", "currentContent": "<?php\n\nnamespace App\\Policies;\n\nuse App\\Models\\Team;\nuse App\\Models\\User;\nuse Illuminate\\Auth\\Access\\HandlesAuthorization;\n\nclass TeamPolicy\n{\n    use HandlesAuthorization;\n\n    /**\n     * Determine whether the user can view any models.\n     */\n    public function viewAny(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can view the model.\n     */\n    public function view(User $user, Team $team): bool\n    {\n        return $user->belongsToTeam($team);\n    }\n\n    /**\n     * Determine whether the user can create models.\n     */\n    public function create(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can update the model.\n     */\n    public function update(User $user, Team $team): bool\n    {\n        return $user->ownsTeam($team);\n    }\n\n    /**\n     * Determine whether the user can add team members.\n     */\n    public function addTeamMember(User $user, Team $team): bool\n    {\n        return $user->ownsTeam($team);\n    }\n\n    /**\n     * Determine whether the user can update team member permissions.\n     */\n    public function updateTeamMember(User $user, Team $team): bool\n    {\n        return $user->ownsTeam($team);\n    }\n\n    /**\n     * Determine whether the user can remove team members.\n     */\n    public function removeTeamMember(User $user, Team $team): bool\n    {\n        return $user->ownsTeam($team);\n    }\n\n    /**\n     * Determine whether the user can delete the model.\n     */\n    public function delete(User $user, Team $team): bool\n    {\n        return $user->ownsTeam($team);\n    }\n}\n", "newContent": "<?php\n\nnamespace App\\Policies;\n\nuse App\\Models\\Team;\nuse App\\Models\\User;\n\nclass TeamPolicy\n{\n    /**\n     * Determine whether the user can view any models.\n     */\n    public function viewAny(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can view the model.\n     */\n    public function view(User $user, Team $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can create models.\n     */\n    public function create(User $user): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can update the model.\n     */\n    public function update(User $user, Team $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can delete the model.\n     */\n    public function delete(User $user, Team $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can restore the model.\n     */\n    public function restore(User $user, Team $model): bool\n    {\n        return true;\n    }\n\n    /**\n     * Determine whether the user can permanently delete the model.\n     */\n    public function forceDelete(User $user, Team $model): bool\n    {\n        return true;\n    }\n}\n"}]}