<?php

namespace App\Models;

use App\Traits\HasUserTracking;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class CriminalRelationship extends Model
{
    use HasFactory, HasUserTracking, LogsActivity, SoftDeletes;

    protected $fillable = [
        'criminal_id',
        'related_person_name',
        'relationship_type',
        'relationship_detail',
        'contact_info',
        'notes',
        'verified',
        'last_contact',
    ];

    protected $casts = [
        'verified' => 'boolean',
        'last_contact' => 'date',
    ];

    protected $dates = [
        'last_contact',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'related_person_name',
                'relationship_type',
                'relationship_detail',
                'contact_info',
                'verified',
                'last_contact'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the criminal that owns this relationship.
     */
    public function criminal()
    {
        return $this->belongsTo(Criminal::class);
    }

    /**
     * Scope for verified relationships.
     */
    public function scopeVerified($query)
    {
        return $query->where('verified', true);
    }

    /**
     * Scope for unverified relationships.
     */
    public function scopeUnverified($query)
    {
        return $query->where('verified', false);
    }

    /**
     * Scope for specific relationship type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('relationship_type', $type);
    }

    /**
     * Scope for family relationships.
     */
    public function scopeFamily($query)
    {
        return $query->where('relationship_type', 'Family');
    }

    /**
     * Scope for associate relationships.
     */
    public function scopeAssociates($query)
    {
        return $query->where('relationship_type', 'Associate');
    }

    /**
     * Scope for gang relationships.
     */
    public function scopeGang($query)
    {
        return $query->where('relationship_type', 'Gang');
    }

    /**
     * Scope for witness relationships.
     */
    public function scopeWitnesses($query)
    {
        return $query->where('relationship_type', 'Witness');
    }

    /**
     * Scope for victim relationships.
     */
    public function scopeVictims($query)
    {
        return $query->where('relationship_type', 'Victim');
    }

    /**
     * Scope for recent contacts.
     */
    public function scopeRecentContact($query, $days = 30)
    {
        return $query->where('last_contact', '>=', now()->subDays($days));
    }

    /**
     * Scope for search by name.
     */
    public function scopeSearchByName($query, $search)
    {
        return $query->where('related_person_name', 'like', "%{$search}%");
    }

    /**
     * Get the relationship type badge color.
     */
    public function getRelationshipTypeBadgeColorAttribute()
    {
        return match($this->relationship_type) {
            'Family' => 'bg-primary',
            'Associate' => 'bg-info',
            'Gang' => 'bg-danger',
            'Witness' => 'bg-success',
            'Victim' => 'bg-warning',
            default => 'bg-secondary',
        };
    }

    /**
     * Get the verification status badge.
     */
    public function getVerificationBadgeAttribute()
    {
        return $this->verified 
            ? '<span class="badge bg-success">Verified</span>'
            : '<span class="badge bg-warning">Unverified</span>';
    }

    /**
     * Get formatted last contact date.
     */
    public function getFormattedLastContactAttribute()
    {
        if (!$this->last_contact) {
            return 'Never';
        }

        $diffInDays = $this->last_contact->diffInDays(now());
        
        if ($diffInDays === 0) {
            return 'Today';
        } elseif ($diffInDays === 1) {
            return 'Yesterday';
        } elseif ($diffInDays <= 7) {
            return $diffInDays . ' days ago';
        } elseif ($diffInDays <= 30) {
            return $this->last_contact->format('M j');
        } else {
            return $this->last_contact->format('M j, Y');
        }
    }

    /**
     * Get the contact status.
     */
    public function getContactStatusAttribute()
    {
        if (!$this->last_contact) {
            return 'No contact';
        }

        $diffInDays = $this->last_contact->diffInDays(now());
        
        if ($diffInDays <= 7) {
            return 'Recent';
        } elseif ($diffInDays <= 30) {
            return 'Moderate';
        } else {
            return 'Old';
        }
    }

    /**
     * Get the contact status color.
     */
    public function getContactStatusColorAttribute()
    {
        return match($this->contact_status) {
            'Recent' => 'text-success',
            'Moderate' => 'text-warning',
            'Old' => 'text-danger',
            default => 'text-muted',
        };
    }

    /**
     * Check if the relationship is high priority.
     */
    public function isHighPriority()
    {
        return in_array($this->relationship_type, ['Gang', 'Associate']) && $this->verified;
    }

    /**
     * Check if the relationship needs verification.
     */
    public function needsVerification()
    {
        return !$this->verified;
    }

    /**
     * Mark the relationship as verified.
     */
    public function markAsVerified()
    {
        $this->update(['verified' => true]);
    }

    /**
     * Mark the relationship as unverified.
     */
    public function markAsUnverified()
    {
        $this->update(['verified' => false]);
    }

    /**
     * Update last contact date.
     */
    public function updateLastContact($date = null)
    {
        $this->update(['last_contact' => $date ?? now()->toDateString()]);
    }

    /**
     * Get available relationship types.
     */
    public static function getRelationshipTypes()
    {
        return [
            'Family' => 'Family Member',
            'Associate' => 'Known Associate',
            'Gang' => 'Gang Member',
            'Witness' => 'Witness',
            'Victim' => 'Victim',
        ];
    }

    /**
     * Get relationship type options for forms.
     */
    public static function getRelationshipTypeOptions()
    {
        return collect(self::getRelationshipTypes())->map(function ($label, $value) {
            return ['value' => $value, 'label' => $label];
        })->values()->toArray();
    }
}
