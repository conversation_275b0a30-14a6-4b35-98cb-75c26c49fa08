<?php $__env->startSection('title', 'Criminal Profile: ' . $criminal->full_name . ' - Criminal Management System'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Criminal Profile: <?php echo e($criminal->full_name); ?></h1>
            <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => ['items' => [
                ['title' => 'Criminal Profiles', 'url' => route('criminals.index')],
                ['title' => $criminal->full_name]
            ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                ['title' => 'Criminal Profiles', 'url' => route('criminals.index')],
                ['title' => $criminal->full_name]
            ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
        </div>
        <div class="d-flex gap-2">
            <a href="<?php echo e(route('criminals.edit', $criminal)); ?>" class="btn btn-outline-primary">
                <i data-feather="edit" class="icon-xs me-2"></i>
                Edit Profile
            </a>
            <a href="<?php echo e(route('criminals.index')); ?>" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to List
            </a>
        </div>
    </div>

    <!-- Criminal Status Alerts -->
    <?php if($criminal->is_wanted): ?>
        <div class="alert alert-danger d-flex align-items-center mb-4" role="alert">
            <i data-feather="alert-triangle" class="icon-sm me-2"></i>
            <div>
                <strong>WANTED CRIMINAL:</strong> This individual is currently wanted by law enforcement.
            </div>
        </div>
    <?php endif; ?>

    <?php if($criminal->risk_level === 'Critical' || $criminal->risk_level === 'High'): ?>
        <div class="alert alert-warning d-flex align-items-center mb-4" role="alert">
            <i data-feather="shield" class="icon-sm me-2"></i>
            <div>
                <strong><?php echo e(strtoupper($criminal->risk_level)); ?> RISK:</strong> Exercise caution when dealing with this individual.
            </div>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-8">
            <!-- Personal Details Tab Navigation -->
            <div class="card mb-4">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="criminalTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" 
                                    data-bs-target="#personal" type="button" role="tab">
                                <i data-feather="user" class="icon-xs me-2"></i>
                                Personal Details
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="arrests-tab" data-bs-toggle="tab" 
                                    data-bs-target="#arrests" type="button" role="tab">
                                <i data-feather="handcuffs" class="icon-xs me-2"></i>
                                Arrest History
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="biometrics-tab" data-bs-toggle="tab" 
                                    data-bs-target="#biometrics" type="button" role="tab">
                                <i data-feather="fingerprint" class="icon-xs me-2"></i>
                                Biometric Data
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="relationships-tab" data-bs-toggle="tab" 
                                    data-bs-target="#relationships" type="button" role="tab">
                                <i data-feather="users" class="icon-xs me-2"></i>
                                Relationships
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="medical-tab" data-bs-toggle="tab" 
                                    data-bs-target="#medical" type="button" role="tab">
                                <i data-feather="heart" class="icon-xs me-2"></i>
                                Medical Info
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="criminalTabsContent">
                        <!-- Personal Details Tab -->
                        <div class="tab-pane fade show active" id="personal" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="mb-3">Basic Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">Criminal Number:</td>
                                            <td><?php echo e($criminal->criminal_number); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Full Name:</td>
                                            <td><?php echo e($criminal->full_name); ?></td>
                                        </tr>
                                        <?php if($criminal->alias): ?>
                                        <tr>
                                            <td class="fw-bold">Alias:</td>
                                            <td><span class="badge bg-info"><?php echo e($criminal->alias); ?></span></td>
                                        </tr>
                                        <?php endif; ?>
                                        <tr>
                                            <td class="fw-bold">Gender:</td>
                                            <td><?php echo e($criminal->gender); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Date of Birth:</td>
                                            <td><?php echo e($criminal->date_of_birth ? $criminal->date_of_birth->format('d/m/Y') : 'Not specified'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Age:</td>
                                            <td><?php echo e($criminal->age ?? 'Unknown'); ?> years</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Nationality:</td>
                                            <td><?php echo e($criminal->nationality ?? 'Not specified'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">National ID:</td>
                                            <td><?php echo e($criminal->national_id ?? 'Not provided'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="mb-3">Physical Description</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">Height:</td>
                                            <td><?php echo e($criminal->height ? $criminal->height . ' meters' : 'Not specified'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Weight:</td>
                                            <td><?php echo e($criminal->weight ? $criminal->weight . ' kg' : 'Not specified'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Eye Color:</td>
                                            <td><?php echo e($criminal->eye_color ?? 'Not specified'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Hair Color:</td>
                                            <td><?php echo e($criminal->hair_color ?? 'Not specified'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Complexion:</td>
                                            <td><?php echo e($criminal->complexion ?? 'Not specified'); ?></td>
                                        </tr>
                                        <?php if($criminal->distinguishing_marks): ?>
                                        <tr>
                                            <td class="fw-bold">Distinguishing Marks:</td>
                                            <td><?php echo e($criminal->distinguishing_marks); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if($criminal->scars_tattoos): ?>
                                        <tr>
                                            <td class="fw-bold">Scars & Tattoos:</td>
                                            <td><?php echo e($criminal->scars_tattoos); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="mb-3">Contact Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">Phone Number:</td>
                                            <td><?php echo e($criminal->phone_number ?? 'Not provided'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Email:</td>
                                            <td><?php echo e($criminal->email ?? 'Not provided'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Current Address:</td>
                                            <td><?php echo e($criminal->current_address ?? 'Not provided'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Permanent Address:</td>
                                            <td><?php echo e($criminal->permanent_address ?? 'Not provided'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="mb-3">Location Details</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">District:</td>
                                            <td><?php echo e($criminal->district ?? 'Not specified'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Traditional Authority:</td>
                                            <td><?php echo e($criminal->traditional_authority ?? 'Not specified'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Village:</td>
                                            <td><?php echo e($criminal->village ?? 'Not specified'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Place of Birth:</td>
                                            <td><?php echo e($criminal->place_of_birth ?? 'Not specified'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <?php if($criminal->emergency_contact_name): ?>
                            <hr>
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="mb-3">Emergency Contact</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">Name:</td>
                                            <td><?php echo e($criminal->emergency_contact_name); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Phone:</td>
                                            <td><?php echo e($criminal->emergency_contact_phone ?? 'Not provided'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Relationship:</td>
                                            <td><?php echo e($criminal->emergency_contact_relationship ?? 'Not specified'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="mb-3">Additional Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">Occupation:</td>
                                            <td><?php echo e($criminal->occupation ?? 'Not specified'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Education Level:</td>
                                            <td><?php echo e($criminal->education_level ?? 'Not specified'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Marital Status:</td>
                                            <td><?php echo e($criminal->marital_status ?? 'Not specified'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if($criminal->known_associates || $criminal->notes): ?>
                            <hr>
                            <div class="row">
                                <?php if($criminal->known_associates): ?>
                                <div class="col-md-6">
                                    <h5 class="mb-3">Known Associates</h5>
                                    <p class="text-muted"><?php echo e($criminal->known_associates); ?></p>
                                </div>
                                <?php endif; ?>
                                <?php if($criminal->notes): ?>
                                <div class="col-md-6">
                                    <h5 class="mb-3">Special Instructions & Notes</h5>
                                    <p class="text-muted"><?php echo e($criminal->notes); ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Arrest History Tab -->
                        <div class="tab-pane fade" id="arrests" role="tabpanel">
                            <?php if($criminal->arrests->count() > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Time</th>
                                                <th>Location</th>
                                                <th>Charges</th>
                                                <th>Officer</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $criminal->arrests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $arrest): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($arrest->arrest_date->format('d/m/Y')); ?></td>
                                                <td><?php echo e($arrest->arrest_time ? $arrest->arrest_time->format('H:i') : 'N/A'); ?></td>
                                                <td><?php echo e($arrest->arrest_location ?? 'Not specified'); ?></td>
                                                <td>
                                                    <?php if($arrest->charges): ?>
                                                        <span class="badge bg-warning"><?php echo e(Str::limit($arrest->charges, 30)); ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">No charges listed</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo e($arrest->arrestingOfficer->name ?? 'Unknown'); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo e($arrest->status == 'Active' ? 'danger' : 'secondary'); ?>">
                                                        <?php echo e($arrest->status); ?>

                                                    </span>
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" 
                                                            data-bs-target="#arrestModal<?php echo e($arrest->id); ?>">
                                                        <i data-feather="eye" class="icon-xs"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i data-feather="file-x" class="icon-xl text-muted mb-3"></i>
                                    <h5 class="text-muted">No Arrest Records</h5>
                                    <p class="text-muted">No arrest history found for this criminal.</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Biometric Data Tab -->
                        <div class="tab-pane fade" id="biometrics" role="tabpanel">
                            <?php if($criminal->biometrics->count() > 0): ?>
                                <div class="row">
                                    <?php $__currentLoopData = $criminal->biometrics; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $biometric): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="card-title"><?php echo e($biometric->biometric_type); ?></h6>
                                                        <p class="card-text text-muted"><?php echo e($biometric->description ?? 'No description'); ?></p>
                                                        <small class="text-muted">
                                                            Collected: <?php echo e($biometric->collected_date ? $biometric->collected_date->format('d/m/Y') : 'Unknown'); ?>

                                                            <?php if($biometric->collector): ?>
                                                                by <?php echo e($biometric->collector->name); ?>

                                                            <?php endif; ?>
                                                        </small>
                                                    </div>
                                                    <div>
                                                        <?php if($biometric->verified): ?>
                                                            <span class="badge bg-success">Verified</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-warning">Pending</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i data-feather="fingerprint" class="icon-xl text-muted mb-3"></i>
                                    <h5 class="text-muted">No Biometric Data</h5>
                                    <p class="text-muted">No biometric information has been collected for this criminal.</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Relationships Tab -->
                        <div class="tab-pane fade" id="relationships" role="tabpanel">
                            <?php if($criminal->relationships->count() > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Relationship Type</th>
                                                <th>Details</th>
                                                <th>Contact Info</th>
                                                <th>Status</th>
                                                <th>Last Contact</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $criminal->relationships; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relationship): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($relationship->related_person_name); ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo e($relationship->relationship_type); ?></span>
                                                </td>
                                                <td><?php echo e($relationship->relationship_detail ?? 'N/A'); ?></td>
                                                <td><?php echo e($relationship->contact_info ?? 'Not provided'); ?></td>
                                                <td>
                                                    <?php if($relationship->verified): ?>
                                                        <span class="badge bg-success">Verified</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">Unverified</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo e($relationship->last_contact ? $relationship->last_contact->format('d/m/Y') : 'Unknown'); ?></td>
                                            </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i data-feather="users" class="icon-xl text-muted mb-3"></i>
                                    <h5 class="text-muted">No Relationships Recorded</h5>
                                    <p class="text-muted">No relationships have been documented for this criminal.</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Medical Information Tab -->
                        <div class="tab-pane fade" id="medical" role="tabpanel">
                            <?php if($criminal->medicalInfo): ?>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5 class="mb-3">Medical Conditions</h5>
                                        <table class="table table-borderless">
                                            <tr>
                                                <td class="fw-bold">Medical Condition:</td>
                                                <td><?php echo e($criminal->medicalInfo->medical_condition ?? 'None reported'); ?></td>
                                            </tr>
                                            <tr>
                                                <td class="fw-bold">Blood Type:</td>
                                                <td><?php echo e($criminal->medicalInfo->blood_type ?? 'Unknown'); ?></td>
                                            </tr>
                                            <tr>
                                                <td class="fw-bold">Allergies:</td>
                                                <td><?php echo e($criminal->medicalInfo->allergies ?? 'None reported'); ?></td>
                                            </tr>
                                            <tr>
                                                <td class="fw-bold">Medications:</td>
                                                <td><?php echo e($criminal->medicalInfo->medications ?? 'None reported'); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h5 class="mb-3">Additional Information</h5>
                                        <?php if($criminal->medicalInfo->special_instructions): ?>
                                            <div class="alert alert-info">
                                                <strong>Special Instructions:</strong><br>
                                                <?php echo e($criminal->medicalInfo->special_instructions); ?>

                                            </div>
                                        <?php endif; ?>
                                        <?php if($criminal->medicalInfo->medical_history): ?>
                                            <div class="mt-3">
                                                <strong>Medical History:</strong><br>
                                                <p class="text-muted"><?php echo e($criminal->medicalInfo->medical_history); ?></p>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($criminal->medicalInfo->last_updated): ?>
                                            <small class="text-muted">
                                                Last updated: <?php echo e($criminal->medicalInfo->last_updated->format('d/m/Y')); ?>

                                                <?php if($criminal->medicalInfo->updater): ?>
                                                    by <?php echo e($criminal->medicalInfo->updater->name); ?>

                                                <?php endif; ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i data-feather="heart" class="icon-xl text-muted mb-3"></i>
                                    <h5 class="text-muted">No Medical Information</h5>
                                    <p class="text-muted">No medical information has been recorded for this criminal.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-lg-4">
            <!-- Criminal Photo -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Criminal Photo</h5>
                </div>
                <div class="card-body text-center">
                    <?php if($criminal->getFirstMediaUrl('mugshots')): ?>
                        <img src="<?php echo e($criminal->getFirstMediaUrl('mugshots')); ?>" 
                             alt="Criminal Photo" class="img-fluid rounded" style="max-height: 300px;">
                    <?php else: ?>
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                             style="height: 200px;">
                            <div class="text-center">
                                <i data-feather="camera" class="icon-xl text-muted mb-2"></i>
                                <p class="text-muted">No photo available</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Status Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Status Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td class="fw-bold">Status:</td>
                            <td>
                                <span class="badge bg-<?php echo e($criminal->status == 'Active' ? 'success' : 'secondary'); ?>">
                                    <?php echo e($criminal->status); ?>

                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Risk Level:</td>
                            <td>
                                <span class="badge bg-<?php echo e($criminal->risk_level == 'Critical' ? 'danger' : 
                                    ($criminal->risk_level == 'High' ? 'warning' : 
                                    ($criminal->risk_level == 'Medium' ? 'info' : 'success'))); ?>">
                                    <?php echo e($criminal->risk_level); ?>

                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Wanted:</td>
                            <td>
                                <?php if($criminal->is_wanted): ?>
                                    <span class="badge bg-danger">Yes</span>
                                <?php else: ?>
                                    <span class="badge bg-success">No</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Repeat Offender:</td>
                            <td>
                                <?php if($criminal->is_repeat_offender): ?>
                                    <span class="badge bg-warning">Yes</span>
                                <?php else: ?>
                                    <span class="badge bg-success">No</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Quick Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary"><?php echo e($criminal->arrests->count()); ?></h4>
                            <small class="text-muted">Total Arrests</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-info"><?php echo e($criminal->biometrics->count()); ?></h4>
                            <small class="text-muted">Biometric Records</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning"><?php echo e($criminal->relationships->count()); ?></h4>
                            <small class="text-muted">Known Associates</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?php echo e($criminal->cases->count() ?? 0); ?></h4>
                            <small class="text-muted">Linked Cases</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Record Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Record Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td class="fw-bold">Created:</td>
                            <td><?php echo e($criminal->created_at->format('d/m/Y H:i')); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Created By:</td>
                            <td><?php echo e($criminal->creator->name ?? 'System'); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Last Updated:</td>
                            <td><?php echo e($criminal->updated_at->format('d/m/Y H:i')); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Updated By:</td>
                            <td><?php echo e($criminal->updater->name ?? 'System'); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Initialize Bootstrap tabs
    var triggerTabList = [].slice.call(document.querySelectorAll('#criminalTabs button'))
    triggerTabList.forEach(function (triggerEl) {
        var tabTrigger = new bootstrap.Tab(triggerEl)
        
        triggerEl.addEventListener('click', function (event) {
            event.preventDefault()
            tabTrigger.show()
        })
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/criminals/show.blade.php ENDPATH**/ ?>