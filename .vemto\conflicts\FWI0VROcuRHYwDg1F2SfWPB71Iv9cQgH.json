{"conflicts": [{"id": "373639ac-f1aa-434f-a242-74f0ff5c2f4e", "currentContent": "<?php\n\nnamespace Database\\Factories;\n\nuse App\\Models\\Team;\nuse App\\Models\\User;\nuse Illuminate\\Database\\Eloquent\\Factories\\Factory;\nuse Illuminate\\Support\\Facades\\Hash;\nuse Illuminate\\Support\\Str;\nuse Laravel\\Jetstream\\Features;\n\n/**\n * @extends \\Illuminate\\Database\\Eloquent\\Factories\\Factory<\\App\\Models\\User>\n */\nclass UserFactory extends Factory\n{\n    /**\n     * The current password being used by the factory.\n     */\n    protected static ?string $password;\n\n    /**\n     * Define the model's default state.\n     *\n     * @return array<string, mixed>\n     */\n    public function definition(): array\n    {\n        return [\n            'name' => fake()->name(),\n            'email' => fake()->unique()->safeEmail(),\n            'email_verified_at' => now(),\n            'password' => static::$password ??= Hash::make('password'),\n            'two_factor_secret' => null,\n            'two_factor_recovery_codes' => null,\n            'remember_token' => Str::random(10),\n            'profile_photo_path' => null,\n            'current_team_id' => null,\n        ];\n    }\n\n    /**\n     * Indicate that the model's email address should be unverified.\n     */\n    public function unverified(): static\n    {\n        return $this->state(fn (array $attributes) => [\n            'email_verified_at' => null,\n        ]);\n    }\n\n    /**\n     * Indicate that the user should have a personal team.\n     */\n    public function withPersonalTeam(?callable $callback = null): static\n    {\n        if (! Features::hasTeamFeatures()) {\n            return $this->state([]);\n        }\n\n        return $this->has(\n            Team::factory()\n                ->state(fn (array $attributes, User $user) => [\n                    'name' => $user->name.'\\'s Team',\n                    'user_id' => $user->id,\n                    'personal_team' => true,\n                ])\n                ->when(is_callable($callback), $callback),\n            'ownedTeams'\n        );\n    }\n}\n", "newContent": "<?php\n\nnamespace Database\\Factories;\n\nuse App\\Models\\User;\nuse Illuminate\\Support\\Str;\nuse Illuminate\\Database\\Eloquent\\Factories\\Factory;\n\nclass UserFactory extends Factory\n{\n    /**\n     * The name of the factory's corresponding model.\n     *\n     * @var string\n     */\n    protected $model = User::class;\n\n    /**\n     * Define the model's default state.\n     *\n     * @return array<string, mixed>\n     */\n    public function definition(): array\n    {\n        return [\n            'name' => fake()->name(),\n            'email' => fake()\n                ->unique()\n                ->safeEmail(),\n            'email_verified_at' => now(),\n            'password' => \\Hash::make('password'),\n            'two_factor_secret' => fake()->text(),\n            'two_factor_recovery_codes' => fake()->text(),\n            'remember_token' => \\Str::random(10),\n            'created_by' => fake()->word(),\n            'updated_by' => fake()->word(),\n        ];\n    }\n\n    /**\n     * Indicate that the model's email address should be unverified.\n     */\n    public function unverified(): Factory\n    {\n        return $this->state(function (array $attributes) {\n            return [\n                'email_verified_at' => null,\n            ];\n        });\n    }\n}\n"}]}