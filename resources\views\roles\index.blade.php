@extends('layouts.app')

@section('title', 'Role Management - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Role Management</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'Role Management']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('roles.create') }}" class="btn btn-primary">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Create Role
            </a>
        </div>
    </div>

    <!-- Role Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">{{ $roles->total() }}</h4>
                    <p class="mb-0">Total Roles</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    @php
                        $systemRoleNames = ['Super Administrator', 'Administrator', 'Inspector', 'Officer', 'Forensic Analyst', 'Court Liaison', 'Viewer'];
                        $systemRolesCount = \Spatie\Permission\Models\Role::whereIn('name', $systemRoleNames)->count();
                    @endphp
                    <h4 class="text-success">{{ $systemRolesCount }}</h4>
                    <p class="mb-0">System Roles</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    @php
                        $totalUsers = \App\Models\User::count();
                    @endphp
                    <h4 class="text-info">{{ $totalUsers }}</h4>
                    <p class="mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    @php
                        $totalPermissions = \Spatie\Permission\Models\Permission::count();
                    @endphp
                    <h4 class="text-warning">{{ $totalPermissions }}</h4>
                    <p class="mb-0">Permissions</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Roles List -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4 class="mb-0">System Roles</h4>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i data-feather="download" class="icon-xs me-2"></i>
                            Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#!" onclick="exportRoles('excel')">
                                <i data-feather="file-text" class="icon-xs me-2"></i>Export to Excel
                            </a></li>
                            <li><a class="dropdown-item" href="#!" onclick="exportRoles('pdf')">
                                <i data-feather="file" class="icon-xs me-2"></i>Export to PDF
                            </a></li>
                        </ul>
                    </div>
                    <a href="{{ route('roles.permissions') }}" class="btn btn-sm btn-outline-secondary">
                        <i data-feather="key" class="icon-xs me-2"></i>
                        Permissions
                    </a>
                    <a href="{{ route('roles.assignments') }}" class="btn btn-sm btn-outline-primary">
                        <i data-feather="users" class="icon-xs me-2"></i>
                        Assignments
                    </a>
                    <a href="{{ route('roles.create') }}" class="btn btn-sm btn-primary">
                        <i data-feather="plus" class="icon-xs me-2"></i>
                        Create Role
                    </a>
                </div>
            </div>

            <!-- Quick Filters -->
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary active" onclick="filterRoles('all')">
                    All Roles <span class="badge bg-primary ms-1">8</span>
                </button>
                <button class="btn btn-sm btn-outline-success" onclick="filterRoles('system')">
                    System Roles <span class="badge bg-success ms-1">6</span>
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="filterRoles('custom')">
                    Custom Roles <span class="badge bg-info ms-1">2</span>
                </button>
                <button class="btn btn-sm btn-outline-warning" onclick="filterRoles('inactive')">
                    Inactive <span class="badge bg-warning ms-1">0</span>
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllRoles">
                                </div>
                            </th>
                            <th>
                                <a href="#!" class="text-decoration-none text-dark d-flex align-items-center">
                                    Role Name
                                    <i data-feather="chevron-up" class="icon-xs ms-1"></i>
                                </a>
                            </th>
                            <th>Display Name</th>
                            <th>Users</th>
                            <th>Permissions</th>
                            <th>Type</th>
                            <th>Created</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($roles as $role)
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input role-checkbox" type="checkbox" value="{{ $role->id }}"
                                           {{ $role->type === 'system' ? 'disabled' : '' }}>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @php
                                        $avatarColors = [
                                            'super_admin' => 'bg-danger',
                                            'admin' => 'bg-warning',
                                            'inspector' => 'bg-primary',
                                            'officer' => 'bg-success',
                                            'forensic_analyst' => 'bg-info',
                                            'court_liaison' => 'bg-secondary',
                                            'viewer' => 'bg-light text-dark',
                                            'detective' => 'bg-dark'
                                        ];
                                        $avatarColor = $avatarColors[$role->name] ?? 'bg-primary';
                                    @endphp
                                    <div>
                                        <strong>{{ ucwords(str_replace('_', ' ', $role->name)) }}</strong>
                                        <br><small class="text-muted">{{ $role->name }} role</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                @php
                                    $badgeColors = [
                                        'super_admin' => 'bg-danger',
                                        'admin' => 'bg-warning',
                                        'inspector' => 'bg-primary',
                                        'officer' => 'bg-success',
                                        'forensic_analyst' => 'bg-info',
                                        'court_liaison' => 'bg-secondary',
                                        'viewer' => 'bg-light text-dark',
                                        'detective' => 'bg-dark'
                                    ];
                                    $badgeColor = $badgeColors[$role->name] ?? 'bg-primary';
                                @endphp
                                <span class="badge {{ $badgeColor }}">{{ ucwords(str_replace('_', ' ', $role->name)) }}</span>
                            </td>
                            <td>
                                <a href="#!" class="text-decoration-none">
                                    <span class="badge bg-primary">{{ $role->users_count }} users</span>
                                </a>
                            </td>
                            <td>
                                @if($role->name === 'super_admin')
                                    <span class="badge bg-success">All Permissions</span>
                                @else
                                    <span class="badge bg-info">{{ $role->permissions_count }} permissions</span>
                                @endif
                                <br><small class="text-muted">{{ $role->permissions_count }} permissions</small>
                            </td>
                            <td>
                                @php
                                    $isSystemRole = in_array($role->name, ['Super Administrator', 'Administrator', 'Inspector', 'Officer', 'Forensic Analyst', 'Court Liaison', 'Viewer']);
                                @endphp
                                <span class="badge {{ $isSystemRole ? 'bg-secondary' : 'bg-primary' }}">
                                    {{ $isSystemRole ? 'System' : 'Custom' }}
                                </span>
                            </td>
                            <td>
                                {{ $role->created_at->format('d/m/Y') }}
                                <br><small class="text-muted">{{ $isSystemRole ? 'System role' : 'Custom role' }}</small>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('roles.show', $role->id) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('roles.edit', $role->id) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit Role
                                        </a></li>
                                        @if(!$isSystemRole)
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#!" onclick="deleteRole({{ $role->id }})">
                                            <i data-feather="trash-2" class="icon-xs me-2"></i>Delete Role
                                        </a></li>
                                        @else
                                        <li><hr class="dropdown-divider"></li>
                                        <li><span class="dropdown-item-text text-muted">Cannot delete system role</span></li>
                                        @endif
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i data-feather="shield" class="icon-lg mb-2"></i>
                                    <p>No roles found.</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse


                    </tbody>
                </table>
            </div>
        </div>

                <!-- Pagination -->
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    Showing <strong>{{ $roles->firstItem() }}</strong> to <strong>{{ $roles->lastItem() }}</strong> of <strong>{{ $roles->total() }}</strong> roles
                </div>
                <nav aria-label="User pagination">
                    {{ $roles->links('pagination::bootstrap-4') }}
                </nav>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();

    // Select All functionality
    document.getElementById('selectAllRoles').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.role-checkbox:not([disabled])');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
});

function exportRoles(format) {
    alert(`Exporting roles to ${format.toUpperCase()} format...`);
}

function filterRoles(type) {
    // Remove active class from all buttons
    document.querySelectorAll('[onclick^="filterRoles"]').forEach(btn => {
        btn.classList.remove('active');
    });

    // Add active class to clicked button
    event.target.classList.add('active');

    // In a real application, this would filter the table
    alert(`Filtering roles by: ${type}`);
}

function bulkRoleAction(action) {
    const selectedRoles = document.querySelectorAll('.role-checkbox:checked:not([disabled])');
    if (selectedRoles.length === 0) {
        alert('Please select roles to perform bulk action.');
        return;
    }

    const actionText = action.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
    if (confirm(`Are you sure you want to ${actionText} ${selectedRoles.length} selected role(s)?`)) {
        alert(`${actionText} action performed on ${selectedRoles.length} role(s).`);
    }
}

function deleteRole(roleId) {
    if (confirm('Are you sure you want to delete this role? This action cannot be undone.')) {
        // In a real application, this would submit a DELETE request
        alert(`Role ${roleId} would be deleted.`);
    }
}
</script>
@endpush
