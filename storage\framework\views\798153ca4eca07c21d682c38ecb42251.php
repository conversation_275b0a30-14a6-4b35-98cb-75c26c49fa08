<?php $__env->startSection('title', 'Court Verdicts - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Branded Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <div class="icon-shape icon-lg bg-white bg-opacity-20 text-white rounded-3">
                                <i data-feather="award" class="icon-md"></i>
                            </div>
                        </div>
                        <div>
                            <h1 class="h2 mb-1">Court Verdicts</h1>
                            <p class="mb-0 opacity-75">View and manage court verdicts and sentences</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex flex-wrap gap-2 justify-content-md-end">
                        <button type="button" class="btn btn-light" data-bs-toggle="collapse" data-bs-target="#searchCollapse">
                            <i data-feather="search" class="icon-xs me-2"></i>
                            Search Verdicts
                        </button>
                        <button type="button" class="btn btn-outline-light">
                            <i data-feather="download" class="icon-xs me-2"></i>
                            Export Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="collapse mb-4" id="searchCollapse">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('court.verdicts')); ?>">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search Verdicts</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo e(request('search')); ?>" placeholder="Case number, title, or defendant">
                        </div>
                        <div class="col-md-3">
                            <label for="verdict_filter" class="form-label">Verdict</label>
                            <select class="form-select" id="verdict_filter" name="verdict_filter">
                                <option value="">All Verdicts</option>
                                <option value="Guilty" <?php echo e(request('verdict_filter') === 'Guilty' ? 'selected' : ''); ?>>Guilty</option>
                                <option value="Not Guilty" <?php echo e(request('verdict_filter') === 'Not Guilty' ? 'selected' : ''); ?>>Not Guilty</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <div class="d-flex gap-2 w-100">
                                <button type="submit" class="btn btn-primary">
                                    <i data-feather="search" class="icon-xs me-2"></i>
                                    Search
                                </button>
                                <a href="<?php echo e(route('court.verdicts')); ?>" class="btn btn-outline-secondary">
                                    <i data-feather="x" class="icon-xs me-2"></i>
                                    Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="icon-shape icon-md bg-danger text-white rounded-circle mx-auto mb-3">
                        <i data-feather="x-circle" class="icon-sm"></i>
                    </div>
                    <h4 class="text-danger"><?php echo e($verdicts->where('verdict', 'Guilty')->count()); ?></h4>
                    <p class="mb-0">Guilty Verdicts</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="icon-shape icon-md bg-success text-white rounded-circle mx-auto mb-3">
                        <i data-feather="check-circle" class="icon-sm"></i>
                    </div>
                    <h4 class="text-success"><?php echo e($verdicts->where('verdict', 'Not Guilty')->count()); ?></h4>
                    <p class="mb-0">Not Guilty</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="icon-shape icon-md bg-warning text-white rounded-circle mx-auto mb-3">
                        <i data-feather="clock" class="icon-sm"></i>
                    </div>
                    <h4 class="text-warning"><?php echo e($verdicts->where('verdict_date', '>=', now()->subDays(30))->count()); ?></h4>
                    <p class="mb-0">This Month</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="icon-shape icon-md bg-info text-white rounded-circle mx-auto mb-3">
                        <i data-feather="file-text" class="icon-sm"></i>
                    </div>
                    <h4 class="text-info"><?php echo e($verdicts->count()); ?></h4>
                    <p class="mb-0">Total Verdicts</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Verdicts Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i data-feather="list" class="icon-sm me-2"></i>
                Court Verdicts
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Case Number</th>
                            <th>Defendant</th>
                            <th>Charges</th>
                            <th>Verdict</th>
                            <th>Sentence</th>
                            <th>Verdict Date</th>
                            <th>Judge</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $verdicts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $verdict): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <strong><?php echo e($verdict['case_number']); ?></strong>
                                    <small class="text-muted d-block"><?php echo e($verdict['case_title']); ?></small>
                                </td>
                                <td><?php echo e($verdict['defendant']); ?></td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo e($verdict['charges']); ?></span>
                                </td>
                                <td>
                                    <?php if($verdict['verdict'] === 'Guilty'): ?>
                                        <span class="badge bg-danger">
                                            <i data-feather="x-circle" class="icon-xs me-1"></i>
                                            Guilty
                                        </span>
                                    <?php elseif($verdict['verdict'] === 'Not Guilty'): ?>
                                        <span class="badge bg-success">
                                            <i data-feather="check-circle" class="icon-xs me-1"></i>
                                            Not Guilty
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary"><?php echo e($verdict['verdict']); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($verdict['sentence'] === 'Acquitted'): ?>
                                        <span class="text-success fw-bold"><?php echo e($verdict['sentence']); ?></span>
                                    <?php else: ?>
                                        <span class="text-danger"><?php echo e($verdict['sentence']); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo e($verdict['verdict_date']->format('d/m/Y')); ?>

                                    <small class="text-muted d-block"><?php echo e($verdict['verdict_date']->diffForHumans()); ?></small>
                                </td>
                                <td><?php echo e($verdict['judge']); ?></td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i data-feather="more-horizontal" class="icon-xs"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#!" onclick="viewVerdictDetails(<?php echo e($verdict['id']); ?>)">
                                                <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                            </a></li>
                                            <li><a class="dropdown-item" href="#!">
                                                <i data-feather="file-text" class="icon-xs me-2"></i>Generate Certificate
                                            </a></li>
                                            <li><a class="dropdown-item" href="#!">
                                                <i data-feather="download" class="icon-xs me-2"></i>Download Verdict
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="#!">
                                                <i data-feather="share" class="icon-xs me-2"></i>Share Verdict
                                            </a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-muted">
                                        <i data-feather="search" class="icon-lg mb-2"></i>
                                        <p class="mb-0">No verdicts found matching your search criteria.</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Recent Verdicts Summary -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="trending-up" class="icon-sm me-2"></i>
                        Recent Verdicts Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Verdict Distribution (Last 30 Days)</h6>
                            <div class="progress mb-3" style="height: 20px;">
                                <?php
                                    $recentVerdicts = $verdicts->where('verdict_date', '>=', now()->subDays(30));
                                    $guiltyCount = $recentVerdicts->where('verdict', 'Guilty')->count();
                                    $notGuiltyCount = $recentVerdicts->where('verdict', 'Not Guilty')->count();
                                    $total = $recentVerdicts->count();
                                    $guiltyPercent = $total > 0 ? ($guiltyCount / $total) * 100 : 0;
                                    $notGuiltyPercent = $total > 0 ? ($notGuiltyCount / $total) * 100 : 0;
                                ?>
                                <div class="progress-bar bg-danger" style="width: <?php echo e($guiltyPercent); ?>%">
                                    Guilty (<?php echo e($guiltyCount); ?>)
                                </div>
                                <div class="progress-bar bg-success" style="width: <?php echo e($notGuiltyPercent); ?>%">
                                    Not Guilty (<?php echo e($notGuiltyCount); ?>)
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Most Common Charges</h6>
                            <div class="list-group list-group-flush">
                                <?php
                                    $chargesCounts = $verdicts->groupBy('charges')->map->count()->sortDesc()->take(3);
                                ?>
                                <?php $__currentLoopData = $chargesCounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $charge => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="list-group-item px-0 py-2">
                                        <div class="d-flex justify-content-between">
                                            <span><?php echo e($charge); ?></span>
                                            <span class="badge bg-primary"><?php echo e($count); ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="calendar" class="icon-sm me-2"></i>
                        Quick Stats
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span>Conviction Rate</span>
                            <strong class="text-danger"><?php echo e($total > 0 ? round($guiltyPercent, 1) : 0); ?>%</strong>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-danger" style="width: <?php echo e($guiltyPercent); ?>%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span>Acquittal Rate</span>
                            <strong class="text-success"><?php echo e($total > 0 ? round($notGuiltyPercent, 1) : 0); ?>%</strong>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: <?php echo e($notGuiltyPercent); ?>%"></div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <h4 class="text-primary"><?php echo e($verdicts->where('verdict_date', '>=', now()->subDays(7))->count()); ?></h4>
                        <p class="mb-0 text-muted">Verdicts This Week</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Verdict Details Modal -->
    <div class="modal fade" id="verdictDetailsModal" tabindex="-1" aria-labelledby="verdictDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="verdictDetailsModalLabel">
                        <i data-feather="file-text" class="icon-sm me-2"></i>
                        Verdict Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="verdictDetailsContent">
                    <!-- Content will be loaded dynamically -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Download Verdict</button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});

function viewVerdictDetails(verdictId) {
    // Show modal with verdict details
    const modal = new bootstrap.Modal(document.getElementById('verdictDetailsModal'));
    
    // Load verdict details (this would typically be an AJAX call)
    document.getElementById('verdictDetailsContent').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading verdict details...</p>
        </div>
    `;
    
    modal.show();
    
    // Simulate loading (replace with actual AJAX call)
    setTimeout(() => {
        document.getElementById('verdictDetailsContent').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Case Information</h6>
                    <p><strong>Case Number:</strong> CR/2023/045</p>
                    <p><strong>Case Title:</strong> State vs. James Phiri</p>
                    <p><strong>Defendant:</strong> James Phiri</p>
                    <p><strong>Charges:</strong> Armed Robbery</p>
                </div>
                <div class="col-md-6">
                    <h6>Verdict Information</h6>
                    <p><strong>Verdict:</strong> <span class="badge bg-danger">Guilty</span></p>
                    <p><strong>Sentence:</strong> 10 years imprisonment</p>
                    <p><strong>Judge:</strong> Hon. Justice M. Phiri</p>
                    <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
                </div>
            </div>
            <hr>
            <h6>Verdict Summary</h6>
            <p>After careful consideration of all evidence presented, the court finds the defendant guilty of armed robbery. The sentence of 10 years imprisonment reflects the serious nature of the crime and serves as a deterrent to others.</p>
        `;
    }, 1000);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/court/verdicts.blade.php ENDPATH**/ ?>