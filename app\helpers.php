<?php

if (!function_exists('app_initials')) {
    /**
     * Get the initials of the application name
     *
     * @return string
     */
    function app_initials()
    {
        $name = config('app.name');
        $words = explode(' ', $name);
        $initials = '';
        
        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper(substr($word, 0, 1));
            }
        }
        
        return $initials ?: 'APP';
    }
}
