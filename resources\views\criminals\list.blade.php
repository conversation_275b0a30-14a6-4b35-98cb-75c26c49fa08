@extends('layouts.app')

@section('title', 'Criminal List  - ' . config('app.name'))

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Criminal List</h1>
            <x-breadcrumb :items="[
                ['title' => 'Criminal Profiles'],
                ['title' => 'Criminal List']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('criminals.create') }}" class="btn btn-primary">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Add New Criminal
            </a>
        </div>
    </div>

    <!-- Criminal Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">1,245</h4>
                    <p class="mb-0">Total Criminals</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-danger">89</h4>
                    <p class="mb-0">Wanted Criminals</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning">156</h4>
                    <p class="mb-0">High Risk</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-success">1,000</h4>
                    <p class="mb-0">Active Profiles</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('criminals.list') }}">
                <div class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" 
                               placeholder="Search by name, criminal number, or alias"
                               value="{{ request('search') }}">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="risk_level">
                            <option value="">All Risk Levels</option>
                            <option value="Low" {{ request('risk_level') == 'Low' ? 'selected' : '' }}>Low</option>
                            <option value="Medium" {{ request('risk_level') == 'Medium' ? 'selected' : '' }}>Medium</option>
                            <option value="High" {{ request('risk_level') == 'High' ? 'selected' : '' }}>High</option>
                            <option value="Critical" {{ request('risk_level') == 'Critical' ? 'selected' : '' }}>Critical</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">All Status</option>
                            <option value="Active" {{ request('status') == 'Active' ? 'selected' : '' }}>Active</option>
                            <option value="Inactive" {{ request('status') == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                            <option value="Deceased" {{ request('status') == 'Deceased' ? 'selected' : '' }}>Deceased</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="district">
                            <option value="">All Districts</option>
                            <option value="Lilongwe" {{ request('district') == 'Lilongwe' ? 'selected' : '' }}>Lilongwe</option>
                            <option value="Blantyre" {{ request('district') == 'Blantyre' ? 'selected' : '' }}>Blantyre</option>
                            <option value="Mzuzu" {{ request('district') == 'Mzuzu' ? 'selected' : '' }}>Mzuzu</option>
                            <option value="Zomba" {{ request('district') == 'Zomba' ? 'selected' : '' }}>Zomba</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i data-feather="search" class="icon-xs me-2"></i>
                            Search
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Criminal List -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Criminal Profiles</h4>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-secondary">
                    <i data-feather="download" class="icon-xs me-2"></i>
                    Export
                </button>
                <button class="btn btn-sm btn-outline-primary">
                    <i data-feather="filter" class="icon-xs me-2"></i>
                    Advanced Filter
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Criminal Number</th>
                            <th>Name</th>
                            <th>Age</th>
                            <th>District</th>
                            <th>Risk Level</th>
                            <th>Status</th>
                            <th>Last Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="table-danger">
                            <td><strong>CRM20240001</strong></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <div class="avatar avatar-sm bg-danger text-white">CP</div>
                                    </div>
                                    <div>
                                        <strong>Chisomo Phiri</strong>
                                        <br><small class="text-muted">Male, 39 years</small>
                                        @if(true) {{-- is_wanted --}}
                                            <span class="badge bg-danger ms-2">WANTED</span>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>39</td>
                            <td>Nkhotakota</td>
                            <td><span class="badge bg-danger">High</span></td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>15/12/2024</td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('criminals.show', 1) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('criminals.edit', 1) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="file-plus" class="icon-xs me-2"></i>Add Case
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="download" class="icon-xs me-2"></i>Export Profile
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>CRM20240002</strong></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <div class="avatar avatar-sm bg-primary text-white">KB</div>
                                    </div>
                                    <div>
                                        <strong>Kondwani Banda</strong>
                                        <br><small class="text-muted">Male, 34 years</small>
                                    </div>
                                </div>
                            </td>
                            <td>34</td>
                            <td>Lilongwe</td>
                            <td><span class="badge bg-info">Medium</span></td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>12/12/2024</td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('criminals.show', 2) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('criminals.edit', 2) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="file-plus" class="icon-xs me-2"></i>Add Case
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="download" class="icon-xs me-2"></i>Export Profile
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>CRM20240003</strong></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <div class="avatar avatar-sm bg-success text-white">CM</div>
                                    </div>
                                    <div>
                                        <strong>Chikondi Mwale</strong>
                                        <br><small class="text-muted">Female, 36 years</small>
                                        <span class="badge bg-info ms-2">Chi</span> {{-- alias --}}
                                    </div>
                                </div>
                            </td>
                            <td>36</td>
                            <td>Blantyre</td>
                            <td><span class="badge bg-success">Low</span></td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>10/12/2024</td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('criminals.show', 3) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('criminals.edit', 3) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="file-plus" class="icon-xs me-2"></i>Add Case
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="download" class="icon-xs me-2"></i>Export Profile
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr class="table-warning">
                            <td><strong>CRM20240004</strong></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <div class="avatar avatar-sm bg-warning text-dark">TP</div>
                                    </div>
                                    <div>
                                        <strong>Thokozani Phiri</strong>
                                        <br><small class="text-muted">Male, 42 years</small>
                                        @if(true) {{-- is_wanted --}}
                                            <span class="badge bg-danger ms-2">WANTED</span>
                                        @endif
                                        @if(true) {{-- is_repeat_offender --}}
                                            <span class="badge bg-warning ms-1">REPEAT</span>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>42</td>
                            <td>Mzuzu</td>
                            <td><span class="badge bg-danger">Critical</span></td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>08/12/2024</td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('criminals.show', 4) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('criminals.edit', 4) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="file-plus" class="icon-xs me-2"></i>Add Case
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-warning" href="#!">
                                            <i data-feather="alert-triangle" class="icon-xs me-2"></i>Mark as Dangerous
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>CRM20240005</strong></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <div class="avatar avatar-sm bg-info text-white">MK</div>
                                    </div>
                                    <div>
                                        <strong>Mwawi Kumwenda</strong>
                                        <br><small class="text-muted">Female, 29 years</small>
                                    </div>
                                </div>
                            </td>
                            <td>29</td>
                            <td>Zomba</td>
                            <td><span class="badge bg-success">Low</span></td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>05/12/2024</td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('criminals.show', 5) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('criminals.edit', 5) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="file-plus" class="icon-xs me-2"></i>Add Case
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="download" class="icon-xs me-2"></i>Export Profile
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Showing 1 to 5 of 1,245 criminals
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">Previous</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Next</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
@endpush
