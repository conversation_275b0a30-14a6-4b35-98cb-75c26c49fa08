<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Criminal>
 */
class CriminalFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $firstName = $this->faker->firstName();
        $lastName = $this->faker->lastName();
        $criminalNumber = 'CR-' . date('Y') . '-' . str_pad($this->faker->unique()->numberBetween(1, 9999), 4, '0', STR_PAD_LEFT);

        return [
            'criminal_number' => $criminalNumber,
            'first_name' => $firstName,
            'middle_name' => $this->faker->optional(0.6)->firstName(),
            'last_name' => $lastName,
            'alias' => $this->faker->optional(0.3)->userName(),
            'gender' => $this->faker->randomElement(['Male', 'Female']),
            'date_of_birth' => $this->faker->dateTimeBetween('-60 years', '-18 years'),
            'place_of_birth' => $this->faker->randomElement([
                'Lilongwe', 'Blantyre', 'Mzuzu', 'Zomba', 'Kasungu', 'Mangochi', 'Salima', 'Balaka'
            ]),
            'nationality' => 'Malawian',
            'national_id' => $this->faker->optional(0.8)->regexify('[A-Z]{2}[0-9]{8}'),
            'height' => $this->faker->randomFloat(2, 1.50, 2.00),
            'weight' => $this->faker->randomFloat(1, 45.0, 120.0),
            'eye_color' => $this->faker->randomElement(['Brown', 'Black', 'Hazel', 'Green', 'Blue']),
            'hair_color' => $this->faker->randomElement(['Black', 'Brown', 'Grey', 'Bald']),
            'complexion' => $this->faker->randomElement(['Light', 'Medium', 'Dark']),
            'distinguishing_marks' => $this->faker->optional(0.4)->sentence(),
            'scars_tattoos' => $this->faker->optional(0.3)->sentence(),
            'phone_number' => $this->faker->optional(0.7)->regexify('(088|099|077)[0-9]{7}'),
            'email' => $this->faker->optional(0.3)->email(),
            'current_address' => $this->faker->address(),
            'permanent_address' => $this->faker->optional(0.8)->address(),
            'district' => $this->faker->randomElement([
                'Lilongwe', 'Blantyre', 'Mzuzu', 'Zomba', 'Kasungu', 'Mangochi',
                'Salima', 'Balaka', 'Chiradzulu', 'Thyolo', 'Nsanje', 'Chikwawa',
                'Ntcheu', 'Dedza', 'Dowa', 'Mchinji', 'Nkhotakota', 'Karonga'
            ]),
            'traditional_authority' => $this->faker->optional(0.7)->lastName(),
            'village' => $this->faker->optional(0.7)->city(),
            'emergency_contact_name' => $this->faker->optional(0.6)->name(),
            'emergency_contact_phone' => $this->faker->optional(0.6)->regexify('(088|099|077)[0-9]{7}'),
            'emergency_contact_relationship' => $this->faker->optional(0.6)->randomElement([
                'Mother', 'Father', 'Spouse', 'Sibling', 'Child', 'Friend', 'Relative'
            ]),
            'status' => $this->faker->randomElement(['Active', 'Inactive', 'Deceased', 'Deported']),
            'risk_level' => $this->faker->randomElement(['Low', 'Medium', 'High', 'Critical']),
            'is_wanted' => $this->faker->boolean(20), // 20% chance of being wanted
            'is_repeat_offender' => $this->faker->boolean(30), // 30% chance of being repeat offender
            'occupation' => $this->faker->optional(0.7)->jobTitle(),
            'education_level' => $this->faker->optional(0.8)->randomElement([
                'Primary', 'Secondary', 'Tertiary', 'University', 'None'
            ]),
            'marital_status' => $this->faker->optional(0.8)->randomElement([
                'Single', 'Married', 'Divorced', 'Widowed'
            ]),
            'known_associates' => $this->faker->optional(0.4)->sentence(),
            'notes' => $this->faker->optional(0.5)->paragraph(),
        ];
    }
}
