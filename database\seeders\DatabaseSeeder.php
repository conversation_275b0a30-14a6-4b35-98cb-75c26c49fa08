<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed roles and permissions first
        $this->call([
            RolePermissionSeeder::class,
        ]);

        // Create test users with roles
        $this->createTestUsers();

        // Seed sample data
        $this->call([
            CriminalSeeder::class,
        ]);

        // Create test users with roles
        $this->createTestUsers();
    }

    /**
     * Create test users for different roles
     */
    private function createTestUsers(): void
    {
        // Create Super Administrator
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Administrator',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$superAdmin->hasRole('Super Administrator')) {
            $superAdmin->assignRole('Super Administrator');
        }

        // Create System Administrator
        $systemAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$systemAdmin->hasRole('System Administrator')) {
            $systemAdmin->assignRole('System Administrator');
        }

        // Create Police Chief
        $policeChief = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Chief Inspector Kondwani Banda',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$policeChief->hasRole('Police Chief')) {
            $policeChief->assignRole('Police Chief');
        }

        // Create Detective Supervisor
        $detectiveSupervisor = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Detective Supervisor Chikondi Mwale',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$detectiveSupervisor->hasRole('Detective Supervisor')) {
            $detectiveSupervisor->assignRole('Detective Supervisor');
        }

        // Create Detective
        $detective = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Detective Thokozani Phiri',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$detective->hasRole('Detective')) {
            $detective->assignRole('Detective');
        }

        // Create Police Officer
        $policeOfficer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Officer Chisomo Nyirenda',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$policeOfficer->hasRole('Police Officer')) {
            $policeOfficer->assignRole('Police Officer');
        }

        // Create Evidence Custodian
        $evidenceCustodian = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Evidence Custodian Mphatso Kachingwe',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$evidenceCustodian->hasRole('Evidence Custodian')) {
            $evidenceCustodian->assignRole('Evidence Custodian');
        }

        // Create Court Clerk
        $courtClerk = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Court Clerk Tamara Mvula',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$courtClerk->hasRole('Court Clerk')) {
            $courtClerk->assignRole('Court Clerk');
        }

        // Create Test User (for testing purposes)
        $testUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$testUser->hasRole('Police Officer')) {
            $testUser->assignRole('Police Officer');
        }
    }
}
