<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleController extends Controller
{
    /**
     * Display a listing of all roles.
     */
    public function index()
    {
        // Get roles with user counts and permission counts using <PERSON><PERSON>'s default pagination
        $roles = Role::orderBy('created_at', 'asc')
            ->paginate(10);

        return view('roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new role.
     */
    public function create()
    {
        return view('roles.create');
    }

    /**
     * Store a newly created role in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'required|array',
        ]);

        return redirect()->route('roles.index')->with('success', 'Role created successfully!');
    }

    /**
     * Display the specified role.
     */
    public function show(string $id)
    {
        return view('roles.show', compact('id'));
    }

    /**
     * Show the form for editing the specified role.
     */
    public function edit(string $id)
    {
        return view('roles.edit', compact('id'));
    }

    /**
     * Update the specified role in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $id,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'required|array',
        ]);

        return redirect()->route('roles.index')->with('success', 'Role updated successfully!');
    }

    /**
     * Remove the specified role from storage.
     */
    public function destroy(string $id)
    {
        return redirect()->route('roles.index')->with('success', 'Role deleted successfully!');
    }

    /**
     * Display permissions management.
     */
    public function permissions()
    {
        return view('roles.permissions');
    }

    /**
     * Display role assignments.
     */
    public function assignments()
    {
        return view('roles.assignments');
    }

    /**
     * Assign role to user.
     */
    public function assignRole(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'role_id' => 'required|exists:roles,id',
        ]);

        return redirect()->back()->with('success', 'Role assigned successfully!');
    }

    /**
     * Remove role from user.
     */
    public function removeRole(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'role_id' => 'required|exists:roles,id',
        ]);

        return redirect()->back()->with('success', 'Role removed successfully!');
    }
}
