<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class RoleController extends Controller
{
    /**
     * Display a listing of all roles.
     */
    public function index()
    {
        // For now, we'll create mock data with pagination
        // In a real application, this would be: Role::paginate(10)
        $roles = collect([
            (object)[
                'id' => 1,
                'name' => 'super_admin',
                'display_name' => 'Super Administrator',
                'description' => 'System administrator with full access',
                'users_count' => 2,
                'permissions_count' => 25,
                'type' => 'system',
                'created_at' => '2024-01-01',
            ],
            (object)[
                'id' => 2,
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'Administrator with management access',
                'users_count' => 5,
                'permissions_count' => 18,
                'type' => 'system',
                'created_at' => '2024-01-01',
            ],
            (object)[
                'id' => 3,
                'name' => 'inspector',
                'display_name' => 'Inspector',
                'description' => 'Police inspector with case management access',
                'users_count' => 12,
                'permissions_count' => 15,
                'type' => 'system',
                'created_at' => '2024-01-01',
            ],
            (object)[
                'id' => 4,
                'name' => 'officer',
                'display_name' => 'Officer',
                'description' => 'Police officer with basic access',
                'users_count' => 18,
                'permissions_count' => 10,
                'type' => 'system',
                'created_at' => '2024-01-01',
            ],
            (object)[
                'id' => 5,
                'name' => 'forensic_analyst',
                'display_name' => 'Forensic Analyst',
                'description' => 'Forensic analyst with evidence access',
                'users_count' => 6,
                'permissions_count' => 8,
                'type' => 'system',
                'created_at' => '2024-01-01',
            ],
            (object)[
                'id' => 6,
                'name' => 'court_liaison',
                'display_name' => 'Court Liaison',
                'description' => 'Court liaison with court case access',
                'users_count' => 3,
                'permissions_count' => 6,
                'type' => 'system',
                'created_at' => '2024-01-01',
            ],
            (object)[
                'id' => 7,
                'name' => 'viewer',
                'display_name' => 'Viewer',
                'description' => 'Read-only access to system data',
                'users_count' => 8,
                'permissions_count' => 4,
                'type' => 'system',
                'created_at' => '2024-01-01',
            ],
            (object)[
                'id' => 8,
                'name' => 'detective',
                'display_name' => 'Detective',
                'description' => 'Detective with investigation access',
                'users_count' => 4,
                'permissions_count' => 12,
                'type' => 'custom',
                'created_at' => '2024-06-15',
            ],
        ]);

        // Create a manual paginator for demonstration
        $currentPage = request()->get('page', 1);
        $perPage = 5;
        $total = $roles->count();
        $items = $roles->forPage($currentPage, $perPage);

        $roles = new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );

        return view('roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new role.
     */
    public function create()
    {
        return view('roles.create');
    }

    /**
     * Store a newly created role in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'required|array',
        ]);

        return redirect()->route('roles.index')->with('success', 'Role created successfully!');
    }

    /**
     * Display the specified role.
     */
    public function show(string $id)
    {
        return view('roles.show', compact('id'));
    }

    /**
     * Show the form for editing the specified role.
     */
    public function edit(string $id)
    {
        return view('roles.edit', compact('id'));
    }

    /**
     * Update the specified role in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $id,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'required|array',
        ]);

        return redirect()->route('roles.index')->with('success', 'Role updated successfully!');
    }

    /**
     * Remove the specified role from storage.
     */
    public function destroy(string $id)
    {
        return redirect()->route('roles.index')->with('success', 'Role deleted successfully!');
    }

    /**
     * Display permissions management.
     */
    public function permissions()
    {
        return view('roles.permissions');
    }

    /**
     * Display role assignments.
     */
    public function assignments()
    {
        return view('roles.assignments');
    }

    /**
     * Assign role to user.
     */
    public function assignRole(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'role_id' => 'required|exists:roles,id',
        ]);

        return redirect()->back()->with('success', 'Role assigned successfully!');
    }

    /**
     * Remove role from user.
     */
    public function removeRole(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'role_id' => 'required|exists:roles,id',
        ]);

        return redirect()->back()->with('success', 'Role removed successfully!');
    }
}
