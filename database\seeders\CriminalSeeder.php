<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CriminalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample criminals with Malawian names and data according to documentation

        // High-profile wanted criminal - <PERSON><PERSON><PERSON> from documentation
        \App\Models\Criminal::create([
            'criminal_number' => 'CRM20240001',
            'first_name' => 'Chisomo',
            'last_name' => 'Phiri',
            'gender' => 'Male',
            'date_of_birth' => '1985-03-15',
            'place_of_birth' => 'Nkhotakota',
            'nationality' => 'Malawian',
            'height' => 1.75,
            'weight' => 70,
            'eye_color' => 'Brown',
            'hair_color' => 'Black',
            'complexion' => 'Dark',
            'phone_number' => '+265997626306',
            'current_address' => 'P.O. Box 456, Blantyre',
            'permanent_address' => 'Makuta Village, T/A Malengachanzi',
            'district' => 'Nkhotakota',
            'traditional_authority' => 'Malengachanzi',
            'village' => 'Makuta',
            'occupation' => 'Driver',
            'risk_level' => 'High',
            'is_wanted' => true,
            'is_repeat_offender' => true,
            'status' => 'Active',
            'known_associates' => 'Known to associate with local gang members',
            'notes' => 'Approach with caution - history of violent behavior',
            'created_by' => 1,
            'updated_by' => 1,
        ]);

        // Kondwani Banda - Officer name from documentation
        \App\Models\Criminal::create([
            'criminal_number' => 'CRM20240002',
            'first_name' => 'Kondwani',
            'last_name' => 'Banda',
            'gender' => 'Male',
            'date_of_birth' => '1990-07-22',
            'nationality' => 'Malawian',
            'height' => 1.68,
            'weight' => 65,
            'eye_color' => 'Brown',
            'hair_color' => 'Black',
            'phone_number' => '+265999123456',
            'current_address' => '123 Kamuzu Procession Road, Lilongwe',
            'district' => 'Lilongwe',
            'occupation' => 'Mechanic',
            'risk_level' => 'Medium',
            'is_wanted' => false,
            'is_repeat_offender' => false,
            'status' => 'Active',
            'created_by' => 1,
            'updated_by' => 1,
        ]);

        // Chikondi Mwale - Female criminal
        \App\Models\Criminal::create([
            'criminal_number' => 'CRM20240003',
            'first_name' => 'Chikondi',
            'last_name' => 'Mwale',
            'alias' => 'Chi',
            'gender' => 'Female',
            'date_of_birth' => '1988-11-10',
            'nationality' => 'Malawian',
            'height' => 1.62,
            'weight' => 58,
            'eye_color' => 'Brown',
            'hair_color' => 'Black',
            'phone_number' => '+265888901234',
            'current_address' => 'PO Box 456, Blantyre',
            'district' => 'Blantyre',
            'occupation' => 'Trader',
            'education_level' => 'Secondary',
            'marital_status' => 'Single',
            'risk_level' => 'Low',
            'is_wanted' => false,
            'is_repeat_offender' => false,
            'status' => 'Active',
            'created_by' => 1,
            'updated_by' => 1,
        ]);

        // Thokozani Phiri - Critical risk wanted criminal
        \App\Models\Criminal::create([
            'criminal_number' => 'CRM20240004',
            'first_name' => 'Thokozani',
            'last_name' => 'Phiri',
            'gender' => 'Male',
            'date_of_birth' => '1982-05-18',
            'nationality' => 'Malawian',
            'height' => 1.80,
            'weight' => 85,
            'eye_color' => 'Brown',
            'hair_color' => 'Black',
            'distinguishing_marks' => 'Scar on left cheek',
            'scars_tattoos' => 'Tattoo of eagle on right arm',
            'phone_number' => '+265777111222',
            'current_address' => '456 Paul Kagame Road, Mzuzu',
            'district' => 'Mzuzu',
            'occupation' => 'Unemployed',
            'risk_level' => 'Critical',
            'is_wanted' => true,
            'is_repeat_offender' => true,
            'status' => 'Active',
            'known_associates' => 'Leader of local criminal gang',
            'notes' => 'EXTREMELY DANGEROUS - Armed and dangerous, do not approach alone',
            'created_by' => 1,
            'updated_by' => 1,
        ]);

        // Mwawi Kumwenda - Female with medical conditions
        \App\Models\Criminal::create([
            'criminal_number' => 'CRM20240005',
            'first_name' => 'Mwawi',
            'last_name' => 'Kumwenda',
            'gender' => 'Female',
            'date_of_birth' => '1995-12-03',
            'nationality' => 'Malawian',
            'height' => 1.58,
            'weight' => 52,
            'eye_color' => 'Brown',
            'hair_color' => 'Black',
            'phone_number' => '+265666789012',
            'current_address' => 'PO Box 901, Zomba',
            'district' => 'Zomba',
            'occupation' => 'Student',
            'education_level' => 'University',
            'marital_status' => 'Single',
            'emergency_contact_name' => 'Loveness Kumwenda',
            'emergency_contact_phone' => '+265555345678',
            'emergency_contact_relationship' => 'Mother',
            'risk_level' => 'Low',
            'is_wanted' => false,
            'is_repeat_offender' => false,
            'status' => 'Active',
            'notes' => 'Diabetic - requires insulin, allergic to penicillin',
            'created_by' => 1,
            'updated_by' => 1,
        ]);

        // Create additional random criminals using factory
        \App\Models\Criminal::factory(15)->create();
    }
}
