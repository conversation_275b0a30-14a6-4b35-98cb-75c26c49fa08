@extends('layouts.app')

@section('title', 'User Management - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">User Management</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'User Management']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('users.create') }}" class="btn btn-primary">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Add New User
            </a>
        </div>
    </div>

    <!-- User Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">{{ $users->total() }}</h4>
                    <p class="mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    @php
                        $activeCount = \App\Models\User::where('status', 'active')->count();
                    @endphp
                    <h4 class="text-success">{{ $activeCount }}</h4>
                    <p class="mb-0">Active Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    @php
                        $inactiveCount = \App\Models\User::whereIn('status', ['inactive', 'suspended'])->count();
                    @endphp
                    <h4 class="text-warning">{{ $inactiveCount }}</h4>
                    <p class="mb-0">Inactive Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    @php
                        $onlineCount = \App\Models\User::where('is_online', true)->count();
                    @endphp
                    <h4 class="text-info">{{ $onlineCount }}</h4>
                    <p class="mb-0">Online Now</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i data-feather="filter" class="icon-sm me-2"></i>
                    Advanced Filters
                </h5>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i data-feather="chevron-down" class="icon-xs"></i>
                </button>
            </div>
        </div>
        <div class="collapse" id="filterCollapse">
            <div class="card-body">
                <form class="row g-3" method="GET" action="{{ route('users.index') }}">
                    <div class="col-md-4">
                        <label class="form-label">Search</label>
                        <input type="text" class="form-control" name="search"
                               value="{{ request('search') }}"
                               placeholder="Name, email, badge number...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            <option value="suspended" {{ request('status') === 'suspended' ? 'selected' : '' }}>Suspended</option>
                            <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Department</label>
                        <select class="form-select" name="department">
                            <option value="">All Departments</option>
                            @php
                                $departments = \App\Models\User::whereNotNull('department')->distinct()->pluck('department');
                            @endphp
                            @foreach($departments as $department)
                                <option value="{{ $department }}" {{ request('department') === $department ? 'selected' : '' }}>
                                    {{ $department }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i data-feather="search" class="icon-xs me-2"></i>
                                Search
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Users List -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">System Users</h4>
                <div class="d-flex gap-2">
                    <span class="text-muted small">
                        Showing {{ $users->count() }} of {{ $users->total() }} users
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th>
                                <a href="#!" class="text-decoration-none text-dark d-flex align-items-center">
                                    User
                                    <i data-feather="chevron-up" class="icon-xs ms-1"></i>
                                </a>
                            </th>
                            <th>Badge Number</th>
                            <th>Role</th>
                            <th>Department</th>
                            <th>Last Login</th>
                            <th>Status</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input user-checkbox" type="checkbox" value="{{ $user->id }}">
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @php
                                        $initials = collect(explode(' ', $user->name))->map(fn($name) => strtoupper(substr($name, 0, 1)))->take(2)->implode('');
                                        $avatarColor = $user->is_online ? 'bg-success' : 'bg-primary';
                                    @endphp
                                    <div class="avatar avatar-sm {{ $avatarColor }} text-white me-3">{{ $initials }}</div>
                                    <div>
                                        <strong>{{ $user->name }}</strong>
                                        <br><small class="text-muted">{{ $user->email }}</small>
                                        @if($user->is_online)
                                            <span class="badge bg-success ms-2">Online</span>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                @if($user->badge_number)
                                    <code>{{ $user->badge_number }}</code>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                @if($user->roles->isNotEmpty())
                                    @foreach($user->roles as $role)
                                        @php
                                            $roleColors = [
                                                'Super Administrator' => 'bg-danger',
                                                'Administrator' => 'bg-warning',
                                                'Inspector' => 'bg-primary',
                                                'Officer' => 'bg-success',
                                                'Forensic Analyst' => 'bg-info',
                                                'Court Liaison' => 'bg-secondary',
                                                'Viewer' => 'bg-light text-dark'
                                            ];
                                            $roleColor = $roleColors[$role->name] ?? 'bg-secondary';
                                        @endphp
                                        <span class="badge {{ $roleColor }}">{{ $role->name }}</span>
                                    @endforeach
                                @else
                                    <span class="badge bg-secondary">No Role</span>
                                @endif
                            </td>
                            <td>{{ $user->department ?? '-' }}</td>
                            <td>
                                @if($user->last_login)
                                    {{ $user->last_login->format('d/m/Y H:i A') }}
                                    <br><small class="text-{{ $user->is_online ? 'success' : 'muted' }}">
                                        {{ $user->is_online ? 'Currently active' : $user->last_login->diffForHumans() }}
                                    </small>
                                @else
                                    <span class="text-muted">Never</span>
                                    <br><small class="text-muted">No login recorded</small>
                                @endif
                            </td>
                            <td>
                                @php
                                    $statusColors = [
                                        'active' => 'bg-success',
                                        'inactive' => 'bg-secondary',
                                        'suspended' => 'bg-danger',
                                        'pending' => 'bg-warning'
                                    ];
                                    $statusColor = $statusColors[$user->status] ?? 'bg-secondary';
                                @endphp
                                <span class="badge {{ $statusColor }}">{{ ucfirst($user->status) }}</span>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('users.show', $user->id) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('users.edit', $user->id) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit User
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#!" onclick="resetPassword({{ $user->id }})">
                                            <i data-feather="key" class="icon-xs me-2"></i>Reset Password
                                        </a></li>
                                        @if($user->status === 'active')
                                        <li><a class="dropdown-item text-warning" href="#!" onclick="deactivateUser({{ $user->id }})">
                                            <i data-feather="user-x" class="icon-xs me-2"></i>Deactivate
                                        </a></li>
                                        @else
                                        <li><a class="dropdown-item text-success" href="#!" onclick="activateUser({{ $user->id }})">
                                            <i data-feather="user-check" class="icon-xs me-2"></i>Activate
                                        </a></li>
                                        @endif
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i data-feather="users" class="icon-lg mb-2"></i>
                                    <p>No users found.</p>
                                    <a href="{{ route('users.create') }}" class="btn btn-primary btn-sm">
                                        <i data-feather="plus" class="icon-xs me-2"></i>
                                        Add First User
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Pagination -->
        @if($users->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    Showing <strong>{{ $users->firstItem() }}</strong> to <strong>{{ $users->lastItem() }}</strong>
                    of <strong>{{ $users->total() }}</strong> users
                </div>
                <!-- Laravel Default Pagination -->
                {{ $users->links() }}
            </div>
        </div>
        @else
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    Showing <strong>{{ $users->count() }}</strong> of <strong>{{ $users->count() }}</strong> users
                </div>
            </div>
        </div>
        @endif
    </div>


@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});

function resetPassword(userId) {
    if (confirm('Are you sure you want to reset this user\'s password?')) {
        alert(`Password reset for user ${userId}. An email will be sent to the user.`);
    }
}

function activateUser(userId) {
    if (confirm('Are you sure you want to activate this user?')) {
        alert(`User ${userId} has been activated.`);
    }
}

function deactivateUser(userId) {
    if (confirm('Are you sure you want to deactivate this user?')) {
        alert(`User ${userId} has been deactivated.`);
    }
}
</script>
@endpush
