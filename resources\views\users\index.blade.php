@extends('layouts.app')

@section('title', 'User Management - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">User Management</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'User Management']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i data-feather="download" class="icon-xs me-2"></i>
                    Export
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#!" onclick="exportUsers('excel')">
                        <i data-feather="file-text" class="icon-xs me-2"></i>Export to Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#!" onclick="exportUsers('pdf')">
                        <i data-feather="file" class="icon-xs me-2"></i>Export to PDF
                    </a></li>
                    <li><a class="dropdown-item" href="#!" onclick="exportUsers('csv')">
                        <i data-feather="database" class="icon-xs me-2"></i>Export to CSV
                    </a></li>
                </ul>
            </div>
            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#importModal">
                <i data-feather="upload" class="icon-xs me-2"></i>
                Import Users
            </button>
            <a href="{{ route('users.create') }}" class="btn btn-primary">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Add New User
            </a>
        </div>
    </div>

    <!-- User Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">156</h4>
                    <p class="mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-success">142</h4>
                    <p class="mb-0">Active Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning">14</h4>
                    <p class="mb-0">Inactive Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-info">89</h4>
                    <p class="mb-0">Online Now</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i data-feather="filter" class="icon-sm me-2"></i>
                    Advanced Filters
                </h5>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i data-feather="chevron-down" class="icon-xs"></i>
                </button>
            </div>
        </div>
        <div class="collapse" id="filterCollapse">
            <div class="card-body">
                <form class="row g-3" id="userFilterForm">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" class="form-control" name="search" placeholder="Name, email, badge number...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Role</label>
                        <select class="form-select" name="role">
                            <option value="">All Roles</option>
                            <option value="super_admin">Super Administrator</option>
                            <option value="admin">Administrator</option>
                            <option value="inspector">Inspector</option>
                            <option value="officer">Officer</option>
                            <option value="forensic_analyst">Forensic Analyst</option>
                            <option value="court_liaison">Court Liaison</option>
                            <option value="viewer">Viewer</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Department</label>
                        <select class="form-select" name="department">
                            <option value="">All Departments</option>
                            <option value="Criminal Investigation">Criminal Investigation</option>
                            <option value="Traffic Police">Traffic Police</option>
                            <option value="Forensics">Forensics</option>
                            <option value="Administration">Administration</option>
                            <option value="Community Policing">Community Policing</option>
                            <option value="Special Operations">Special Operations</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="suspended">Suspended</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Station</label>
                        <select class="form-select" name="station">
                            <option value="">All Stations</option>
                            <option value="Lilongwe Central">Lilongwe Central</option>
                            <option value="Blantyre Central">Blantyre Central</option>
                            <option value="Mzuzu Central">Mzuzu Central</option>
                            <option value="Zomba Central">Zomba Central</option>
                            <option value="Kasungu">Kasungu</option>
                            <option value="Mangochi">Mangochi</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i data-feather="search" class="icon-xs"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Users List -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">System Users</h4>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i data-feather="more-horizontal" class="icon-xs me-2"></i>
                            Bulk Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#!" onclick="bulkAction('activate')">
                                <i data-feather="user-check" class="icon-xs me-2"></i>Activate Selected
                            </a></li>
                            <li><a class="dropdown-item" href="#!" onclick="bulkAction('deactivate')">
                                <i data-feather="user-x" class="icon-xs me-2"></i>Deactivate Selected
                            </a></li>
                            <li><a class="dropdown-item" href="#!" onclick="bulkAction('reset-password')">
                                <i data-feather="key" class="icon-xs me-2"></i>Reset Passwords
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#!" onclick="bulkAction('delete')">
                                <i data-feather="trash-2" class="icon-xs me-2"></i>Delete Selected
                            </a></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-info dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i data-feather="columns" class="icon-xs me-2"></i>
                            Columns
                        </button>
                        <ul class="dropdown-menu">
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2" checked> Name
                            </label></li>
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2" checked> Badge Number
                            </label></li>
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2" checked> Role
                            </label></li>
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2" checked> Department
                            </label></li>
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2"> Phone
                            </label></li>
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2"> Last Login
                            </label></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i data-feather="list" class="icon-xs me-2"></i>
                            Per Page: 25
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#!" onclick="changePerPage(10)">10 per page</a></li>
                            <li><a class="dropdown-item" href="#!" onclick="changePerPage(25)">25 per page</a></li>
                            <li><a class="dropdown-item" href="#!" onclick="changePerPage(50)">50 per page</a></li>
                            <li><a class="dropdown-item" href="#!" onclick="changePerPage(100)">100 per page</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th>
                                <a href="#!" class="text-decoration-none text-dark d-flex align-items-center">
                                    User
                                    <i data-feather="chevron-up" class="icon-xs ms-1"></i>
                                </a>
                            </th>
                            <th>Badge Number</th>
                            <th>Role</th>
                            <th>Department</th>
                            <th>Last Login</th>
                            <th>Status</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input user-checkbox" type="checkbox" value="1">
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-primary text-white me-3">KB</div>
                                    <div>
                                        <strong>Inspector Kondwani Banda</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                        <span class="badge bg-success ms-2">Online</span>
                                    </div>
                                </div>
                            </td>
                            <td><code>PB001</code></td>
                            <td><span class="badge bg-primary">Inspector</span></td>
                            <td>Criminal Investigation</td>
                            <td>
                                15/12/2024 08:30 AM
                                <br><small class="text-success">Currently active</small>
                            </td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('users.show', 1) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('users.edit', 1) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit User
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#!">
                                            <i data-feather="key" class="icon-xs me-2"></i>Reset Password
                                        </a></li>
                                        <li><a class="dropdown-item text-warning" href="#!">
                                            <i data-feather="user-x" class="icon-xs me-2"></i>Deactivate
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <!-- More user rows would be here -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Pagination -->
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    Showing <strong>1</strong> to <strong>25</strong> of <strong>156</strong> users
                </div>
                <nav aria-label="User pagination">
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">
                                <i data-feather="chevron-left" class="icon-xs"></i>
                            </a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item"><a class="page-link" href="#">4</a></li>
                        <li class="page-item"><a class="page-link" href="#">5</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">
                                <i data-feather="chevron-right" class="icon-xs"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importModalLabel">
                        <i data-feather="upload" class="icon-sm me-2"></i>
                        Import Users
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i data-feather="info" class="icon-sm me-2"></i>
                        <strong>Import Format:</strong> Upload an Excel (.xlsx) or CSV file with user data.
                    </div>
                    
                    <form enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="import_file" class="form-label">Select File</label>
                            <input type="file" class="form-control" id="import_file" accept=".xlsx,.xls,.csv">
                            <small class="text-muted">Supported formats: Excel (.xlsx, .xls), CSV (.csv)</small>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="update_existing">
                                <label class="form-check-label" for="update_existing">
                                    Update existing users if email/badge number matches
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_welcome_emails">
                                <label class="form-check-label" for="send_welcome_emails">
                                    Send welcome emails to new users
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <a href="#!" class="btn btn-outline-secondary btn-sm">
                                <i data-feather="download" class="icon-xs me-2"></i>
                                Download Template
                            </a>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="importUsers()">
                        <i data-feather="upload" class="icon-xs me-2"></i>
                        Import Users
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Select All functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
});

function exportUsers(format) {
    // In a real application, this would trigger the export
    alert(`Exporting users to ${format.toUpperCase()} format...`);
}

function importUsers() {
    const fileInput = document.getElementById('import_file');
    if (!fileInput.files.length) {
        alert('Please select a file to import.');
        return;
    }
    
    // In a real application, this would handle the file upload
    alert('Importing users... This may take a few moments.');
    document.getElementById('importModal').querySelector('.btn-close').click();
}

function bulkAction(action) {
    const selectedUsers = document.querySelectorAll('.user-checkbox:checked');
    if (selectedUsers.length === 0) {
        alert('Please select users to perform bulk action.');
        return;
    }
    
    const actionText = action.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
    if (confirm(`Are you sure you want to ${actionText} ${selectedUsers.length} selected user(s)?`)) {
        alert(`${actionText} action performed on ${selectedUsers.length} user(s).`);
    }
}

function changePerPage(count) {
    // In a real application, this would reload the page with new pagination
    alert(`Changing to ${count} users per page...`);
}
</script>
@endpush
