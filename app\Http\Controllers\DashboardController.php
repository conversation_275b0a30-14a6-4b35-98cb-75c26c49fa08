<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the main dashboard
     */
    public function index()
    {
        $statistics = $this->getCaseStatistics();
        $recentActivities = $this->getRecentActivity();
        $notifications = $this->getNotifications();

        return view('dashboard', compact('statistics', 'recentActivities', 'notifications'));
    }

    /**
     * Get case statistics for dashboard cards
     */
    public function getCaseStatistics()
    {
        // Cache statistics for 5 minutes to improve performance
        return Cache::remember('dashboard_statistics', 300, function () {
            return [
                'total_criminals' => $this->getTotalCriminals(),
                'active_cases' => $this->getActiveCases(),
                'evidence_items' => $this->getEvidenceItems(),
                'court_hearings' => $this->getUpcomingCourtHearings(),
                'new_cases_24h' => $this->getNewCases(24),
                'cases_awaiting_investigation' => $this->getCasesAwaitingInvestigation(),
                'cases_under_prosecution' => $this->getCasesUnderProsecution(),
                'closed_cases_30d' => $this->getClosedCases(30),
            ];
        });
    }

    /**
     * Get crime analytics data for charts
     */
    public function getCrimeAnalytics(Request $request)
    {
        $period = $request->get('period', '30days');

        return response()->json([
            'crime_trends' => $this->getCrimeTrends($period),
            'crime_categories' => $this->getCrimeCategories(),
            'crime_by_location' => $this->getCrimeByLocation(),
            'crime_heatmap_data' => $this->getCrimeHeatmapData(),
        ]);
    }

    /**
     * Get recent activity for dashboard feed
     */
    public function getRecentActivity()
    {
        // Mock data for now - will be replaced with actual database queries
        return [
            [
                'type' => 'criminal_added',
                'title' => 'New criminal profile added',
                'description' => 'Kondwani Banda - ID: CRM001234',
                'time' => '2 hours ago',
                'icon' => 'user-plus',
                'color' => 'primary'
            ],
            [
                'type' => 'evidence_submitted',
                'title' => 'Evidence submitted',
                'description' => 'Case #CS2024001 - Physical evidence',
                'time' => '4 hours ago',
                'icon' => 'upload',
                'color' => 'info'
            ],
            [
                'type' => 'court_scheduled',
                'title' => 'Court hearing scheduled',
                'description' => 'Lilongwe Magistrate Court - Tomorrow 9:00 AM',
                'time' => '6 hours ago',
                'icon' => 'calendar',
                'color' => 'success'
            ],
            [
                'type' => 'case_updated',
                'title' => 'Case status updated',
                'description' => 'Case #CS2024002 - Under investigation',
                'time' => '8 hours ago',
                'icon' => 'folder',
                'color' => 'warning'
            ],
        ];
    }

    /**
     * Get notifications for user
     */
    public function getNotifications()
    {
        // Mock data for now - will be replaced with actual notification system
        return [
            [
                'title' => 'New Case Assignment',
                'message' => 'You have been assigned to Case #CS2024003',
                'time' => '1 hour ago',
                'read' => false
            ],
            [
                'title' => 'Court Reminder',
                'message' => 'Court hearing tomorrow at 9:00 AM',
                'time' => '3 hours ago',
                'read' => false
            ],
            [
                'title' => 'Evidence Analysis Complete',
                'message' => 'Forensic analysis for Case #CS2024001 is ready',
                'time' => '5 hours ago',
                'read' => true
            ],
        ];
    }

    // Private helper methods for statistics (mock data for now)

    private function getTotalCriminals()
    {
        // Mock data - replace with actual database query
        return 1234;
    }

    private function getActiveCases()
    {
        // Mock data - replace with actual database query
        return 567;
    }

    private function getEvidenceItems()
    {
        // Mock data - replace with actual database query
        return 89;
    }

    private function getUpcomingCourtHearings()
    {
        // Mock data - replace with actual database query
        return 23;
    }

    private function getNewCases($hours)
    {
        // Mock data - replace with actual database query
        return 12;
    }

    private function getCasesAwaitingInvestigation()
    {
        // Mock data - replace with actual database query
        return 45;
    }

    private function getCasesUnderProsecution()
    {
        // Mock data - replace with actual database query
        return 78;
    }

    private function getClosedCases($days)
    {
        // Mock data - replace with actual database query
        return 156;
    }

    private function getCrimeTrends($period)
    {
        // Mock data for crime trends chart
        $days = $period === '7days' ? 7 : ($period === '30days' ? 30 : 365);
        $trends = [];

        for ($i = $days; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $trends[] = [
                'date' => $date->format('Y-m-d'),
                'crimes' => rand(5, 25),
                'cases' => rand(3, 15),
            ];
        }

        return $trends;
    }

    private function getCrimeCategories()
    {
        // Mock data for crime categories pie chart
        return [
            ['category' => 'Theft', 'count' => 45, 'percentage' => 35],
            ['category' => 'Assault', 'count' => 32, 'percentage' => 25],
            ['category' => 'Fraud', 'count' => 25, 'percentage' => 20],
            ['category' => 'Drug Offenses', 'count' => 15, 'percentage' => 12],
            ['category' => 'Other', 'count' => 10, 'percentage' => 8],
        ];
    }

    private function getCrimeByLocation()
    {
        // Mock data for location-based crime statistics
        return [
            ['location' => 'Lilongwe', 'crimes' => 89],
            ['location' => 'Blantyre', 'crimes' => 67],
            ['location' => 'Mzuzu', 'crimes' => 45],
            ['location' => 'Zomba', 'crimes' => 34],
            ['location' => 'Kasungu', 'crimes' => 23],
        ];
    }

    private function getCrimeHeatmapData()
    {
        // Mock data for crime heatmap (latitude, longitude, intensity)
        return [
            ['lat' => -13.9626, 'lng' => 33.7741, 'intensity' => 0.8], // Lilongwe
            ['lat' => -15.7861, 'lng' => 35.0058, 'intensity' => 0.6], // Blantyre
            ['lat' => -11.4439, 'lng' => 34.0104, 'intensity' => 0.4], // Mzuzu
            ['lat' => -15.3875, 'lng' => 35.3188, 'intensity' => 0.3], // Zomba
            ['lat' => -13.0317, 'lng' => 33.4827, 'intensity' => 0.2], // Kasungu
        ];
    }
}
