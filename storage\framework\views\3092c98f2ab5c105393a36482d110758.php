<?php $__env->startSection('title', 'User Details - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">User Details</h1>
            <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => ['items' => [
                ['title' => 'System'],
                ['title' => 'User Management', 'url' => route('users.index')],
                ['title' => 'User Details']
            ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                ['title' => 'System'],
                ['title' => 'User Management', 'url' => route('users.index')],
                ['title' => 'User Details']
            ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
        </div>
        <div class="d-flex gap-2">
            <a href="<?php echo e(route('users.edit', $id)); ?>" class="btn btn-primary">
                <i data-feather="edit" class="icon-xs me-2"></i>
                Edit User
            </a>
            <a href="<?php echo e(route('users.index')); ?>" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Users
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <!-- User Profile Card -->
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div class="avatar avatar-xl mb-3">
                        <img src="https://via.placeholder.com/150" alt="User Avatar" class="rounded-circle">
                    </div>
                    <h4 class="mb-1">Inspector Kondwani Banda</h4>
                    <p class="text-muted mb-3">Criminal Investigation Department</p>
                    <div class="d-flex justify-content-center gap-2">
                        <span class="badge bg-success">Active</span>
                        <span class="badge bg-primary">Inspector</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm">
                            <i data-feather="key" class="icon-xs me-2"></i>
                            Reset Password
                        </button>
                        <button class="btn btn-outline-warning btn-sm">
                            <i data-feather="user-x" class="icon-xs me-2"></i>
                            Deactivate User
                        </button>
                        <button class="btn btn-outline-info btn-sm">
                            <i data-feather="mail" class="icon-xs me-2"></i>
                            Send Email
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- User Information Tabs -->
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#personal" role="tab">
                                <i data-feather="user" class="icon-xs me-2"></i>
                                Personal Info
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#professional" role="tab">
                                <i data-feather="briefcase" class="icon-xs me-2"></i>
                                Professional
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#permissions" role="tab">
                                <i data-feather="shield" class="icon-xs me-2"></i>
                                Permissions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#activity" role="tab">
                                <i data-feather="activity" class="icon-xs me-2"></i>
                                Activity Log
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- Personal Information Tab -->
                        <div class="tab-pane fade show active" id="personal" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Full Name</label>
                                    <p class="mb-0">Inspector Kondwani Banda</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Email Address</label>
                                    <p class="mb-0"><EMAIL></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Phone Number</label>
                                    <p class="mb-0">+265 999 123 456</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Badge Number</label>
                                    <p class="mb-0">PB001</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Date of Birth</label>
                                    <p class="mb-0">15/03/1985</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Gender</label>
                                    <p class="mb-0">Male</p>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Information Tab -->
                        <div class="tab-pane fade" id="professional" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Rank</label>
                                    <p class="mb-0">Inspector</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Department</label>
                                    <p class="mb-0">Criminal Investigation</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Police Station</label>
                                    <p class="mb-0">Lilongwe Central</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Supervisor</label>
                                    <p class="mb-0">Chief Inspector Mwale</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Employment Date</label>
                                    <p class="mb-0">01/06/2010</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Years of Service</label>
                                    <p class="mb-0">14 years</p>
                                </div>
                            </div>
                        </div>

                        <!-- Permissions Tab -->
                        <div class="tab-pane fade" id="permissions" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Assigned Roles</h6>
                                    <div class="mb-3">
                                        <span class="badge bg-primary me-2">Inspector</span>
                                        <span class="badge bg-info">Case Manager</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>System Access</h6>
                                    <div class="mb-3">
                                        <span class="badge bg-success me-2">Active</span>
                                        <span class="badge bg-warning">2FA Enabled</span>
                                    </div>
                                </div>
                            </div>
                            <h6>Permissions</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>View Cases</li>
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>Create Cases</li>
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>Edit Cases</li>
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>Manage Evidence</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>View Reports</li>
                                        <li><i data-feather="check" class="icon-xs text-success me-2"></i>Generate Reports</li>
                                        <li><i data-feather="x" class="icon-xs text-danger me-2"></i>Manage Users</li>
                                        <li><i data-feather="x" class="icon-xs text-danger me-2"></i>System Settings</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Activity Log Tab -->
                        <div class="tab-pane fade" id="activity" role="tabpanel">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Logged in to system</h6>
                                        <p class="mb-1 text-muted">User logged in from IP: *************</p>
                                        <small class="text-muted">15/12/2024 08:30 AM</small>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Created new case</h6>
                                        <p class="mb-1 text-muted">Case CS2024001 - Theft investigation</p>
                                        <small class="text-muted">15/12/2024 09:15 AM</small>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-info"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Updated evidence</h6>
                                        <p class="mb-1 text-muted">Added new evidence item EV2024001</p>
                                        <small class="text-muted">15/12/2024 11:45 AM</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/users/show.blade.php ENDPATH**/ ?>