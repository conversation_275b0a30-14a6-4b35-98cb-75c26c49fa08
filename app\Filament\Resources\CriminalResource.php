<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CriminalResource\Pages;
use App\Filament\Resources\CriminalResource\RelationManagers;
use App\Models\Criminal;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class CriminalResource extends Resource
{
    protected static ?string $model = Criminal::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationLabel = 'Criminal Profiles';

    protected static ?string $modelLabel = 'Criminal Profile';

    protected static ?string $pluralModelLabel = 'Criminal Profiles';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Criminal Information')
                    ->tabs([
                        Tabs\Tab::make('Basic Information')
                            ->schema([
                                Section::make('Identification')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Forms\Components\TextInput::make('criminal_number')
                                                    ->label('Criminal Number')
                                                    ->required()
                                                    ->unique(ignoreRecord: true)
                                                    ->maxLength(20)
                                                    ->placeholder('CR-2024-001'),
                                                Forms\Components\Select::make('status')
                                                    ->options([
                                                        'Active' => 'Active',
                                                        'Inactive' => 'Inactive',
                                                        'Deceased' => 'Deceased',
                                                        'Deported' => 'Deported',
                                                    ])
                                                    ->default('Active')
                                                    ->required(),
                                            ]),
                                    ]),

                                Section::make('Personal Details')
                                    ->schema([
                                        Grid::make(3)
                                            ->schema([
                                                Forms\Components\TextInput::make('first_name')
                                                    ->label('First Name')
                                                    ->required()
                                                    ->maxLength(100),
                                                Forms\Components\TextInput::make('middle_name')
                                                    ->label('Middle Name')
                                                    ->maxLength(100),
                                                Forms\Components\TextInput::make('last_name')
                                                    ->label('Last Name')
                                                    ->required()
                                                    ->maxLength(100),
                                            ]),

                                        Grid::make(2)
                                            ->schema([
                                                Forms\Components\TextInput::make('alias')
                                                    ->label('Alias/Nickname')
                                                    ->maxLength(100),
                                                Forms\Components\Select::make('gender')
                                                    ->options([
                                                        'Male' => 'Male',
                                                        'Female' => 'Female',
                                                        'Other' => 'Other',
                                                    ])
                                                    ->required(),
                                            ]),

                                        Grid::make(3)
                                            ->schema([
                                                Forms\Components\DatePicker::make('date_of_birth')
                                                    ->label('Date of Birth')
                                                    ->maxDate(now()),
                                                Forms\Components\TextInput::make('place_of_birth')
                                                    ->label('Place of Birth')
                                                    ->maxLength(200),
                                                Forms\Components\TextInput::make('nationality')
                                                    ->label('Nationality')
                                                    ->default('Malawian')
                                                    ->maxLength(100),
                                            ]),

                                        Forms\Components\TextInput::make('national_id')
                                            ->label('National ID Number')
                                            ->unique(ignoreRecord: true)
                                            ->maxLength(20),
                                    ]),
                            ]),

                        Tabs\Tab::make('Physical Description')
                            ->schema([
                                Section::make('Physical Characteristics')
                                    ->schema([
                                        Grid::make(4)
                                            ->schema([
                                                Forms\Components\TextInput::make('height')
                                                    ->label('Height (meters)')
                                                    ->numeric()
                                                    ->step(0.01)
                                                    ->placeholder('1.75'),
                                                Forms\Components\TextInput::make('weight')
                                                    ->label('Weight (kg)')
                                                    ->numeric()
                                                    ->step(0.1)
                                                    ->placeholder('70.5'),
                                                Forms\Components\TextInput::make('eye_color')
                                                    ->label('Eye Color')
                                                    ->maxLength(50),
                                                Forms\Components\TextInput::make('hair_color')
                                                    ->label('Hair Color')
                                                    ->maxLength(50),
                                            ]),

                                        Grid::make(2)
                                            ->schema([
                                                Forms\Components\TextInput::make('complexion')
                                                    ->label('Complexion')
                                                    ->maxLength(50),
                                                Forms\Components\Select::make('risk_level')
                                                    ->label('Risk Level')
                                                    ->options([
                                                        'Low' => 'Low',
                                                        'Medium' => 'Medium',
                                                        'High' => 'High',
                                                        'Critical' => 'Critical',
                                                    ])
                                                    ->default('Low')
                                                    ->required(),
                                            ]),

                                        Forms\Components\Textarea::make('distinguishing_marks')
                                            ->label('Distinguishing Marks')
                                            ->rows(3),

                                        Forms\Components\Textarea::make('scars_tattoos')
                                            ->label('Scars and Tattoos')
                                            ->rows(3),
                                    ]),
                            ]),

                        Tabs\Tab::make('Contact Information')
                            ->schema([
                                Section::make('Contact Details')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Forms\Components\TextInput::make('phone_number')
                                                    ->label('Phone Number')
                                                    ->tel()
                                                    ->maxLength(20),
                                                Forms\Components\TextInput::make('email')
                                                    ->label('Email Address')
                                                    ->email()
                                                    ->maxLength(100),
                                            ]),

                                        Forms\Components\Textarea::make('current_address')
                                            ->label('Current Address')
                                            ->rows(3),

                                        Forms\Components\Textarea::make('permanent_address')
                                            ->label('Permanent Address')
                                            ->rows(3),

                                        Grid::make(3)
                                            ->schema([
                                                Forms\Components\TextInput::make('district')
                                                    ->label('District')
                                                    ->maxLength(100),
                                                Forms\Components\TextInput::make('traditional_authority')
                                                    ->label('Traditional Authority')
                                                    ->maxLength(100),
                                                Forms\Components\TextInput::make('village')
                                                    ->label('Village')
                                                    ->maxLength(100),
                                            ]),
                                    ]),

                                Section::make('Emergency Contact')
                                    ->schema([
                                        Grid::make(3)
                                            ->schema([
                                                Forms\Components\TextInput::make('emergency_contact_name')
                                                    ->label('Emergency Contact Name')
                                                    ->maxLength(200),
                                                Forms\Components\TextInput::make('emergency_contact_phone')
                                                    ->label('Emergency Contact Phone')
                                                    ->tel()
                                                    ->maxLength(20),
                                                Forms\Components\TextInput::make('emergency_contact_relationship')
                                                    ->label('Relationship')
                                                    ->maxLength(100),
                                            ]),
                                    ]),
                            ]),

                        Tabs\Tab::make('Criminal Status')
                            ->schema([
                                Section::make('Status Information')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Forms\Components\Toggle::make('is_wanted')
                                                    ->label('Wanted Person')
                                                    ->default(false),
                                                Forms\Components\Toggle::make('is_repeat_offender')
                                                    ->label('Repeat Offender')
                                                    ->default(false),
                                            ]),
                                    ]),
                            ]),

                        Tabs\Tab::make('Additional Information')
                            ->schema([
                                Section::make('Personal Details')
                                    ->schema([
                                        Grid::make(3)
                                            ->schema([
                                                Forms\Components\TextInput::make('occupation')
                                                    ->label('Occupation')
                                                    ->maxLength(200),
                                                Forms\Components\TextInput::make('education_level')
                                                    ->label('Education Level')
                                                    ->maxLength(100),
                                                Forms\Components\TextInput::make('marital_status')
                                                    ->label('Marital Status')
                                                    ->maxLength(50),
                                            ]),

                                        Forms\Components\Textarea::make('known_associates')
                                            ->label('Known Associates')
                                            ->rows(3),

                                        Forms\Components\Textarea::make('notes')
                                            ->label('Additional Notes')
                                            ->rows(4),
                                    ]),
                            ]),

                        Tabs\Tab::make('Photos & Documents')
                            ->schema([
                                Section::make('Criminal Photos')
                                    ->schema([
                                        SpatieMediaLibraryFileUpload::make('mugshots')
                                            ->label('Mugshot')
                                            ->collection('mugshots')
                                            ->image()
                                            ->imageEditor()
                                            ->maxFiles(1),

                                        SpatieMediaLibraryFileUpload::make('identification_photos')
                                            ->label('Identification Photos')
                                            ->collection('identification_photos')
                                            ->image()
                                            ->imageEditor()
                                            ->multiple()
                                            ->maxFiles(5),
                                    ]),

                                Section::make('Documents')
                                    ->schema([
                                        SpatieMediaLibraryFileUpload::make('documents')
                                            ->label('Related Documents')
                                            ->collection('documents')
                                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                                            ->multiple()
                                            ->maxFiles(10),
                                    ]),
                            ]),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\SpatieMediaLibraryImageColumn::make('mugshots')
                    ->label('Photo')
                    ->collection('mugshots')
                    ->circular()
                    ->size(50),

                Tables\Columns\TextColumn::make('criminal_number')
                    ->label('Criminal #')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('full_name')
                    ->label('Full Name')
                    ->getStateUsing(fn ($record) => $record->full_name)
                    ->searchable(['first_name', 'middle_name', 'last_name'])
                    ->sortable(['first_name', 'last_name'])
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('alias')
                    ->label('Alias')
                    ->searchable()
                    ->toggleable()
                    ->placeholder('—'),

                Tables\Columns\TextColumn::make('gender')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Male' => 'info',
                        'Female' => 'success',
                        'Other' => 'warning',
                    }),

                Tables\Columns\TextColumn::make('age')
                    ->label('Age')
                    ->getStateUsing(fn ($record) => $record->age ? $record->age . ' years' : '—')
                    ->sortable('date_of_birth'),

                Tables\Columns\TextColumn::make('national_id')
                    ->label('National ID')
                    ->searchable()
                    ->toggleable()
                    ->copyable()
                    ->placeholder('—'),

                Tables\Columns\TextColumn::make('district')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'success' => 'Active',
                        'warning' => 'Inactive',
                        'danger' => 'Deceased',
                        'secondary' => 'Deported',
                    ]),

                Tables\Columns\BadgeColumn::make('risk_level')
                    ->label('Risk Level')
                    ->colors([
                        'success' => 'Low',
                        'warning' => 'Medium',
                        'danger' => 'High',
                        'danger' => 'Critical',
                    ]),

                Tables\Columns\IconColumn::make('is_wanted')
                    ->label('Wanted')
                    ->boolean()
                    ->trueIcon('heroicon-o-exclamation-triangle')
                    ->falseIcon('heroicon-o-check-circle')
                    ->trueColor('danger')
                    ->falseColor('success'),

                Tables\Columns\IconColumn::make('is_repeat_offender')
                    ->label('Repeat Offender')
                    ->boolean()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('phone_number')
                    ->label('Phone')
                    ->searchable()
                    ->toggleable()
                    ->placeholder('—'),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'Active' => 'Active',
                        'Inactive' => 'Inactive',
                        'Deceased' => 'Deceased',
                        'Deported' => 'Deported',
                    ]),

                SelectFilter::make('risk_level')
                    ->label('Risk Level')
                    ->options([
                        'Low' => 'Low',
                        'Medium' => 'Medium',
                        'High' => 'High',
                        'Critical' => 'Critical',
                    ]),

                SelectFilter::make('gender')
                    ->options([
                        'Male' => 'Male',
                        'Female' => 'Female',
                        'Other' => 'Other',
                    ]),

                Filter::make('is_wanted')
                    ->label('Wanted Persons')
                    ->query(fn (Builder $query): Builder => $query->where('is_wanted', true)),

                Filter::make('is_repeat_offender')
                    ->label('Repeat Offenders')
                    ->query(fn (Builder $query): Builder => $query->where('is_repeat_offender', true)),

                SelectFilter::make('district')
                    ->searchable()
                    ->preload(),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCriminals::route('/'),
            'create' => Pages\CreateCriminal::route('/create'),
            'edit' => Pages\EditCriminal::route('/{record}/edit'),
        ];
    }
}
