<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CriminalController;
use App\Http\Controllers\EvidenceController;
use App\Http\Controllers\CaseController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\BackupController;

Route::get('/', function () {
    return view('landing');
})->name('landing');

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    // Dashboard Routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/analytics', [DashboardController::class, 'getCrimeAnalytics'])->name('dashboard.analytics');

    Route::get('/dashboard/statistics', function () {
        return view('dashboard.statistics');
    })->name('dashboard.statistics');

    Route::get('/dashboard/quick-actions', function () {
        return view('dashboard.quick-actions');
    })->name('dashboard.quick-actions');

    // Criminal Profile Routes
    Route::resource('criminals', CriminalController::class);
    Route::get('criminals-list', [CriminalController::class, 'list'])->name('criminals.list');

    // Case Management Routes
    Route::prefix('cases')->name('cases.')->group(function () {
        Route::get('/active', [CaseController::class, 'active'])->name('active');
        Route::get('/create', [CaseController::class, 'create'])->name('create');
        Route::post('/store', [CaseController::class, 'store'])->name('store');
        Route::get('/search', [CaseController::class, 'search'])->name('search');
        Route::get('/reports', [CaseController::class, 'reports'])->name('reports');
        Route::get('/{id}', [CaseController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [CaseController::class, 'edit'])->name('edit');
        Route::put('/{id}', [CaseController::class, 'update'])->name('update');
        Route::delete('/{id}', [CaseController::class, 'destroy'])->name('destroy');
    });

    // Evidence Management Routes
    Route::resource('evidence', EvidenceController::class);
    Route::get('evidence/custody', [EvidenceController::class, 'custody'])->name('evidence.custody');
    Route::get('evidence/forensic', [EvidenceController::class, 'forensic'])->name('evidence.forensic');

    // Court Case Routes
    Route::prefix('court')->name('court.')->group(function () {
        Route::get('/hearings', function () {
            return view('court.hearings');
        })->name('hearings');

        Route::get('/status', function () {
            return view('court.status');
        })->name('status');

        Route::get('/calendar', function () {
            return view('court.calendar');
        })->name('calendar');

        Route::get('/verdicts', function () {
            return view('court.verdicts');
        })->name('verdicts');
    });

    // Document Management Routes
    Route::prefix('documents')->name('documents.')->group(function () {
        Route::get('/library', function () {
            return view('documents.library');
        })->name('library');

        Route::get('/upload', function () {
            return view('documents.upload');
        })->name('upload');

        Route::get('/templates', function () {
            return view('documents.templates');
        })->name('templates');

        Route::get('/shared', function () {
            return view('documents.shared');
        })->name('shared');
    });

    // Reports Routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/crime', [ReportsController::class, 'crime'])->name('crime');
        Route::get('/statistics', [ReportsController::class, 'statistics'])->name('statistics');
        Route::get('/analytics', [ReportsController::class, 'analytics'])->name('analytics');
    });

    // User Management Routes
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [UserController::class, 'index'])->name('index');
        Route::get('/create', [UserController::class, 'create'])->name('create');
        Route::post('/', [UserController::class, 'store'])->name('store');
        Route::get('/active', [UserController::class, 'active'])->name('active');
        Route::get('/inactive', [UserController::class, 'inactive'])->name('inactive');
        Route::get('/{id}', [UserController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [UserController::class, 'edit'])->name('edit');
        Route::put('/{id}', [UserController::class, 'update'])->name('update');
        Route::delete('/{id}', [UserController::class, 'destroy'])->name('destroy');
        Route::post('/{id}/toggle-status', [UserController::class, 'toggleStatus'])->name('toggle-status');
        Route::post('/{id}/reset-password', [UserController::class, 'resetPassword'])->name('reset-password');
        Route::post('/bulk-action', [UserController::class, 'bulkAction'])->name('bulk-action');
    });

    // Role Management Routes
    Route::prefix('roles')->name('roles.')->group(function () {
        Route::get('/', [RoleController::class, 'index'])->name('index');
        Route::get('/create', [RoleController::class, 'create'])->name('create');
        Route::post('/', [RoleController::class, 'store'])->name('store');
        Route::get('/permissions', [RoleController::class, 'permissions'])->name('permissions');
        Route::get('/assignments', [RoleController::class, 'assignments'])->name('assignments');
        Route::get('/{id}', [RoleController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [RoleController::class, 'edit'])->name('edit');
        Route::put('/{id}', [RoleController::class, 'update'])->name('update');
        Route::delete('/{id}', [RoleController::class, 'destroy'])->name('destroy');
        Route::post('/assign', [RoleController::class, 'assignRole'])->name('assign');
        Route::post('/remove', [RoleController::class, 'removeRole'])->name('remove');
    });

    // Backup Management Routes
    Route::prefix('backups')->name('backups.')->group(function () {
        Route::get('/', [BackupController::class, 'index'])->name('index');
        Route::get('/create', [BackupController::class, 'create'])->name('create');
        Route::post('/', [BackupController::class, 'store'])->name('store');
        Route::get('/schedule', [BackupController::class, 'schedule'])->name('schedule');
        Route::get('/restore', [BackupController::class, 'restore'])->name('restore');
        Route::get('/{id}/download', [BackupController::class, 'download'])->name('download');
        Route::delete('/{id}', [BackupController::class, 'destroy'])->name('destroy');
        Route::post('/restore-backup', [BackupController::class, 'restoreBackup'])->name('restore-backup');
    });

    // Settings Routes
    Route::get('/settings', function () {
        return view('settings.index');
    })->name('settings.index');
});
