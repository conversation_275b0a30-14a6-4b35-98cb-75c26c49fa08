<?php

namespace App\Models;

use App\Traits\HasUserTracking;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Criminal extends Model implements HasMedia
{
    use HasFactory;
    use HasUserTracking;
    use LogsActivity;
    use InteractsWithMedia;
    use HasSlug;
    use SoftDeletes;

    protected $fillable = [
        'criminal_number',
        'first_name',
        'middle_name',
        'last_name',
        'alias',
        'gender',
        'date_of_birth',
        'place_of_birth',
        'nationality',
        'national_id',
        'height',
        'weight',
        'eye_color',
        'hair_color',
        'complexion',
        'distinguishing_marks',
        'scars_tattoos',
        'phone_number',
        'email',
        'current_address',
        'permanent_address',
        'district',
        'traditional_authority',
        'village',
        'emergency_contact_name',
        'emergency_contact_phone',
        'emergency_contact_relationship',
        'status',
        'risk_level',
        'is_wanted',
        'is_repeat_offender',
        'occupation',
        'education_level',
        'marital_status',
        'known_associates',
        'notes',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'height' => 'decimal:2',
        'weight' => 'decimal:2',
        'is_wanted' => 'boolean',
        'is_repeat_offender' => 'boolean',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom(['first_name', 'last_name'])
            ->saveSlugsTo('slug');
    }

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'criminal_number', 'first_name', 'last_name', 'status',
                'risk_level', 'is_wanted', 'is_repeat_offender'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Register media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('mugshots')
            ->acceptsMimeTypes(['image/jpeg', 'image/png'])
            ->singleFile();

        $this->addMediaCollection('identification_photos')
            ->acceptsMimeTypes(['image/jpeg', 'image/png']);

        $this->addMediaCollection('documents')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png']);
    }

    /**
     * Register media conversions.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(300)
            ->sharpen(10);

        $this->addMediaConversion('preview')
            ->width(800)
            ->height(600)
            ->quality(90);
    }

    /**
     * Get the full name attribute.
     */
    public function getFullNameAttribute(): string
    {
        $name = $this->first_name;
        if ($this->middle_name) {
            $name .= ' ' . $this->middle_name;
        }
        $name .= ' ' . $this->last_name;

        return $name;
    }

    /**
     * Get the age attribute.
     */
    public function getAgeAttribute(): ?int
    {
        return $this->date_of_birth ? $this->date_of_birth->age : null;
    }

    /**
     * Scope for active criminals.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    /**
     * Scope for wanted criminals.
     */
    public function scopeWanted($query)
    {
        return $query->where('is_wanted', true);
    }

    /**
     * Scope for high-risk criminals.
     */
    public function scopeHighRisk($query)
    {
        return $query->whereIn('risk_level', ['High', 'Critical']);
    }

    /**
     * Scope for repeat offenders.
     */
    public function scopeRepeatOffenders($query)
    {
        return $query->where('is_repeat_offender', true);
    }

    /**
     * Scope for search by name.
     */
    public function scopeSearchByName($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('first_name', 'like', "%{$search}%")
              ->orWhere('middle_name', 'like', "%{$search}%")
              ->orWhere('last_name', 'like', "%{$search}%")
              ->orWhere('alias', 'like', "%{$search}%");
        });
    }

    // Relationships

    /**
     * Get the arrests for the criminal.
     */
    public function arrests()
    {
        return $this->hasMany(CriminalArrest::class);
    }

    /**
     * Get the biometric data for the criminal.
     */
    public function biometrics()
    {
        return $this->hasMany(CriminalBiometric::class);
    }

    /**
     * Get the relationships for the criminal.
     */
    public function relationships()
    {
        return $this->hasMany(CriminalRelationship::class);
    }

    /**
     * Get the medical information for the criminal.
     */
    public function medicalInfo()
    {
        return $this->hasOne(CriminalMedicalInfo::class);
    }

    /**
     * Get the cases associated with the criminal.
     */
    public function cases()
    {
        return $this->belongsToMany(CriminalCase::class, 'case_criminals');
    }

    /**
     * Get the user who created the criminal record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the criminal record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
