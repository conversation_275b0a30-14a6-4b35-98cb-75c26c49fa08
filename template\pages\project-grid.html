<!DOCTYPE html>
<html lang="en">


<!-- Mirrored from dashui.codescandy.com/dashuipro/pages/project-grid.html by HTTrack Website Copier/3.x [XR&CO'2014], <PERSON>e, 27 May 2025 08:29:42 GMT -->
<head>
  <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
<meta name="author" content="Codescandy" />

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M8S4MT3EYG"></script>
<script>
	window.dataLayer = window.dataLayer || [];
	function gtag() {
		dataLayer.push(arguments);
	}
	gtag('js', new Date());

	gtag('config', 'G-M8S4MT3EYG');
</script>

<!-- Favicon icon-->
<link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon/favicon.ico" />

<!-- Color modes -->
<script src="../assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="../assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />
<link href="../assets/libs/%40mdi/font/css/materialdesignicons.min.css" rel="stylesheet" />
<link href="../assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />

<!-- Theme CSS -->
<link rel="stylesheet" href="../assets/css/theme.min.css">

  <!-- dropzone -->
  <link href="../assets/libs/dropzone/dist/dropzone.css" rel="stylesheet">
  <title>Project Grid | Dash UI - Bootstrap 5 Admin Dashboard Template</title>
</head>

<body>
  <!-- Wrapper -->
  <main id="main-wrapper" class="main-wrapper">

    <div class="header">
	<!-- navbar -->
	<div class="navbar-custom navbar navbar-expand-lg">
		<div class="container-fluid px-0">
			<a class="navbar-brand d-block d-md-none" href="../index.html">
				<img src="../assets/images/brand/logo/logo-2.svg" alt="Image" />
			</a>

			<a id="nav-toggle" href="#!" class="ms-auto ms-md-0 me-0 me-lg-3">
				<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor" class="bi bi-text-indent-left text-muted" viewBox="0 0 16 16">
					<path
						d="M2 3.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm.646 2.146a.5.5 0 0 1 .708 0l2 2a.5.5 0 0 1 0 .708l-2 2a.5.5 0 0 1-.708-.708L4.293 8 2.646 6.354a.5.5 0 0 1 0-.708zM7 6.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm-5 3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"
					/>
				</svg>
			</a>

			<div class="d-none d-md-none d-lg-block">
				<!-- Form -->
				<form action="#">
					<div class="input-group">
						<input class="form-control rounded-3 bg-transparent ps-9" type="search" value="" id="searchInput" placeholder="Search" />
						<span class="">
							<button class="btn position-absolute start-0" type="button">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="15"
									height="15"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
									class="feather feather-search text-dark"
								>
									<circle cx="11" cy="11" r="8"></circle>
									<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
								</svg>
							</button>
						</span>
					</div>
				</form>
			</div>
			<!--Navbar nav -->
			<ul class="navbar-nav navbar-right-wrap ms-lg-auto d-flex nav-top-wrap align-items-center ms-4 ms-lg-0">
				<li>
					<div class="dropdown">
						<button class="btn btn-ghost btn-icon rounded-circle" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
							<i class="bi theme-icon-active"></i>
							<span class="visually-hidden bs-theme-text">Toggle theme</span>
						</button>
						<ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
									<i class="bi theme-icon bi-sun-fill"></i>
									<span class="ms-2">Light</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
									<i class="bi theme-icon bi-moon-stars-fill"></i>
									<span class="ms-2">Dark</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="auto" aria-pressed="true">
									<i class="bi theme-icon bi-circle-half"></i>
									<span class="ms-2">Auto</span>
								</button>
							</li>
						</ul>
					</div>
				</li>

				<li class="dropdown stopevent ms-2">
					<a class="btn btn-ghost btn-icon rounded-circle" href="#!" role="button" id="dropdownNotification" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<i class="icon-xs" data-feather="bell"></i>
					</a>
					<div class="dropdown-menu dropdown-menu-lg dropdown-menu-end" aria-labelledby="dropdownNotification">
						<div>
							<div class="border-bottom px-3 pt-2 pb-3 d-flex justify-content-between align-items-center">
								<p class="mb-0 text-dark fw-medium fs-4">Notifications</p>
								<a href="#!" class="text-muted">
									<span>
										<i class="me-1 icon-xs" data-feather="settings"></i>
									</span>
								</a>
							</div>
							<div data-simplebar style="height: 250px">
								<!-- List group -->
								<ul class="list-group list-group-flush notification-list-scroll">
									<!-- List group item -->
									<li class="list-group-item bg-light">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Rishi Chopra</h5>
											<p class="mb-0">Mauris blandit erat id nunc blandit, ac eleifend dolor pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Neha Kannned</h5>
											<p class="mb-0">Proin at elit vel est condimentum elementum id in ante. Maecenas et sapien metus.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Nirmala Chauhan</h5>
											<p class="mb-0">Morbi maximus urna lobortis elit sollicitudin sollicitudieget elit vel pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Sina Ray</h5>
											<p class="mb-0">Sed aliquam augue sit amet mauris volutpat hendrerit sed nunc eu diam.</p>
										</a>
									</li>
								</ul>
							</div>
							<div class="border-top px-3 py-2 text-center">
								<a href="#!" class="text-inherit">View all Notifications</a>
							</div>
						</div>
					</div>
				</li>
				<!-- List -->
				<li class="dropdown ms-2">
					<a class="rounded-circle" href="#!" role="button" id="dropdownUser" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<div class="avatar avatar-md avatar-indicators avatar-online">
							<img alt="avatar" src="../assets/images/avatar/avatar-11.jpg" class="rounded-circle" />
						</div>
					</a>
					<div class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownUser">
						<div class="px-4 pb-0 pt-2">
							<div class="lh-1">
								<h5 class="mb-1">John E. Grainger</h5>
								<a href="#!" class="text-inherit fs-6">View my profile</a>
							</div>
							<div class="dropdown-divider mt-3 mb-2"></div>
						</div>

						<ul class="list-unstyled">
							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="user"></i>
									Edit Profile
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="activity"></i>
									Activity Log
								</a>
							</li>

							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="settings"></i>
									Settings
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="../index.html">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="power"></i>
									Sign Out
								</a>
							</li>
						</ul>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>

    <!-- navbar vertical -->
    <!-- Sidebar -->

<div class="navbar-vertical navbar nav-dashboard">
	<div class="h-100" data-simplebar>
		<!-- Brand logo -->
		<a class="navbar-brand" href="../index.html">
			<img src="../assets/images/brand/logo/logo-2.svg" alt="dash ui - bootstrap 5 admin dashboard template" />
		</a>
		<!-- Navbar nav -->
		<ul class="navbar-nav flex-column" id="sideNavbar">
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navDashboard"
					aria-expanded="false"
					aria-controls="navDashboard"
				>
					<i data-feather="home" class="nav-icon me-2 icon-xxs"></i>
					Dashboard
				</a>

				<div id="navDashboard" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="dashboard-analytics.html">Analytics</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../index.html">Project</a>
						</li>

						<li class="nav-item">
							<a class="nav-link has-arrow " href="dashboard-ecommerce.html">Ecommerce</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="dashboard-crm.html">CRM</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="dashboard-finance.html">Finance</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="dashboard-blog.html">Blog</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">Apps</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow " href="calendar.html">Calendar</a>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow " href="chat-app.html">
					<i data-feather="message-square" class="nav-icon me-2 icon-xxs"></i>
					Chat
				</a>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navecommerce"
					aria-expanded="false"
					aria-controls="navecommerce"
				>
					<i data-feather="shopping-cart" class="nav-icon me-2 icon-xxs"></i>
					Ecommerce
				</a>

				<div id="navecommerce" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-products.html">Products</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-products-details.html">Product Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-product-edit.html">Add Product</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-order-list.html">Orders</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-order-detail.html">Orders Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-cart.html">Shopping cart</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-checkout.html">Checkout</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-customer.html">Customer</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-seller.html">Seller</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navEmail" aria-expanded="false" aria-controls="navEmail">
					<i data-feather="mail" class="nav-icon me-2 icon-xxs"></i>
					Email
				</a>

				<div id="navEmail" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="mail.html">Inbox</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="mail-details.html">Details</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="mail-draft.html">Draft</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navKanban"
					aria-expanded="false"
					aria-controls="navKanban"
				>
					<i data-feather="layout" class="nav-icon me-2 icon-xxs"></i>
					Kanban
				</a>

				<div id="navKanban" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="task-kanban-grid.html">Grid</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="task-kanban-list.html">List</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link " href="#!" data-bs-toggle="collapse" data-bs-target="#navProject" aria-expanded="false" aria-controls="navProject">
					<i class="nav-icon me-2 icon-xxs" data-feather="file"></i>
					Project
				</a>
				<div id="navProject" class="collapse  show " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link  active " href="project-grid.html">Grid</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="project-list.html">List</a>
						</li>
						<li class="nav-item">
							<a
								class="nav-link  collapsed "
								href="#!"
								data-bs-toggle="collapse"
								data-bs-target="#navprojectSingle"
								aria-expanded="false"
								aria-controls="navprojectSingle"
							>
								Single
							</a>
							<div id="navprojectSingle" class="collapse " data-bs-parent="#navProject">
								<ul class="nav flex-column">
									<li class="nav-item">
										<a class="nav-link " href="project-overview.html">Overview</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="project-task.html">Task</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="project-budget.html">Budget</a>
									</li>

									<li class="nav-item">
										<a class="nav-link " href="project-files.html">Files</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="project-team.html">Team</a>
									</li>
								</ul>
							</div>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="add-project.html">Create Project</a>
						</li>
					</ul>
				</div>
			</li>
			<li class="nav-item">
				<a class="nav-link " href="apps-file-manager.html">
					<i data-feather="folder-plus" class="nav-icon me-2 icon-xxs"></i>
					File Manager
				</a>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navCRM" aria-expanded="false" aria-controls="navCRM">
					<i data-feather="pie-chart" class="nav-icon me-2 icon-xxs"></i>

					CRM
				</a>

				<div id="navCRM" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="crm-contacts.html">Contacts</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="crm-company.html">Company</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="deals.html">
								Deals
								<span class="badge text-bg-primary ms-2">New</span>
							</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="deals-single.html">
								Deals Single
								<span class="badge text-bg-primary ms-2">New</span>
							</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navinvoice"
					aria-expanded="false"
					aria-controls="navinvoice"
				>
					<i data-feather="clipboard" class="nav-icon me-2 icon-xxs"></i>
					Invoice
				</a>

				<div id="navinvoice" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="invoice-list.html">List</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="invoice-detail.html">Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="invoice-generator.html">Invoice Generator</a>
						</li>
					</ul>
				</div>
			</li>
			<li class="nav-item">
				<a
					class="nav-link  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navprofilePages"
					aria-expanded="false"
					aria-controls="navprofilePages"
				>
					<i data-feather="user" class="nav-icon me-2 icon-xxs"></i>
					Profile
				</a>
				<div id="navprofilePages" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="profile-overview.html">Overview</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="profile-project.html">Project</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="profile-files.html">Files</a>
						</li>

						<li class="nav-item">
							<a class="nav-link " href="profile-team.html">Team</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="profile-followers.html">Followers</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="profile-activity.html">Activity</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="profile-settings.html">Settings</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navblog" aria-expanded="false" aria-controls="navblog">
					<i data-feather="edit" class="nav-icon me-2 icon-xxs"></i>
					Blog
				</a>

				<div id="navblog" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="blog-author.html">Author</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="blog-author-detail.html">Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="create-blog-post.html">Create Post</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">Layouts & Pages</div>
			</li>

			<li class="nav-item">
				<a
					class="nav-link  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navlayoutPage"
					aria-expanded="false"
					aria-controls="navlayoutPage"
				>
					<i class="nav-icon me-2 icon-xxs" data-feather="file"></i>
					Pages
				</a>
				<div id="navlayoutPage" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="starter.html">Starter</a>
						</li>

						<li class="nav-item">
							<a class="nav-link " href="pricing.html">Pricing</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="maintenance.html">Maintenance</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="404-error.html">404 Error</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navAuthentication"
					aria-expanded="false"
					aria-controls="navAuthentication"
				>
					<i data-feather="lock" class="nav-icon me-2 icon-xxs"></i>
					Authentication
				</a>
				<div id="navAuthentication" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="sign-in.html">Sign In</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="sign-up.html">Sign Up</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="forget-password.html">Forget Password</a>
						</li>
					</ul>
				</div>
			</li>
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navLayouts"
					aria-expanded="false"
					aria-controls="navLayouts"
				>
					<i data-feather="sidebar" class="nav-icon me-2 icon-xxs"></i>
					Layouts
				</a>
				<div id="navLayouts" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="../index.html">Default</a>
						</li>

						<li class="nav-item">
							<a class="nav-link " href="../horizontal/index.html">Horizontal</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">UI Components</div>
			</li>
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navComponents"
					aria-expanded="false"
					aria-controls="navComponents"
				>
					<i data-feather="package" class="nav-icon me-2 icon-xxs"></i>
					Components
				</a>
				<div id="navComponents" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a href="components/accordions.html" class="nav-link ">Accordions</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="components/alerts.html">Alert</a>
						</li>

						<li class="nav-item">
							<a href="components/badge.html" class="nav-link ">Badge</a>
						</li>

						<li class="nav-item">
							<a href="components/breadcrumb.html" class="nav-link ">Breadcrumb</a>
						</li>
						<li class="nav-item">
							<a href="components/buttons.html" class="nav-link ">Buttons</a>
						</li>
						<li class="nav-item">
							<a href="components/button-group.html" class="nav-link ">Button group</a>
						</li>
						<li class="nav-item">
							<a href="components/card.html" class="nav-link ">Card</a>
						</li>
						<li class="nav-item">
							<a href="components/carousel.html" class="nav-link ">Carousel</a>
						</li>
						<li class="nav-item">
							<a href="components/close-button.html" class="nav-link ">Close Button</a>
						</li>
						<li class="nav-item">
							<a href="components/collapse.html" class="nav-link ">Collapse</a>
						</li>
						<li class="nav-item">
							<a href="components/dropdowns.html" class="nav-link ">Dropdowns</a>
						</li>
						<li class="nav-item">
							<a href="components/forms.html" class="nav-link ">Forms</a>
						</li>

						<li class="nav-item">
							<a href="components/list-group.html" class="nav-link ">List group</a>
						</li>

						<li class="nav-item">
							<a href="components/modal.html" class="nav-link ">Modal</a>
						</li>
						<li class="nav-item">
							<a href="components/navs-tabs.html" class="nav-link ">Navs and tabs</a>
						</li>
						<li class="nav-item">
							<a href="components/navbar.html" class="nav-link ">Navbar</a>
						</li>
						<li class="nav-item">
							<a href="components/offcanvas.html" class="nav-link ">Offcanvas</a>
						</li>
						<li class="nav-item">
							<a href="components/pagination.html" class="nav-link ">Pagination</a>
						</li>
						<li class="nav-item">
							<a href="components/placeholders.html" class="nav-link ">Placeholders</a>
						</li>
						<li class="nav-item">
							<a href="components/popovers.html" class="nav-link ">Popovers</a>
						</li>
						<li class="nav-item">
							<a href="components/progress.html" class="nav-link ">Progress</a>
						</li>
						<li class="nav-item">
							<a href="components/scrollspy.html" class="nav-link ">Scrollspy</a>
						</li>
						<li class="nav-item">
							<a href="components/spinners.html" class="nav-link ">Spinners</a>
						</li>
						<li class="nav-item">
							<a href="components/tables.html" class="nav-link ">Tables</a>
						</li>
						<li class="nav-item">
							<a href="components/toasts.html" class="nav-link ">Toasts</a>
						</li>
						<li class="nav-item">
							<a href="components/tooltips.html" class="nav-link ">Tooltips</a>
						</li>
					</ul>
				</div>
			</li>

			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navMenuLevel"
					aria-expanded="false"
					aria-controls="navMenuLevel"
				>
					<i data-feather="corner-left-down" class="nav-icon me-2 icon-xxs"></i>
					Menu Level
				</a>
				<div id="navMenuLevel" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a
								class="nav-link has-arrow "
								href="#!"
								data-bs-toggle="collapse"
								data-bs-target="#navMenuLevelSecond"
								aria-expanded="false"
								aria-controls="navMenuLevelSecond"
							>
								Two Level
							</a>
							<div id="navMenuLevelSecond" class="collapse" data-bs-parent="#navMenuLevel">
								<ul class="nav flex-column">
									<li class="nav-item">
										<a class="nav-link " href="#!">NavItem 1</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="#!">NavItem 2</a>
									</li>
								</ul>
							</div>
						</li>
						<li class="nav-item">
							<a
								class="nav-link has-arrow  collapsed "
								href="#!"
								data-bs-toggle="collapse"
								data-bs-target="#navMenuLevelThree"
								aria-expanded="false"
								aria-controls="navMenuLevelThree"
							>
								Three Level
							</a>
							<div id="navMenuLevelThree" class="collapse " data-bs-parent="#navMenuLevel">
								<ul class="nav flex-column">
									<li class="nav-item">
										<a
											class="nav-link  collapsed "
											href="#!"
											data-bs-toggle="collapse"
											data-bs-target="#navMenuLevelThreeOne"
											aria-expanded="false"
											aria-controls="navMenuLevelThreeOne"
										>
											NavItem 1
										</a>
										<div id="navMenuLevelThreeOne" class="collapse collapse " data-bs-parent="#navMenuLevelThree">
											<ul class="nav flex-column">
												<li class="nav-item">
													<a class="nav-link " href="#!">NavChild Item 1</a>
												</li>
											</ul>
										</div>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="#!">Nav Item 2</a>
									</li>
								</ul>
							</div>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">Documentation</div>
			</li>
			<li class="nav-item">
				<a class="nav-link has-arrow  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navDocs" aria-expanded="false" aria-controls="navDocs">
					<i data-feather="package" class="nav-icon me-2 icon-xxs"></i>
					Docs
				</a>
				<div id="navDocs" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item"><a href="../docs/index.html" class="nav-link ">Introduction</a></li>
						<li class="nav-item"><a href="../docs/environment-setup.html" class="nav-link ">Environment setup</a></li>
						<li class="nav-item"><a href="../docs/working-with-gulp.html" class="nav-link ">Working with Gulp</a></li>
						<li class="nav-item"><a href="../docs/compiled-files.html" class="nav-link ">Compiled Files</a></li>
						<li class="nav-item"><a href="../docs/file-structure.html" class="nav-link ">File Structure</a></li>
						<li class="nav-item"><a href="../docs/resources-assets.html" class="nav-link ">Resources & assets</a></li>
						<li class="nav-item"><a href="../docs/changelog.html" class="nav-link ">Changelog</a></li>
					</ul>
				</div>
			</li>
		</ul>
		<div class="card bg-light shadow-none text-center mx-4 my-8">
			<div class="card-body py-6">
				<img src="../assets/images/background/giftbox.png" alt="dash ui - admin dashboard template" />
				<div class="mt-4">
					<h5>Unlimited Access</h5>
					<p class="fs-6 mb-4">Upgrade your plan from a Free trial, to select Business Plan. Start Now</p>
					<a href="#" class="btn btn-secondary btn-sm">Upgrade Now</a>
				</div>
			</div>
		</div>
	</div>
</div>




    <!-- Page Content -->
    <div id="app-content">
      <!-- Container fluid -->
      <div class="app-content-area">
        <div class="container-fluid">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-12">
              <!-- Page header -->
              <div class="mb-4">
                <h3 class="mb-0 ">Project Grid</h3>

              </div>
            </div>
          </div>
          <!-- row -->
          <div class="row justify-content-md-between mb-5 mb-xl-0 ">
            <!-- col -->
            <div class="col-xl-2 col-lg-4 col-md-6 col-12">
              <!-- search -->
              <div class="mb-2 mb-lg-4">
                <input type="search" class="form-control" placeholder="Search by project name">
              </div>
            </div>
            <div class="col-xxl-1 col-lg-2 col-md-6 col-12 ">


              <!-- Custom select -->
              <select class="form-select">
                <option value="">Filter</option>
                <option value="In Progress">In Progress</option>
                <option value="Pending">Pending</option>
                <option value="Modified">Modified</option>
                <option value="Finished">Finished</option>
                <option value="Cancel">Cancel</option>

              </select>

            </div>
          </div>
          <div class="row">
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-5">
              <!-- card -->
              <div class="card h-100">
                <!-- card body -->
                <div class="card-body">
                  <!-- heading-->
                  <div class="d-flex align-items-center
                                justify-content-between">

                    <!-- text-->

                    <div>
                      <h4 class="mb-0 h5"><a href="#!" class="text-inherit">Bootsrap 5 UI Kit Design in Figma</a></h4>
                      <span class="text-muted fs-6">Web Design</span>


                    </div>
                    <!-- dropdown-->
                    <div class="d-flex align-items-center">

                      <div class="dropdown dropstart">
                        <a href="#!" class="btn-icon btn btn-ghost btn-sm rounded-circle" data-bs-toggle="dropdown"
                          aria-haspopup="true" aria-expanded="false">
                          <i data-feather="more-vertical" class="icon-xs"></i>
                        </a>
                        <div class="dropdown-menu">

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="edit"></i>Edit Details
                          </a>

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="link"></i>Copy project link

                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="save"></i>Save as Default
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="copy"></i>Duplicate
                          </a>


                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="upload"></i>Import
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="printer"></i>Export / Print
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="users"></i>Move to another team
                          </a>
                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="archive"></i>Archive
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="trash"></i>Delete Project
                          </a>


                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- para-->
                  <div class="mt-3 mb-4">
                    <p class="mb-0">Web application design is an important stage
                      when building a web application...</p>
                  </div>
                  <!-- progress -->
                  <div class="d-flex justify-content-between
                                align-items-center mb-5">
                    <div class="d-flex align-items-center">
                      <!-- avatar group -->
                      <div class="avatar-group">
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-11.jpg" class="rounded-circle imgtooltip"
                            data-template="one">
                          <span id="one" class="d-none">
                            <span>Paul Haney</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-2.jpg" class="rounded-circle imgtooltip"
                            data-template="two">
                          <span id="two" class="d-none">
                            <span>Gali Linear</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-3.jpg" class="rounded-circle imgtooltip"
                            data-template="three">
                          <span id="three" class="d-none">
                            <span>Mary Holler</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm ">
                          <span class="avatar-initials rounded-circle bg-light text-dark">5+</span>
                        </span>
                      </div>
                    </div>
                    <!-- text -->
                    <div>
                      <span class="badge badge-info-soft ">In Progress</span>
                    </div>
                  </div>
                  <div>
                    <!-- progress bar -->
                    <div class="d-flex justify-content-between mb-2 fs-6"> <span>Progress</span>
                      <span>45%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                      <div class="progress-bar bg-info " role="progressbar" style="width: 45%;" aria-valuenow="45"
                        aria-valuemin="0" aria-valuemax="100">

                      </div>
                    </div>
                  </div>
                </div>
                <!-- card footer -->
                <div class="card-footer p-0">
                  <div class="d-flex justify-content-between ">
                    <div class="w-50 py-3 px-4 ">
                      <h6 class="mb-0 text-muted">Due Date:</h6>
                      <p class="text-dark fs-6  mb-0">1 Jan, 2023</p>
                    </div>
                    <div class="border-start w-50 py-3 px-4">
                      <h6 class="mb-0 text-muted">Budget:</h6>
                      <p class="text-dark fs-6  mb-0">$1,23,000 </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-5">
              <!-- card -->
              <div class="card h-100">
                <!-- card body -->
                <div class="card-body">
                  <!-- heading-->
                  <div class="d-flex align-items-center justify-content-between">
                    <!-- text-->
                    <div>
                      <h4 class="mb-0 h5"><a href="#!" class="text-inherit">Ecommerce UI Kit Design & Development </a></h4>
                      <span class="text-muted fs-6">Web Development</span>
                    </div>
                    <!-- dropdown-->
                    <div class="d-flex align-items-center">

                      <div class="dropdown dropstart">
                        <a href="#!" class="btn-icon btn btn-ghost btn-sm rounded-circle" data-bs-toggle="dropdown"
                          aria-haspopup="true" aria-expanded="false">
                          <i data-feather="more-vertical" class="icon-xs"></i>
                        </a>
                        <div class="dropdown-menu">

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="edit"></i>Edit Details
                          </a>

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="link"></i>Copy project link

                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="save"></i>Save as Default
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="copy"></i>Duplicate
                          </a>


                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="upload"></i>Import
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="printer"></i>Export / Print
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="users"></i>Move to another team
                          </a>
                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="archive"></i>Archive
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="trash"></i>Delete Project
                          </a>


                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- para-->
                  <div class="mt-3 mb-4">
                    <p class="mb-0">Mauris quis nibh eu tortor blandit lacinia id sed
                      ans dui turpis, semper ac turpis quis</p>
                  </div>

                  <!-- progress -->
                  <div class="d-flex justify-content-between
                                  align-items-center mb-5">
                    <div class="d-flex align-items-center">
                      <!-- avatar group -->
                      <div class="avatar-group">
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-5.jpg" class="rounded-circle imgtooltip"
                            data-template="four">
                          <span id="four" class="d-none">
                            <span>Gilbert Farr</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm avatar-primary imgtooltip" data-template="textOne">
                          <span class="avatar-initials rounded-circle ">

                            DU</span>

                          <span id="textOne" class="d-none">
                            <span>Dash UI Only</span>

                          </span>
                        </span>


                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-6.jpg" class="rounded-circle imgtooltip"
                            data-template="five">
                          <span id="five" class="d-none">
                            <span>Charlie Holland</span>

                          </span>
                        </span>

                        <span class="avatar avatar-sm ">
                          <span class="avatar-initials rounded-circle bg-light text-dark">6+</span>
                        </span>


                      </div>

                    </div>
                    <!-- text -->
                    <div>
                      <span class="badge badge-danger-soft ">Cancel</span>
                    </div>

                  </div>
                  <div>
                    <!-- progress bar -->
                    <div class="d-flex justify-content-between mb-2 fs-6"> <span>Progress</span>
                      <span>12%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                      <div class="progress-bar bg-danger " role="progressbar" style="width: 12%;" aria-valuenow="12"
                        aria-valuemin="0" aria-valuemax="100">

                      </div>
                    </div>

                  </div>

                </div>
                <!-- card footer -->
                <div class="card-footer p-0">
                  <div class="d-flex justify-content-between ">

                    <div class="w-50 py-3 px-4 ">
                      <h6 class="mb-0 text-muted">Due Date:</h6>
                      <p class="text-dark fs-6  mb-0">-</p>
                    </div>
                    <div class="border-start w-50 py-3 px-4">
                      <h6 class="mb-0 text-muted">Budget:</h6>
                      <p class="text-dark fs-6  mb-0">$0</p>

                    </div>
                  </div>

                </div>

              </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-5">
              <!-- card -->
              <div class="card h-100">
                <!-- card body -->
                <div class="card-body">
                  <!-- heading-->
                  <div class="d-flex align-items-center
                                  justify-content-between">

                    <!-- text-->

                    <div>
                      <h4 class="mb-0 h5"><a href="#!" class="text-inherit">CRM Customer Service Software </a></h4>
                      <span class="text-muted fs-6">Front End Development</span>


                    </div>
                    <!-- dropdown-->
                    <div class="d-flex align-items-center">

                      <div class="dropdown dropstart">
                        <a href="#!" class="btn-icon btn btn-ghost btn-sm rounded-circle" data-bs-toggle="dropdown"
                          aria-haspopup="true" aria-expanded="false">
                          <i data-feather="more-vertical" class="icon-xs"></i>
                        </a>
                        <div class="dropdown-menu">

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="edit"></i>Edit Details
                          </a>

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="link"></i>Copy project link

                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="save"></i>Save as Default
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="copy"></i>Duplicate
                          </a>


                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="upload"></i>Import
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="printer"></i>Export / Print
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="users"></i>Move to another team
                          </a>
                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="archive"></i>Archive
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="trash"></i>Delete Project
                          </a>


                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- para-->
                  <div class="mt-3 mb-4">
                    <p class="mb-0">Quisque at augue convallis, tincidunt erat et,
                      tristique ssa mollis dignissim eget</p>
                  </div>

                  <!-- progress -->
                  <div class="d-flex justify-content-between
                                  align-items-center mb-5">
                    <div class="d-flex align-items-center">
                      <!-- avatar group -->
                      <div class="avatar-group">
                        <span class="avatar avatar-sm avatar-info imgtooltip" data-template="textTwo">
                          <span class="avatar-initials rounded-circle ">

                            DU</span>

                          <span id="textTwo" class="d-none">
                            <span>Dash UI Only</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-9.jpg" class="rounded-circle imgtooltip"
                            data-template="eight">
                          <span id="eight" class="d-none">
                            <span>Charlie Holland</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-10.jpg"
                            class="rounded-circle imgtooltip" data-template="nine">
                          <span id="nine" class="d-none">
                            <span>Jamie Lusar</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm ">
                          <span class="avatar-initials rounded-circle bg-light text-dark">3+</span>
                        </span>


                      </div>

                    </div>
                    <!-- text -->
                    <div>
                      <span class="badge badge-success-soft ">Finished</span>
                    </div>

                  </div>
                  <div>
                    <!-- progress bar -->
                    <div class="d-flex justify-content-between mb-2 fs-6"> <span>Progress</span>
                      <span>100%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                      <div class="progress-bar bg-success  " role="progressbar" style="width: 100%;" aria-valuenow="100"
                        aria-valuemin="0" aria-valuemax="100">

                      </div>
                    </div>

                  </div>

                </div>
                <!-- card footer -->
                <div class="card-footer p-0">
                  <div class="d-flex justify-content-between ">

                    <div class="w-50 py-3 px-4 ">
                      <h6 class="mb-0 text-muted">Due Date:</h6>
                      <p class="text-dark fs-6  mb-0">1 Sept, 2023</p>
                    </div>
                    <div class="border-start w-50 py-3 px-4">
                      <h6 class="mb-0 text-muted">Budget:</h6>
                      <p class="text-dark fs-6  mb-0">$5,200 </p>

                    </div>
                  </div>

                </div>

              </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-5">
              <!-- card -->
              <div class="card h-100">
                <!-- card body -->
                <div class="card-body">
                  <!-- heading-->
                  <div class="d-flex align-items-center justify-content-between">
                    <!-- text-->
                    <div>
                      <h4 class="mb-0 h5"><a href="#!" class="text-inherit">Task Application Development.. </a></h4>
                      <span class="text-muted fs-6">Web Development</span>
                    </div>
                    <!-- dropdown-->
                    <div class="d-flex align-items-center">

                      <div class="dropdown dropstart">
                        <a href="#!" class="btn-icon btn btn-ghost btn-sm rounded-circle" data-bs-toggle="dropdown"
                          aria-haspopup="true" aria-expanded="false">
                          <i data-feather="more-vertical" class="icon-xs"></i>
                        </a>
                        <div class="dropdown-menu">

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="edit"></i>Edit Details
                          </a>

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="link"></i>Copy project link

                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="save"></i>Save as Default
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="copy"></i>Duplicate
                          </a>


                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="upload"></i>Import
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="printer"></i>Export / Print
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="users"></i>Move to another team
                          </a>
                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="archive"></i>Archive
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="trash"></i>Delete Project
                          </a>


                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- para-->
                  <div class="mt-3 mb-4">
                    <p class="mb-0">Mauris quis nibh eu tortor blandit lacinia id sed
                      ans dui turpis, semper ac turpis quis</p>
                  </div>

                  <!-- progress -->
                  <div class="d-flex justify-content-between
                                  align-items-center mb-5">
                    <div class="d-flex align-items-center">
                      <!-- avatar group -->
                      <div class="avatar-group">
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-5.jpg" class="rounded-circle imgtooltip"
                            data-template="eighTeen">
                          <span id="eighTeen" class="d-none">
                            <span>Gilbert Farr</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm avatar-primary imgtooltip" data-template="textTen">
                          <span class="avatar-initials rounded-circle ">

                            DU</span>

                          <span id="textTen" class="d-none">
                            <span>Dash UI Only</span>

                          </span>
                        </span>


                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-6.jpg" class="rounded-circle imgtooltip"
                            data-template="nineTeen">
                          <span id="nineTeen" class="d-none">
                            <span>Charlie Holland</span>

                          </span>
                        </span>

                        <span class="avatar avatar-sm ">
                          <span class="avatar-initials rounded-circle bg-light text-dark">6+</span>
                        </span>


                      </div>

                    </div>
                    <!-- text -->
                    <div>
                      <span class="badge badge-info-soft ">In Progress</span>
                    </div>

                  </div>
                  <div>
                    <!-- progress bar -->
                    <div class="d-flex justify-content-between mb-2 fs-6"> <span>Progress</span>
                      <span>50%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                      <div class="progress-bar bg-info " role="progressbar" style="width: 50%;" aria-valuenow="50"
                        aria-valuemin="0" aria-valuemax="100">

                      </div>
                    </div>

                  </div>

                </div>
                <!-- card footer -->
                <div class="card-footer p-0">
                  <div class="d-flex justify-content-between ">

                    <div class="w-50 py-3 px-4 ">
                      <h6 class="mb-0 text-muted">Due Date:</h6>
                      <p class="text-dark fs-6  mb-0">22 Sept, 2023</p>
                    </div>
                    <div class="border-start w-50 py-3 px-4">
                      <h6 class="mb-0 text-muted">Budget:</h6>
                      <p class="text-dark fs-6  mb-0">$400</p>

                    </div>
                  </div>

                </div>

              </div>
            </div>


            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-5">
              <!-- card -->
              <div class="card h-100">
                <!-- card body -->
                <div class="card-body">
                  <!-- heading-->
                  <div class="d-flex align-items-center
                                  justify-content-between">

                    <!-- text-->

                    <div class="d-flex align-items-center">
                      <div class="icon-shape icon-lg rounded-3 border ">
                        <i class=" icon-xs text-muted" data-feather="clipboard"> </i>
                      </div>
                      <div class="ms-3">
                        <h4 class="mb-0 h5"><a href="#!" class="text-inherit">File Manager UI Design
                          </a></h4>
                        <span class="text-muted fs-6">Web Design</span></div>


                    </div>
                    <!-- dropdown-->
                    <div class="d-flex">

                      <div class="dropdown dropstart">
                        <a href="#!" class="btn-icon btn btn-ghost btn-sm rounded-circle" data-bs-toggle="dropdown"
                          aria-haspopup="true" aria-expanded="false">
                          <i data-feather="more-vertical" class="icon-xs"></i>
                        </a>
                        <div class="dropdown-menu">

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="edit"></i>Edit Details
                          </a>

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="link"></i>Copy project link

                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="save"></i>Save as Default
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="copy"></i>Duplicate
                          </a>


                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="upload"></i>Import
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="printer"></i>Export / Print
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="users"></i>Move to another team
                          </a>
                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="archive"></i>Archive
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="trash"></i>Delete Project
                          </a>


                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- para-->
                  <div class="mt-4 mb-4">
                    <p class="mb-0">Quisque at augue convallis, tincidunt erat et,
                      tristique ssa mollis dignissim eget</p>
                  </div>

                  <!-- progress -->
                  <div class="d-flex justify-content-between
                                  align-items-center mb-5">
                    <div class="d-flex align-items-center">
                      <!-- avatar group -->
                      <div class="avatar-group">
                        <span class="avatar avatar-sm avatar-warning imgtooltip" data-template="textFour">
                          <span class="avatar-initials rounded-circle ">

                            DU</span>

                          <span id="textFour" class="d-none">
                            <span>Dash UI Only</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-12.jpg"
                            class="rounded-circle imgtooltip" data-template="eleven">
                          <span id="eleven" class="d-none">
                            <span>Charlie Holland</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-13.jpg"
                            class="rounded-circle imgtooltip" data-template="twelve">
                          <span id="twelve" class="d-none">
                            <span>Jamie Lusar</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm ">
                          <span class="avatar-initials rounded-circle bg-light text-dark">2+</span>
                        </span>


                      </div>

                    </div>
                    <!-- text -->
                    <div>
                      <span class="badge badge-info-soft ">In Progress</span>
                    </div>

                  </div>
                  <div>
                    <!-- progress bar -->
                    <div class="d-flex justify-content-between mb-2 fs-6"> <span>Progress</span>
                      <span>85%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                      <div class="progress-bar bg-info" role="progressbar" style="width: 85%;" aria-valuenow="85"
                        aria-valuemin="0" aria-valuemax="85">

                      </div>
                    </div>

                  </div>

                </div>
                <!-- card footer -->
                <div class="card-footer p-0">
                  <div class="d-flex justify-content-between ">
                    <div class="w-50 py-3 px-4 ">
                      <h6 class="mb-0 text-muted">Due Date:</h6>
                      <p class="text-dark fs-6  mb-0">25 Jan, 2023</p>
                    </div>
                    <div class="border-start w-50 py-3 px-4">
                      <h6 class="mb-0 text-muted">Budget:</h6>
                      <p class="text-dark fs-6  mb-0">$2,000 </p>

                    </div>
                  </div>

                </div>

              </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-5">
              <!-- card -->
              <div class="card h-100">
                <!-- card body -->
                <div class="card-body">
                  <!-- heading-->
                  <div class="d-flex align-items-center
                                  justify-content-between">

                    <!-- text-->

                    <div class="d-flex align-items-center">
                      <div class="icon-shape icon-lg rounded-3 border ">
                        <i class=" icon-xs text-muted" data-feather="message-square"> </i>
                      </div>
                      <div class="ms-3">
                        <h4 class="mb-0 h5"><a href="#!" class="text-inherit">Chat Application Design
                          </a></h4>
                        <span class="text-muted fs-6">Web Design</span>


                      </div>


                    </div>
                    <!-- dropdown-->
                    <div class="d-flex">

                      <div class="dropdown dropstart">
                        <a href="#!" class="btn-icon btn btn-ghost btn-sm rounded-circle" data-bs-toggle="dropdown"
                          aria-haspopup="true" aria-expanded="false">
                          <i data-feather="more-vertical" class="icon-xs"></i>
                        </a>
                        <div class="dropdown-menu">

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="edit"></i>Edit Details
                          </a>

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="link"></i>Copy project link

                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="save"></i>Save as Default
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="copy"></i>Duplicate
                          </a>


                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="upload"></i>Import
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="printer"></i>Export / Print
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="users"></i>Move to another team
                          </a>
                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="archive"></i>Archive
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="trash"></i>Delete Project
                          </a>


                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- para-->
                  <div class="mt-4 mb-4">


                    <p class="mb-0">Nam gravida vestibulum justo, ac aliquet erat.
                      Pellentesque vitae massa lacus.
                    </p>
                  </div>

                  <!-- progress -->
                  <div class="d-flex justify-content-between
                                  align-items-center mb-5">
                    <div class="d-flex align-items-center">
                      <!-- avatar group -->
                      <div class="avatar-group">
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-12.jpg"
                            class="rounded-circle imgtooltip" data-template="thirteen">
                          <span id="thirteen" class="d-none">
                            <span>Gilbert Farr</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-13.jpg"
                            class="rounded-circle imgtooltip" data-template="fourteen">
                          <span id="fourteen" class="d-none">
                            <span>Charlie Holland</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm avatar-success imgtooltip" data-template="textThree">
                          <span class="avatar-initials rounded-circle ">

                            DU</span>

                          <span id="textThree" class="d-none">
                            <span>Dash UI Only</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm ">
                          <span class="avatar-initials rounded-circle bg-light text-dark">4+</span>
                        </span>


                      </div>

                    </div>
                    <!-- text -->
                    <div>
                      <span class="badge badge-warning-soft ">Pending</span>
                    </div>

                  </div>
                  <div>
                    <!-- progress bar -->
                    <div class="d-flex justify-content-between mb-2 fs-6"> <span>Progress</span>
                      <span>95%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                      <div class="progress-bar bg-warning" role="progressbar" style="width: 95%;" aria-valuenow="95"
                        aria-valuemin="0" aria-valuemax="95">

                      </div>
                    </div>

                  </div>

                </div>
                <!-- card footer -->
                <div class="card-footer p-0">
                  <div class="d-flex justify-content-between ">

                    <div class="w-50 py-3 px-4 ">
                      <h6 class="mb-0 text-muted">Due Date:</h6>
                      <p class="text-dark fs-6  mb-0">30 May, 2023</p>
                    </div>
                    <div class="border-start w-50 py-3 px-4">
                      <h6 class="mb-0 text-muted">Budget:</h6>
                      <p class="text-dark fs-6  mb-0">$800 </p>

                    </div>
                  </div>

                </div>

              </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-5">
              <!-- card -->
              <div class="card h-100">
                <!-- card body -->
                <div class="card-body">
                  <!-- heading-->
                  <div class="d-flex align-items-center
                                  justify-content-between">

                    <!-- text-->

                    <div class="d-flex align-items-center">
                      <div class="icon-shape icon-lg rounded-3 border ">
                        <i class=" icon-xs text-muted" data-feather="shopping-cart"> </i>
                      </div>
                      <div class="ms-3">
                        <h4 class="mb-0 h5"><a href="#!" class="text-inherit">E-Commerce Project
                          </a></h4>
                        <span class="text-muted fs-6">Web Development</span>


                      </div>


                    </div>
                    <!-- dropdown-->
                    <div class="d-flex">

                      <div class="dropdown dropstart">
                        <a href="#!" class="btn-icon btn btn-ghost btn-sm rounded-circle" data-bs-toggle="dropdown"
                          aria-haspopup="true" aria-expanded="false">
                          <i data-feather="more-vertical" class="icon-xs"></i>
                        </a>
                        <div class="dropdown-menu">

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="edit"></i>Edit Details
                          </a>

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="link"></i>Copy project link

                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="save"></i>Save as Default
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="copy"></i>Duplicate
                          </a>


                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="upload"></i>Import
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="printer"></i>Export / Print
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="users"></i>Move to another team
                          </a>
                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="archive"></i>Archive
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="trash"></i>Delete Project
                          </a>


                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- para-->
                  <div class="mt-4 mb-4">

                    <p class="mb-0">Donec vel tellus nec purus mollis consequat sed
                      at urna. In sit amet vehicula odio.
                    </p>
                  </div>

                  <!-- progress -->
                  <div class="d-flex justify-content-between
                                  align-items-center mb-5">
                    <div class="d-flex align-items-center">
                      <!-- avatar group -->
                      <div class="avatar-group">
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-15.jpg"
                            class="rounded-circle imgtooltip" data-template="sixteen">
                          <span id="sixteen" class="d-none">
                            <span>Gilbert Farr</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm avatar-danger imgtooltip" data-template="textFive">
                          <span class="avatar-initials rounded-circle ">

                            DU</span>

                          <span id="textFive" class="d-none">
                            <span>Dash UI Only</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-17.jpg"
                            class="rounded-circle imgtooltip" data-template="eighteen">
                          <span id="eighteen" class="d-none">
                            <span>Jamie Lusar</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm ">
                          <span class="avatar-initials rounded-circle bg-light text-dark">5+</span>
                        </span>


                      </div>

                    </div>
                    <!-- text -->
                    <div>
                      <span class="badge badge-success-soft ">Finished</span>
                    </div>

                  </div>
                  <div>
                    <!-- progress bar -->
                    <div class="d-flex justify-content-between mb-2 fs-6"> <span>Progress</span>
                      <span>100%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                      <div class="progress-bar bg-success" role="progressbar" style="width: 100%;" aria-valuenow="100"
                        aria-valuemin="0" aria-valuemax="100">

                      </div>
                    </div>

                  </div>

                </div>
                <!-- card footer -->
                <div class="card-footer p-0">
                  <div class="d-flex justify-content-between ">

                    <div class="w-50 py-3 px-4 ">
                      <h6 class="mb-0 text-muted">Due Date:</h6>
                      <p class="text-dark fs-6  mb-0">31 June, 2023</p>
                    </div>
                    <div class="border-start w-50 py-3 px-4">
                      <h6 class="mb-0 text-muted">Budget:</h6>
                      <p class="text-dark fs-6  mb-0">$2,53,000 </p>

                    </div>
                  </div>

                </div>

              </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-5">
              <!-- card -->
              <div class="card h-100">
                <!-- card body -->
                <div class="card-body">
                  <!-- heading-->
                  <div class="d-flex align-items-center
                                  justify-content-between">

                    <!-- text-->

                    <div class="d-flex align-items-center">
                      <div class="icon-shape icon-lg rounded-3 border ">
                        <i class=" icon-xs text-muted" data-feather="message-square"> </i>
                      </div>
                      <div class="ms-3">
                        <h4 class="mb-0 h5"><a href="#!" class="text-inherit">Chat Application Design
                          </a></h4>
                        <span class="text-muted fs-6">Web Design</span>


                      </div>


                    </div>
                    <!-- dropdown-->
                    <div class="d-flex">

                      <div class="dropdown dropstart">
                        <a href="#!" class="btn-icon btn btn-ghost btn-sm rounded-circle" data-bs-toggle="dropdown"
                          aria-haspopup="true" aria-expanded="false">
                          <i data-feather="more-vertical" class="icon-xs"></i>
                        </a>
                        <div class="dropdown-menu">

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="edit"></i>Edit Details
                          </a>

                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="link"></i>Copy project link

                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="save"></i>Save as Default
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="copy"></i>Duplicate
                          </a>


                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="upload"></i>Import
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class=" dropdown-item-icon" data-feather="printer"></i>Export / Print
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="users"></i>Move to another team
                          </a>
                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="archive"></i>Archive
                          </a>
                          <a class="dropdown-item d-flex align-items-center" href="#!">
                            <i class="dropdown-item-icon" data-feather="trash"></i>Delete Project
                          </a>


                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- para-->
                  <div class="mt-4 mb-4">


                    <p class="mb-0">Nam gravida vestibulum justo, ac aliquet erat.
                      Pellentesque vitae massa lacus.
                    </p>
                  </div>

                  <!-- progress -->
                  <div class="d-flex justify-content-between
                                  align-items-center mb-5">
                    <div class="d-flex align-items-center">
                      <!-- avatar group -->
                      <div class="avatar-group">
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-12.jpg"
                            class="rounded-circle imgtooltip" data-template="twenty">
                          <span id="twenty" class="d-none">
                            <span>Gilbert Farr</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-13.jpg"
                            class="rounded-circle imgtooltip" data-template="twentyone">
                          <span id="twentyone" class="d-none">
                            <span>Charlie Holland</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm avatar-success imgtooltip" data-template="textTwelve">
                          <span class="avatar-initials rounded-circle ">

                            DU</span>

                          <span id="textTwelve" class="d-none">
                            <span>Dash UI Only</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm ">
                          <span class="avatar-initials rounded-circle bg-light text-dark">4+</span>
                        </span>


                      </div>

                    </div>
                    <!-- text -->
                    <div>
                      <span class="badge badge-warning-soft ">Pending</span>
                    </div>

                  </div>
                  <div>
                    <!-- progress bar -->
                    <div class="d-flex justify-content-between mb-2 fs-6"> <span>Progress</span>
                      <span>95%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                      <div class="progress-bar bg-warning" role="progressbar" style="width: 95%;" aria-valuenow="95"
                        aria-valuemin="0" aria-valuemax="95">

                      </div>
                    </div>

                  </div>

                </div>
                <!-- card footer -->
                <div class="card-footer p-0">
                  <div class="d-flex justify-content-between ">

                    <div class="w-50 py-3 px-4 ">
                      <h6 class="mb-0 text-muted">Due Date:</h6>
                      <p class="text-dark fs-6  mb-0">30 May, 2023</p>
                    </div>
                    <div class="border-start w-50 py-3 px-4">
                      <h6 class="mb-0 text-muted">Budget:</h6>
                      <p class="text-dark fs-6  mb-0">$800 </p>

                    </div>
                  </div>

                </div>

              </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-5">
              <!-- card -->
              <div class="card h-100">
                <img src="../assets/images/background/project-cover-img.jpg" alt="Image" class="img-fluid rounded-top">
                <div class="d-flex position-absolute end-0 pe-3 pt-3">

                  <div class="dropdown dropstart">
                    <a href="#!" class="btn-icon btn btn-ghost btn-sm rounded-circle" data-bs-toggle="dropdown"
                      aria-haspopup="true" aria-expanded="false">
                      <i data-feather="more-vertical" class="icon-xs"></i>
                    </a>
                    <div class="dropdown-menu">

                      <a class="dropdown-item d-flex align-items-center" href="#!">
                        <i class=" dropdown-item-icon" data-feather="edit"></i>Edit Details
                      </a>

                      <a class="dropdown-item d-flex align-items-center" href="#!">
                        <i class="dropdown-item-icon" data-feather="link"></i>Copy project link

                      </a>
                      <a class="dropdown-item d-flex align-items-center" href="#!">
                        <i class="dropdown-item-icon" data-feather="save"></i>Save as Default
                      </a>
                      <a class="dropdown-item d-flex align-items-center" href="#!">
                        <i class="dropdown-item-icon" data-feather="copy"></i>Duplicate
                      </a>


                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item d-flex align-items-center" href="#!">
                        <i class="dropdown-item-icon" data-feather="upload"></i>Import
                      </a>
                      <a class="dropdown-item d-flex align-items-center" href="#!">
                        <i class=" dropdown-item-icon" data-feather="printer"></i>Export / Print
                      </a>
                      <a class="dropdown-item d-flex align-items-center" href="#!">
                        <i class="dropdown-item-icon" data-feather="users"></i>Move to another team
                      </a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item d-flex align-items-center" href="#!">
                        <i class="dropdown-item-icon" data-feather="archive"></i>Archive
                      </a>
                      <a class="dropdown-item d-flex align-items-center" href="#!">
                        <i class="dropdown-item-icon" data-feather="trash"></i>Delete Project
                      </a>


                    </div>
                  </div>
                </div>
                <!-- card body -->
                <div class="card-body">
                  <!-- heading-->


                  <!-- text-->



                  <div class="mb-4">
                    <h4 class="mb-0 h5"><a href="#!" class="text-inherit">CRM Dashboard
                      </a></h4>
                    <span class="text-muted fs-6">Front End Development</span>


                  </div>




                  <!-- progress -->
                  <div class="d-flex justify-content-between
                                  align-items-center mb-5">
                    <div class="d-flex align-items-center">
                      <!-- avatar group -->
                      <div class="avatar-group">
                        <span class="avatar avatar-sm avatar-success imgtooltip" data-template="textThirteen">
                          <span class="avatar-initials rounded-circle ">

                            DU</span>

                          <span id="textThirteen" class="d-none">
                            <span>Dash UI Only</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-13.jpg"
                            class="rounded-circle imgtooltip" data-template="fifteen">
                          <span id="fifteen" class="d-none">
                            <span>Charlie Holland</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm">
                          <img alt="avatar" src="../assets/images/avatar/avatar-14.jpg"
                            class="rounded-circle imgtooltip" data-template="seventeen">
                          <span id="seventeen" class="d-none">
                            <span>Jamie Lusar</span>

                          </span>
                        </span>
                        <span class="avatar avatar-sm ">
                          <span class="avatar-initials rounded-circle bg-light text-dark">9+</span>
                        </span>


                      </div>

                    </div>
                    <!-- text -->
                    <div>
                      <span class="badge badge-success-soft ">Finished</span>
                    </div>

                  </div>
                  <div>
                    <!-- progress bar -->
                    <div class="d-flex justify-content-between mb-2 fs-6"> <span>Progress</span>
                      <span>100%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                      <div class="progress-bar bg-success" role="progressbar" style="width: 100%;" aria-valuenow="100"
                        aria-valuemin="0" aria-valuemax="100">

                      </div>
                    </div>

                  </div>

                </div>
                <!-- card footer -->
                <div class="card-footer p-0">
                  <div class="d-flex justify-content-between ">

                    <div class="w-50 py-3 px-4 ">
                      <h6 class="mb-0 text-muted">Due Date:</h6>
                      <p class="text-dark fs-6  mb-0">1 Sept, 2023</p>
                    </div>
                    <div class="border-start w-50 py-3 px-4">
                      <h6 class="mb-0 text-muted">Budget:</h6>
                      <p class="text-dark fs-6  mb-0">$5,200 </p>

                    </div>
                  </div>

                </div>

              </div>
            </div>

            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-5">
              <a href="#!" data-bs-toggle="offcanvas" data-bs-target="#offcanvasRight">
                <div class="card h-100 border border-2 shadow-none card-dashed-hover p-12">
                  <div class="card-body d-flex flex-column justify-content-center text-center">
                    <i class="mdi mdi-plus text-secondary mb-3 fs-2" aria-hidden="true"></i>
                  </div>
                </div>
              </a>
            </div>

          </div>

        </div>
      </div>
    </div>
  </main>

  <!-- Offcanvas -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRight" style="width: 600px;">

    <div class="offcanvas-body" data-simplebar>
      <div class="offcanvas-header px-2 pt-0">
        <h3 class="offcanvas-title" id="offcanvasExampleLabel">Create Project</h3>
        <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>

      <!-- card body -->
      <div class="container">
        <!-- form -->
        <div class="row">
          <!-- form group -->
          <div class="mb-3 col-12">
            <label class="form-label">Name <span class="text-danger">*</span></label>
            <input type="text" class="form-control" placeholder="Enter project title" required>
          </div>
          <!-- form group -->
          <div class="mb-3 col-12">
            <label class="form-label">Description</label>
            <div id="editor">

              <p> Type something here
              </p>
              <br>
              <br>
              <br>


            </div>
          </div>
          <!-- form group -->
          <div class="mb-3 col-md-6 col-12">
            <label class="form-label">Start Date <span class="text-danger">*</span></label>
            <div class="input-group me-3 flatpickr rounded">
              <input class="form-control " type="text" placeholder="Select Date" aria-describedby="basic-addon2">

              <span class="input-group-text text-muted" id="basic-addon2"><i data-feather="calendar"
                  class="icon-xs"></i></span>

            </div>
          </div>
          <!-- form group -->
          <div class="mb-3 col-md-6 col-12">
            <label class="form-label">End Date <span class="text-danger">*</span></label>
            <div class="input-group me-3 flatpickr rounded">
              <input class="form-control " type="text" placeholder="Select Date" aria-describedby="basic-addon3">

              <span class="input-group-text text-muted" id="basic-addon3"><i data-feather="calendar"
                  class="icon-xs"></i></span>

            </div>
          </div>
          <!-- form group -->
          <div class="mb-4 col-md-6 col-12">
            <label class="form-label">Priority
            </label>
            <select class="form-select">
              <option value="">Set Priority</option>
              <option value="High">High</option>
              <option value="Medium">Medium</option>
              <option value="Low">Low</option>
            </select>
          </div>
          <!-- form group -->
          <div class="mb-4 col-md-6 col-12">
            <label class="form-label">Status</label>
            <select class="form-select">
              <option value="">In Progress</option>
              <option value="Based on Project Amount">Based on Project Amount</option>
              <option value="Based on Project Hours">Based on Project Hours</option>
            </select>
          </div>






          <div class="col-12 mb-4">
            <h5 class="mb-2">Cover Image </h5>

            <form action="#" class="d-block dropzone border-dashed min-h-0 rounded-2">
              <div class="fallback">
                <input name="file" type="file" multiple>
              </div>
            </form>


          </div>
          <div class="col-md-8"></div>
          <!-- button -->
          <div class="col-12">
            <button class="btn btn-primary" type="button">Submit</button>
            <button type="button" class="btn btn-outline-primary ms-2" data-bs-dismiss="offcanvas"
              aria-label="Close">Close</button>
          </div>
        </div>

      </div>
    </div>
  </div>
  <!-- Scripts -->

  <!-- dropzone -->
  <script src="../assets/libs/dropzone/dist/min/dropzone.min.js"></script>
  <!-- flatpickr -->
  <script src="../assets/libs/flatpickr/dist/flatpickr.min.js"></script>
  <!-- quill js -->
  <script src="../assets/libs/quill/dist/quill.min.js"></script>

  <!-- Libs JS -->

<script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../assets/libs/feather-icons/dist/feather.min.js"></script>
<script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>

<!-- Theme JS -->
<script src="../assets/js/theme.min.js"></script>

  <!-- popper js -->
  <script src="../assets/libs/%40popperjs/core/dist/umd/popper.min.js"></script>
  <!-- tippy js -->
  <script src="../assets/libs/tippy.js/dist/tippy-bundle.umd.min.js"></script>
  <script src="../assets/js/vendors/tooltip.js"></script>


</body>


<!-- Mirrored from dashui.codescandy.com/dashuipro/pages/project-grid.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:29:44 GMT -->
</html>