# Criminal Profile Management Task

## Overview
Implement a comprehensive criminal profile management system that allows law enforcement officers to create, manage, and track detailed criminal profiles including personal information, biometric data, arrest history, and case associations.

## Requirements (from System-Specification-Document.md)

### Personal Details
- **Name**: Full name of the suspect (e.g., <PERSON><PERSON><PERSON>)
- **Age**: Age of the suspect (numeric, optional)
- **Village**: Village of residence (e.g., Makuta)
- **Traditional Authority (T/A)**: Traditional Authority area (e.g., Malengachanzi)
- **District**: District of residence (e.g., Nkhotakota District)
- **Occupation**: Suspect's occupation (e.g., Driver)
- **Address**: Postal address in Malawi format (e.g., P.O. Box 456, Blantyre)
- **Phone Number**: Malawian phone number (e.g., +265997626306)
- **List of Relatives**: Names and relationships (e.g., brother, sister, parents, cousins)

### Biometric Data
- **Fingerprint**: Stored as digital file or reference ID
- **DNA**: Stored as reference ID or analysis result
- **Voice Recognition**: Stored as reference ID or file
- **Face Recognition**: Stored as reference ID or image

### Gang Affiliation and Special Instructions
- **Gang Affiliation**: Known gang affiliations (text field, optional)
- **Special Instructions**: Medical conditions, allergies, or other relevant notes (text field, optional)

### Arrest and Charge Details
- **Date of Arrest**: Date in DD/MM/YYYY format
- **Time of Arrest**: Time in HH:MM (24-hour format)
- **Location of Arrest**: District or specific location (e.g., Mchinji District)
- **Other Charges**: List of additional charges (e.g., theft, assault)
- **Other Offenses**: List of related offenses (e.g., parole violations)

### Case History
- **Case Number**: Unique identifier (alphanumeric)
- **Case Status**: Status of the case (e.g., Pending, Ongoing, Closed)
- **Court Appearances**: List of court appearances with:
  - Date (DD/MM/YYYY)
  - Time (HH:MM)
  - Court Name (e.g., Lilongwe Magistrates Court)
- **Bail Status**: Status of bail (e.g., Granted, Denied, Pending)
  - Bail Amount (if applicable, in MWK)
  - Bail Conditions (e.g., curfew, check-ins)

### Additional Information
- **Previous Convictions**: List of prior convictions with:
  - Case Number
  - Date of Conviction (DD/MM/YYYY)
  - Offense
  - Sentence
- **Known Associates**: List of associates with:
  - Name
  - Relationship (e.g., friend, accomplice)
  - Contact Information (optional)

### Witnesses
- **Witness Name**: Full name of the witness
- **Statement**: Text of the witness's statement
- **Date and Time**: When the statement was recorded (DD/MM/YYYY HH:MM)
- **Contact Information**: Phone number or address (optional)

### Next Steps
- **Court Hearing**: Date and time of the next hearing (DD/MM/YYYY HH:MM)
- **Trial Date**: Scheduled trial date (DD/MM/YYYY)
- **Sentence**: Details of sentence (e.g., 5 years imprisonment)
- **Appeal Status**: Status of appeal (e.g., Pending, Denied, Granted)

## Technical Implementation

### 1. Database Schema

#### Criminals Table
```sql
CREATE TABLE criminals (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    criminal_number VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    middle_name VARCHAR(100),
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE,
    age INT,
    gender ENUM('Male', 'Female', 'Other'),
    village VARCHAR(100),
    traditional_authority VARCHAR(100),
    district VARCHAR(100),
    occupation VARCHAR(100),
    address TEXT,
    phone_number VARCHAR(20),
    email VARCHAR(100),
    nationality VARCHAR(50) DEFAULT 'Malawian',
    id_number VARCHAR(50),
    passport_number VARCHAR(50),
    height DECIMAL(5,2),
    weight DECIMAL(5,2),
    eye_color VARCHAR(20),
    hair_color VARCHAR(20),
    distinguishing_marks TEXT,
    risk_level ENUM('Low', 'Medium', 'High', 'Critical') DEFAULT 'Low',
    status ENUM('Active', 'Inactive', 'Deceased', 'Deported') DEFAULT 'Active',
    created_by BIGINT,
    updated_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);
```

#### Criminal Arrests Table
```sql
CREATE TABLE criminal_arrests (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    criminal_id BIGINT NOT NULL,
    arrest_date DATE NOT NULL,
    arrest_time TIME,
    arrest_location TEXT,
    arresting_officer_id BIGINT,
    arrest_reason TEXT,
    charges TEXT,
    other_offenses TEXT,
    arrest_report_number VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (criminal_id) REFERENCES criminals(id) ON DELETE CASCADE,
    FOREIGN KEY (arresting_officer_id) REFERENCES users(id)
);
```

#### Biometric Data Table
```sql
CREATE TABLE criminal_biometrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    criminal_id BIGINT NOT NULL,
    biometric_type ENUM('Fingerprint', 'DNA', 'Voice', 'Face', 'Photo') NOT NULL,
    file_path VARCHAR(500),
    file_hash VARCHAR(255),
    metadata JSON,
    collected_date DATE,
    collected_by BIGINT,
    verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (criminal_id) REFERENCES criminals(id) ON DELETE CASCADE,
    FOREIGN KEY (collected_by) REFERENCES users(id)
);
```

#### Criminal Relationships Table
```sql
CREATE TABLE criminal_relationships (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    criminal_id BIGINT NOT NULL,
    related_person_name VARCHAR(200),
    relationship_type ENUM('Family', 'Associate', 'Gang', 'Witness', 'Victim') NOT NULL,
    relationship_detail VARCHAR(100),
    contact_info VARCHAR(200),
    notes TEXT,
    verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (criminal_id) REFERENCES criminals(id) ON DELETE CASCADE
);
```

#### Medical Information Table
```sql
CREATE TABLE criminal_medical_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    criminal_id BIGINT NOT NULL,
    medical_condition VARCHAR(200),
    allergies TEXT,
    medications TEXT,
    special_instructions TEXT,
    last_updated DATE,
    updated_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (criminal_id) REFERENCES criminals(id) ON DELETE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);
```

### 2. Laravel Models

#### Criminal Model
```php
class Criminal extends Model
{
    protected $fillable = [
        'criminal_number', 'first_name', 'middle_name', 'last_name',
        'date_of_birth', 'age', 'gender', 'village', 'traditional_authority',
        'district', 'occupation', 'address', 'phone_number', 'email',
        'nationality', 'id_number', 'passport_number', 'height', 'weight',
        'eye_color', 'hair_color', 'distinguishing_marks', 'risk_level', 'status'
    ];

    protected $dates = ['date_of_birth'];

    // Relationships
    public function arrests() {
        return $this->hasMany(CriminalArrest::class);
    }

    public function biometrics() {
        return $this->hasMany(CriminalBiometric::class);
    }

    public function relationships() {
        return $this->hasMany(CriminalRelationship::class);
    }

    public function medicalInfo() {
        return $this->hasOne(CriminalMedicalInfo::class);
    }

    public function cases() {
        return $this->belongsToMany(CriminalCase::class, 'case_criminals');
    }

    // Accessors
    public function getFullNameAttribute() {
        return trim($this->first_name . ' ' . $this->middle_name . ' ' . $this->last_name);
    }

    public function getAgeFromDobAttribute() {
        return $this->date_of_birth ? $this->date_of_birth->age : $this->age;
    }

    // Scopes
    public function scopeActive($query) {
        return $query->where('status', 'Active');
    }

    public function scopeHighRisk($query) {
        return $query->whereIn('risk_level', ['High', 'Critical']);
    }
}
```

### 3. Controllers

#### CriminalController
```php
class CriminalController extends Controller
{
    public function index(Request $request) {
        // List all criminals with search and filtering
    }

    public function create() {
        // Show create criminal form
    }

    public function store(CreateCriminalRequest $request) {
        // Create new criminal profile
    }

    public function show(Criminal $criminal) {
        // Display criminal profile details
    }

    public function edit(Criminal $criminal) {
        // Show edit criminal form
    }

    public function update(UpdateCriminalRequest $request, Criminal $criminal) {
        // Update criminal profile
    }

    public function destroy(Criminal $criminal) {
        // Soft delete criminal profile
    }

    public function search(Request $request) {
        // Advanced search functionality
    }

    public function uploadBiometric(Request $request, Criminal $criminal) {
        // Upload biometric data
    }

    public function addRelationship(Request $request, Criminal $criminal) {
        // Add relationship/associate
    }
}
```

### 4. Form Requests

#### CreateCriminalRequest
```php
class CreateCriminalRequest extends FormRequest
{
    public function rules() {
        return [
            'first_name' => 'required|string|max:100',
            'last_name' => 'required|string|max:100',
            'middle_name' => 'nullable|string|max:100',
            'date_of_birth' => 'nullable|date|before:today',
            'age' => 'nullable|integer|min:1|max:120',
            'gender' => 'required|in:Male,Female,Other',
            'village' => 'nullable|string|max:100',
            'traditional_authority' => 'nullable|string|max:100',
            'district' => 'nullable|string|max:100',
            'occupation' => 'nullable|string|max:100',
            'phone_number' => 'nullable|regex:/^\+265[0-9]{9}$/',
            'email' => 'nullable|email|max:100',
            'height' => 'nullable|numeric|min:0|max:300',
            'weight' => 'nullable|numeric|min:0|max:500',
            'risk_level' => 'required|in:Low,Medium,High,Critical'
        ];
    }
}
```

## User Interface Implementation

### 1. Criminal Profile List View
**Template**: `resources/views/criminals/index.blade.php`

**Features**:
- Searchable and sortable table
- Filter by district, risk level, status
- Pagination for large datasets
- Quick actions (view, edit, delete)
- Export functionality

**Search Capabilities**:
- Name search (fuzzy matching)
- Criminal number search
- Phone number search
- Address search
- Biometric search (if available)

### 2. Criminal Profile Detail View
**Template**: `resources/views/criminals/show.blade.php`

**Sections**:
- **Personal Information**: Basic details with photo
- **Arrest History**: Timeline of arrests
- **Biometric Data**: Fingerprints, photos, DNA status
- **Relationships**: Family and associates
- **Case History**: Associated cases and convictions
- **Medical Information**: Health conditions and notes
- **Activity Log**: Recent profile changes

### 3. Criminal Profile Form
**Template**: `resources/views/criminals/create.blade.php` & `edit.blade.php`

**Form Sections**:
- **Personal Details**: Multi-step form with validation
- **Address Information**: Malawi-specific location fields
- **Physical Description**: Height, weight, distinguishing marks
- **Contact Information**: Phone, email with validation
- **Risk Assessment**: Risk level with justification
- **Biometric Upload**: File upload for photos and documents

### 4. Advanced Search Interface
**Template**: `resources/views/criminals/search.blade.php`

**Search Options**:
- **Basic Search**: Name, criminal number
- **Advanced Search**: Multiple criteria combination
- **Biometric Search**: Photo comparison (future feature)
- **Location Search**: District, T/A, village
- **Case Association**: Search by case number

## Implementation Steps

### Phase 1: Basic CRUD Operations
1. **Database Setup**
   - Create migration files for all tables
   - Set up model relationships
   - Create seeders with sample Malawi data

2. **Basic Controllers and Routes**
   - Implement CRUD operations
   - Set up form validation
   - Create basic Blade templates

3. **List and Detail Views**
   - Criminal list with search and pagination
   - Detailed profile view
   - Basic edit functionality

### Phase 2: Advanced Features
1. **Biometric Data Management**
   - File upload system for photos and documents
   - Image processing and storage
   - Biometric data display components

2. **Relationship Management**
   - Add/edit relationships and associates
   - Family tree visualization
   - Associate network mapping

3. **Search and Filtering**
   - Advanced search functionality
   - Multiple filter combinations
   - Search result export

### Phase 3: Integration and Enhancement
1. **Case Integration**
   - Link criminals to cases
   - Case history display
   - Cross-referencing with evidence

2. **Reporting and Analytics**
   - Criminal statistics reports
   - Risk assessment analytics
   - Geographic distribution analysis

3. **Security and Audit**
   - Access control implementation
   - Audit trail for profile changes
   - Data encryption for sensitive information

## Sample Data for Malawi Context

### Districts
- Lilongwe, Blantyre, Mzuzu, Zomba, Kasungu, Mangochi, Salima, Dedza, Ntchisi, Nkhotakota

### Traditional Authorities (Sample)
- Malengachanzi (Nkhotakota)
- Kalolo (Lilongwe)
- Lundu (Chikwawa)
- Kyungu (Karonga)

### Sample Criminal Profiles
Using names from overview.md:
- Chisomo Phiri (Driver, Makuta Village, Nkhotakota)
- Kondwani Banda (Various occupations)
- Chikondi Mwale (Various locations)

## Security Considerations

### Data Protection
- Encrypt biometric data at rest
- Secure file storage for photos and documents
- Access logging for sensitive data
- Regular data backup and recovery

### Access Control
- Role-based permissions for viewing/editing
- Audit trail for all profile changes
- Secure API endpoints
- Input validation and sanitization

## Testing Requirements

### Unit Tests
- Model relationships and methods
- Form validation rules
- Search functionality
- File upload handling

### Integration Tests
- CRUD operations end-to-end
- Search and filtering accuracy
- Biometric data upload and retrieval
- Cross-module integration

### User Acceptance Tests
- Profile creation workflow
- Search and discovery
- Data accuracy and completeness
- Performance with large datasets

## Performance Considerations

### Database Optimization
- Indexes on frequently searched fields
- Full-text search for name fields
- Efficient pagination queries
- Image optimization and CDN usage

### Caching Strategy
- Cache frequently accessed profiles
- Search result caching
- Image thumbnail caching
- Session-based filter caching

## Deliverables
1. Complete database schema with migrations
2. Laravel models with relationships
3. CRUD controllers with validation
4. Responsive Blade templates
5. Advanced search functionality
6. Biometric data management system
7. Security implementation
8. Test suite with good coverage
9. Sample data seeders
10. Documentation and user guide

## Timeline Estimate
- **Phase 1**: 6-8 days
- **Phase 2**: 5-7 days
- **Phase 3**: 4-5 days
- **Total**: 15-20 days

## Dependencies
- Completion of UI template integration
- Database schema design approval
- File storage configuration
- Sample Malawi location data
- User authentication system