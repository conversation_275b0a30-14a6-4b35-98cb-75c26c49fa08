@extends('layouts.app')

@section('title', 'Analytics  - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Analytics Dashboard</h1>
            <x-breadcrumb :items="[
                ['title' => 'Reports'],
                ['title' => 'Analytics']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <select class="form-select" style="width: auto;">
                <option value="7days">Last 7 Days</option>
                <option value="30days" selected>Last 30 Days</option>
                <option value="90days">Last 90 Days</option>
                <option value="1year">Last Year</option>
            </select>
            <a href="#!" class="btn btn-primary">
                <i data-feather="refresh-cw" class="icon-xs me-2"></i>
                Refresh
            </a>
        </div>
    </div>

    <!-- Analytics Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i data-feather="trending-up" class="icon-lg text-success"></i>
                        </div>
                        <div>
                            <h4 class="mb-0">+15%</h4>
                            <p class="mb-0 small">Case Resolution Rate</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i data-feather="clock" class="icon-lg text-info"></i>
                        </div>
                        <div>
                            <h4 class="mb-0">12.5</h4>
                            <p class="mb-0 small">Avg Days to Solve</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i data-feather="users" class="icon-lg text-warning"></i>
                        </div>
                        <div>
                            <h4 class="mb-0">45</h4>
                            <p class="mb-0 small">Active Officers</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i data-feather="target" class="icon-lg text-danger"></i>
                        </div>
                        <div>
                            <h4 class="mb-0">89%</h4>
                            <p class="mb-0 small">Target Achievement</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Crime Pattern Analysis -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i data-feather="activity" class="icon-sm me-2"></i>
                        Crime Pattern Analysis
                    </h4>
                </div>
                <div class="card-body">
                    <canvas id="crimePatternChart" height="300"></canvas>
                </div>
            </div>

            <!-- Predictive Analytics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i data-feather="trending-up" class="icon-sm me-2"></i>
                        Predictive Crime Trends
                    </h4>
                </div>
                <div class="card-body">
                    <canvas id="predictiveChart" height="250"></canvas>
                    <div class="mt-3">
                        <div class="alert alert-info">
                            <i data-feather="info" class="icon-sm me-2"></i>
                            <strong>Prediction:</strong> Based on current trends, we expect a 8% increase in theft cases next month.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Geographic Heat Map -->
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i data-feather="map" class="icon-sm me-2"></i>
                        Crime Geographic Distribution
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <canvas id="geoChart" height="300"></canvas>
                        </div>
                        <div class="col-md-6">
                            <h6>High Crime Areas</h6>
                            <div class="list-group">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>Lilongwe City Center</span>
                                    <span class="badge bg-danger">High Risk</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>Blantyre Market Area</span>
                                    <span class="badge bg-warning">Medium Risk</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>Mzuzu Bus Station</span>
                                    <span class="badge bg-warning">Medium Risk</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>Zomba University</span>
                                    <span class="badge bg-success">Low Risk</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Time-based Analysis -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="clock" class="icon-sm me-2"></i>
                        Crime Time Patterns
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Peak Crime Hours</h6>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>18:00 - 22:00</span>
                            <span><strong>35%</strong></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-danger" style="width: 35%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>22:00 - 02:00</span>
                            <span><strong>28%</strong></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: 28%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>14:00 - 18:00</span>
                            <span><strong>22%</strong></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: 22%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>06:00 - 14:00</span>
                            <span><strong>15%</strong></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 15%"></div>
                        </div>
                    </div>

                    <h6 class="mt-4">Peak Crime Days</h6>
                    <div class="row text-center">
                        <div class="col-4">
                            <h6 class="text-danger">Friday</h6>
                            <small>25% of crimes</small>
                        </div>
                        <div class="col-4">
                            <h6 class="text-warning">Saturday</h6>
                            <small>22% of crimes</small>
                        </div>
                        <div class="col-4">
                            <h6 class="text-info">Sunday</h6>
                            <small>18% of crimes</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resource Allocation -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="pie-chart" class="icon-sm me-2"></i>
                        Resource Allocation
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="resourceChart" height="200"></canvas>
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>Recommendation:</strong> Increase patrol units in Lilongwe City Center during evening hours.
                        </small>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i data-feather="bar-chart" class="icon-sm me-2"></i>
                        Performance Metrics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h5 class="text-success">92%</h5>
                            <small class="text-muted">Response Time Target</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h5 class="text-info">78%</h5>
                            <small class="text-muted">Case Closure Rate</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning">85%</h5>
                            <small class="text-muted">Evidence Collection</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-primary">91%</h5>
                            <small class="text-muted">Court Conviction</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Crime Pattern Chart
    const patternCtx = document.getElementById('crimePatternChart').getContext('2d');
    new Chart(patternCtx, {
        type: 'line',
        data: {
            labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
            datasets: [{
                label: 'Theft',
                data: [12, 8, 15, 25, 35, 45],
                borderColor: '#dc3545',
                tension: 0.4
            }, {
                label: 'Assault',
                data: [8, 5, 10, 15, 28, 38],
                borderColor: '#ffc107',
                tension: 0.4
            }, {
                label: 'Robbery',
                data: [5, 3, 8, 12, 22, 30],
                borderColor: '#17a2b8',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // Predictive Chart
    const predictiveCtx = document.getElementById('predictiveChart').getContext('2d');
    new Chart(predictiveCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul (Predicted)'],
            datasets: [{
                label: 'Actual',
                data: [120, 135, 110, 145, 160, 155, null],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)'
            }, {
                label: 'Predicted',
                data: [null, null, null, null, null, 155, 168],
                borderColor: '#28a745',
                borderDash: [5, 5],
                backgroundColor: 'rgba(40, 167, 69, 0.1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // Geographic Chart
    const geoCtx = document.getElementById('geoChart').getContext('2d');
    new Chart(geoCtx, {
        type: 'bar',
        data: {
            labels: ['Lilongwe', 'Blantyre', 'Mzuzu', 'Zomba'],
            datasets: [{
                label: 'Crime Incidents',
                data: [89, 67, 45, 23],
                backgroundColor: ['#dc3545', '#ffc107', '#ffc107', '#28a745']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // Resource Allocation Chart
    const resourceCtx = document.getElementById('resourceChart').getContext('2d');
    new Chart(resourceCtx, {
        type: 'doughnut',
        data: {
            labels: ['Patrol Units', 'Investigation', 'Traffic', 'Special Units'],
            datasets: [{
                data: [40, 30, 20, 10],
                backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
@endpush
