<!DOCTYPE html>
<html lang="en" data-layout="horizontal">


<!-- Mirrored from dashui.codescandy.com/dashuipro/horizontal/components/pagination.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:31:07 GMT -->
<head>
  <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
<meta name="author" content="Codescandy" />

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M8S4MT3EYG"></script>
<script>
	window.dataLayer = window.dataLayer || [];
	function gtag() {
		dataLayer.push(arguments);
	}
	gtag('js', new Date());

	gtag('config', 'G-M8S4MT3EYG');
</script>

<!-- Favicon icon-->
<link rel="shortcut icon" type="image/x-icon" href="../../assets/images/favicon/favicon.ico" />

<!-- Color modes -->
<script src="../../assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="../../assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />
<link href="../../assets/libs/%40mdi/font/css/materialdesignicons.min.css" rel="stylesheet" />
<link href="../../assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />

<!-- Theme CSS -->
<link rel="stylesheet" href="../../assets/css/theme.min.css">

  <link href="../../assets/libs/prismjs/themes/prism-okaidia.min.css" rel="stylesheet">
  <title>Pagination | Dash UI - Bootstrap 5 Admin Dashboard Template</title>

</head>

<body>
  <!-- Wrapper -->
  <main id="main-wrapper" class="main-wrapper">
    <div class="header">
	<!-- navbar -->
	<div class="navbar-custom navbar navbar-expand-lg">
		<div class="container-fluid px-0">
			<a class="navbar-brand d-block d-md-none" href="../../index.html">
				<img src="../../assets/images/brand/logo/logo-2.svg" alt="Image" />
			</a>

			<a id="nav-toggle" href="#!" class="ms-auto ms-md-0 me-0 me-lg-3">
				<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor" class="bi bi-text-indent-left text-muted" viewBox="0 0 16 16">
					<path
						d="M2 3.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm.646 2.146a.5.5 0 0 1 .708 0l2 2a.5.5 0 0 1 0 .708l-2 2a.5.5 0 0 1-.708-.708L4.293 8 2.646 6.354a.5.5 0 0 1 0-.708zM7 6.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm-5 3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"
					/>
				</svg>
			</a>

			<div class="d-none d-md-none d-lg-block">
				<!-- Form -->
				<form action="#">
					<div class="input-group">
						<input class="form-control rounded-3 bg-transparent ps-9" type="search" value="" id="searchInput" placeholder="Search" />
						<span class="">
							<button class="btn position-absolute start-0" type="button">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="15"
									height="15"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
									class="feather feather-search text-dark"
								>
									<circle cx="11" cy="11" r="8"></circle>
									<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
								</svg>
							</button>
						</span>
					</div>
				</form>
			</div>
			<!--Navbar nav -->
			<ul class="navbar-nav navbar-right-wrap ms-lg-auto d-flex nav-top-wrap align-items-center ms-4 ms-lg-0">
				<li>
					<div class="dropdown">
						<button class="btn btn-ghost btn-icon rounded-circle" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
							<i class="bi theme-icon-active"></i>
							<span class="visually-hidden bs-theme-text">Toggle theme</span>
						</button>
						<ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
									<i class="bi theme-icon bi-sun-fill"></i>
									<span class="ms-2">Light</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
									<i class="bi theme-icon bi-moon-stars-fill"></i>
									<span class="ms-2">Dark</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="auto" aria-pressed="true">
									<i class="bi theme-icon bi-circle-half"></i>
									<span class="ms-2">Auto</span>
								</button>
							</li>
						</ul>
					</div>
				</li>

				<li class="dropdown stopevent ms-2">
					<a class="btn btn-ghost btn-icon rounded-circle" href="#!" role="button" id="dropdownNotification" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<i class="icon-xs" data-feather="bell"></i>
					</a>
					<div class="dropdown-menu dropdown-menu-lg dropdown-menu-end" aria-labelledby="dropdownNotification">
						<div>
							<div class="border-bottom px-3 pt-2 pb-3 d-flex justify-content-between align-items-center">
								<p class="mb-0 text-dark fw-medium fs-4">Notifications</p>
								<a href="#!" class="text-muted">
									<span>
										<i class="me-1 icon-xs" data-feather="settings"></i>
									</span>
								</a>
							</div>
							<div data-simplebar style="height: 250px">
								<!-- List group -->
								<ul class="list-group list-group-flush notification-list-scroll">
									<!-- List group item -->
									<li class="list-group-item bg-light">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Rishi Chopra</h5>
											<p class="mb-0">Mauris blandit erat id nunc blandit, ac eleifend dolor pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Neha Kannned</h5>
											<p class="mb-0">Proin at elit vel est condimentum elementum id in ante. Maecenas et sapien metus.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Nirmala Chauhan</h5>
											<p class="mb-0">Morbi maximus urna lobortis elit sollicitudin sollicitudieget elit vel pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Sina Ray</h5>
											<p class="mb-0">Sed aliquam augue sit amet mauris volutpat hendrerit sed nunc eu diam.</p>
										</a>
									</li>
								</ul>
							</div>
							<div class="border-top px-3 py-2 text-center">
								<a href="#!" class="text-inherit">View all Notifications</a>
							</div>
						</div>
					</div>
				</li>
				<!-- List -->
				<li class="dropdown ms-2">
					<a class="rounded-circle" href="#!" role="button" id="dropdownUser" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<div class="avatar avatar-md avatar-indicators avatar-online">
							<img alt="avatar" src="../../assets/images/avatar/avatar-11.jpg" class="rounded-circle" />
						</div>
					</a>
					<div class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownUser">
						<div class="px-4 pb-0 pt-2">
							<div class="lh-1">
								<h5 class="mb-1">John E. Grainger</h5>
								<a href="#!" class="text-inherit fs-6">View my profile</a>
							</div>
							<div class="dropdown-divider mt-3 mb-2"></div>
						</div>

						<ul class="list-unstyled">
							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="user"></i>
									Edit Profile
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="activity"></i>
									Activity Log
								</a>
							</li>

							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="settings"></i>
									Settings
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="../../index.html">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="power"></i>
									Sign Out
								</a>
							</li>
						</ul>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>

    <!-- navbar horizontal -->
    <!-- navbar -->
<div class="navbar-horizontal nav-dashboard">
	<div class="container-fluid">
		<nav class="navbar navbar-expand-lg navbar-default navbar-dropdown p-0 py-lg-2">
			<div class="d-flex d-lg-block justify-content-between align-items-center w-100 w-lg-0 py-2 px-4 px-md-2 px-lg-0">
				<span class="d-lg-none">Menu</span>
				<!-- Button -->
				<button
					class="navbar-toggler collapsed ms-2"
					type="button"
					data-bs-toggle="collapse"
					data-bs-target="#navbar-default"
					aria-controls="navbar-default"
					aria-expanded="false"
					aria-label="Toggle navigation"
				>
					<span class="icon-bar top-bar mt-0"></span>
					<span class="icon-bar middle-bar"></span>
					<span class="icon-bar bottom-bar"></span>
				</button>
			</div>
			<!-- Collapse -->
			<div class="collapse navbar-collapse px-6 px-lg-0" id="navbar-default">
				<ul class="navbar-nav">
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarDashboard" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-bs-display="static">Dashboard</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarDashboard">
							<li>
								<a class="dropdown-item" href="../index.html">Project</a>
							</li>
							<li>
								<a class="dropdown-item" href="../dashboard-analytics.html">Analytics</a>
							</li>
							<li>
								<a class="dropdown-item" href="../dashboard-ecommerce.html">Ecommerce</a>
							</li>
							<li>
								<a class="dropdown-item" href="../dashboard-crm.html">CRM</a>
							</li>
							<li>
								<a class="dropdown-item" href="../dashboard-finance.html">Finance</a>
							</li>
							<li>
								<a class="dropdown-item" href="../dashboard-blog.html">Blog</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarApps" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Apps</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarApps">
							<li>
								<a class="dropdown-item" href="../calendar.html">Calendar</a>
							</li>
							<li>
								<a class="dropdown-item" href="../apps-file-manager.html">File Manager</a>
							</li>

							<li>
								<a class="dropdown-item" href="../chat-app.html">Chat</a>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Kanban</a>
								<ul class="dropdown-menu">
									<li>
										<a href="../task-kanban-grid.html" class="dropdown-item">Board</a>
									</li>
									<li>
										<a href="../task-kanban-list.html" class="dropdown-item">List</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Email</a>
								<ul class="dropdown-menu">
									<li>
										<a href="../mail.html" class="dropdown-item">Inbox</a>
									</li>
									<li>
										<a href="../mail-details.html" class="dropdown-item">Details</a>
									</li>
									<li>
										<a href="../mail-draft.html" class="dropdown-item">Draft</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Ecommerce</a>
								<ul class="dropdown-menu">
									<li>
										<a href="../ecommerce-products.html" class="dropdown-item">Products</a>
									</li>
									<li>
										<a href="../ecommerce-products-details.html" class="dropdown-item">Prouduct Details</a>
									</li>
									<li>
										<a href="../ecommerce-product-edit.html" class="dropdown-item">Add Product</a>
									</li>

									<li>
										<a href="../ecommerce-order-list.html" class="dropdown-item">Orders</a>
									</li>
									<li>
										<a href="../ecommerce-order-detail.html" class="dropdown-item">Order Details</a>
									</li>
									<li>
										<a href="../ecommerce-cart.html" class="dropdown-item">Shopping Cart</a>
									</li>
									<li>
										<a href="../ecommerce-checkout.html" class="dropdown-item">Checkout</a>
									</li>
									<li>
										<a href="../ecommerce-customer.html" class="dropdown-item">Customers</a>
									</li>
									<li>
										<a href="../ecommerce-seller.html" class="dropdown-item">Seller</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Project</a>
								<ul class="dropdown-menu">
									<li class="nav-item">
										<a class="dropdown-item" href="../project-grid.html">Grid</a>
									</li>
									<li class="nav-item">
										<a class="dropdown-item" href="../project-list.html">List</a>
									</li>

									<li class="dropdown-submenu dropend">
										<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Single</a>
										<ul class="dropdown-menu">
											<li class="nav-item">
												<a class="dropdown-item" href="../project-overview.html">Overview</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="../project-task.html">Task</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="../project-budget.html">Budget</a>
											</li>

											<li class="nav-item">
												<a class="dropdown-item" href="../project-files.html">File</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="../project-team.html">Team</a>
											</li>
										</ul>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">CRM</a>
								<ul class="dropdown-menu">
									<li>
										<a href="../crm-company.html" class="dropdown-item">Company</a>
									</li>
									<li>
										<a href="../crm-contacts.html" class="dropdown-item">Contacts</a>
									</li>
									<li>
										<a class="dropdown-item" href="../deals.html">
											Deals
											<span class="badge text-bg-primary ms-2">New</span>
										</a>
									</li>
									<li>
										<a class="dropdown-item" href="../deals-single.html">
											Deals Single
											<span class="badge text-bg-primary ms-2">New</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Invoice</a>
								<ul class="dropdown-menu">
									<li>
										<a href="../invoice-list.html" class="dropdown-item">List</a>
									</li>
									<li>
										<a href="../invoice-detail.html" class="dropdown-item">Details</a>
									</li>
									<li>
										<a href="../invoice-generator.html" class="dropdown-item">Create Invoice</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Profile</a>
								<ul class="dropdown-menu">
									<li>
										<a href="../profile-overview.html" class="dropdown-item">Overview</a>
									</li>
									<li>
										<a href="../profile-project.html" class="dropdown-item">Project</a>
									</li>
									<li>
										<a href="../profile-file.html" class="dropdown-item">Files</a>
									</li>
									<li>
										<a href="../profile-team.html" class="dropdown-item">Team</a>
									</li>
									<li>
										<a href="../profile-followers.html" class="dropdown-item">Followers</a>
									</li>
									<li>
										<a href="../profile-activity.html" class="dropdown-item">Activity</a>
									</li>
									<li>
										<a class="dropdown-item" href="../profile-settings.html">Settings</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Blog</a>
								<ul class="dropdown-menu">
									<li>
										<a class="dropdown-item" href="../blog-author.html">Author</a>
									</li>
									<li>
										<a class="dropdown-item" href="../blog-author-detail.html">Detail</a>
									</li>
									<li>
										<a class="dropdown-item" href="../create-blog-post.html">Create Post</a>
									</li>
								</ul>
							</li>
						</ul>
					</li>
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarAuthentication" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Authentication</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarAuthentication">
							<li>
								<a class="dropdown-item" href="../sign-in.html">Sign In</a>
							</li>
							<li>
								<a class="dropdown-item" href="../sign-up.html">Sign Up</a>
							</li>
							<li>
								<a class="dropdown-item" href="../forget-password.html">Forgot Password</a>
							</li>
							<li>
								<a class="dropdown-item" href="../maintenance.html">maintenance</a>
							</li>
							<li>
								<a class="dropdown-item" href="../404-error.html">404 Error</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="layoutsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">Layouts</a>
						<ul class="dropdown-menu dropdown-menu-start" aria-labelledby="layoutsDropdown">
							<li><span class="dropdown-header">Layouts</span></li>
							<li class="nav-item">
								<a class="dropdown-item" href="../../index.html">Default</a>
							</li>

							<li class="nav-item">
								<a class="dropdown-item" href="../index.html">Horizontal</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarPages" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Pages</a>
						<ul class="dropdown-menu" aria-labelledby="navbarPages">
							<li>
								<a class="dropdown-item" href="../pricing.html">Pricing</a>
							</li>
							<li>
								<a class="dropdown-item" href="../starter.html">Starter</a>
							</li>

							<li>
								<a class="dropdown-item" href="../maintenance.html">Maintenance</a>
							</li>
							<li>
								<a class="dropdown-item" href="../404-error.html">404 Error</a>
							</li>
						</ul>
					</li>
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarBaseUI" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Components</a>
						<div class="dropdown-menu dropdown-menu-xl" aria-labelledby="navbarBaseUI">
							<div class="row">
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="accordions.html" class="dropdown-item">Accordions</a>
										</li>
										<li class="nav-item">
											<a class="dropdown-item" href="alerts.html">Alert</a>
										</li>

										<li class="nav-item">
											<a href="badge.html" class="dropdown-item">Badge</a>
										</li>

										<li class="nav-item">
											<a href="breadcrumb.html" class="dropdown-item">Breadcrumb</a>
										</li>
										<li class="nav-item">
											<a href="buttons.html" class="dropdown-item">Buttons</a>
										</li>
										<li class="nav-item">
											<a href="button-group.html" class="dropdown-item">Button group</a>
										</li>
										<li class="nav-item">
											<a href="card.html" class="dropdown-item">Card</a>
										</li>
										<li class="nav-item">
											<a href="carousel.html" class="dropdown-item">Carousel</a>
										</li>
										<li class="nav-item">
											<a href="close-button.html" class="dropdown-item">Close Button</a>
										</li>
									</ul>
								</div>
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="collapse.html" class="dropdown-item">Collapse</a>
										</li>
										<li class="nav-item">
											<a href="dropdowns.html" class="dropdown-item">Dropdowns</a>
										</li>
										<li class="nav-item">
											<a href="forms.html" class="dropdown-item">Forms</a>
										</li>

										<li class="nav-item">
											<a href="list-group.html" class="dropdown-item">List group</a>
										</li>
										<li class="nav-item">
											<a href="modal.html" class="dropdown-item">Modal</a>
										</li>
										<li class="nav-item">
											<a href="navs-tabs.html" class="dropdown-item">Navs and tabs</a>
										</li>
										<li class="nav-item">
											<a href="navbar.html" class="dropdown-item">Navbar</a>
										</li>
										<li class="nav-item">
											<a href="offcanvas.html" class="dropdown-item">Offcanvas</a>
										</li>
										<li class="nav-item">
											<a href="pagination.html" class="dropdown-item">Pagination</a>
										</li>
									</ul>
								</div>
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="placeholders.html" class="dropdown-item">Placeholders</a>
										</li>
										<li class="nav-item">
											<a href="popovers.html" class="dropdown-item">Popovers</a>
										</li>
										<li class="nav-item">
											<a href="progress.html" class="dropdown-item">Progress</a>
										</li>
										<li class="nav-item">
											<a href="scrollspy.html" class="dropdown-item">Scrollspy</a>
										</li>
										<li class="nav-item">
											<a href="spinners.html" class="dropdown-item">Spinners</a>
										</li>
										<li class="nav-item">
											<a href="tables.html" class="dropdown-item">Tables</a>
										</li>
										<li class="nav-item">
											<a href="toasts.html" class="dropdown-item">Toasts</a>
										</li>
										<li class="nav-item">
											<a href="tooltips.html" class="dropdown-item">Tooltips</a>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<i data-feather="more-horizontal" class="icon-xxs"></i>
						</a>
						<div class="dropdown-menu dropdown-menu-md" aria-labelledby="navbarDropdown">
							<div class="list-group">
								<a class="list-group-item list-group-item-action border-0" href="../../docs/index.html">
									<div class="d-flex align-items-center">
										<i data-feather="file-text" class="icon-sm text-primary"></i>

										<div class="ms-3">
											<h5 class="mb-0">Documentations</h5>
											<p class="mb-0 fs-6">Browse the all documentation</p>
										</div>
									</div>
								</a>
								<a class="list-group-item list-group-item-action border-0" href="../../docs/changelog.html">
									<div class="d-flex align-items-center">
										<i data-feather="layers" class="icon-sm text-primary"></i>
										<div class="ms-3">
											<h5 class="mb-0">
												Changelog
												<span class="text-primary ms-1">v1.0.0</span>
											</h5>
											<p class="mb-0 fs-6">See what's new</p>
										</div>
									</div>
								</a>
							</div>
						</div>
					</li>
				</ul>
			</div>
		</nav>
	</div>
</div>

    <!-- Page Content -->
    <div id="app-content">
      <div class="app-content-area">
        <!-- Container fluid -->
        <div class="container-fluid">
          <div class="row">
            <div class="col-xl-9 col-md-12 col-sm-12 col-12">
              <!-- Content -->
              <div class="">
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                    <div class="mb-8" id="intro">
                      <h1 class="mb-0 h2">Pagination</h1>
                      <p class="mb-0 text-muted">Documentation and examples for showing pagination to indicate a
                        series of related content exists across multiple pages.
                      </p>
                    </div>
                  </div>
                </div>
                <!-- default-pagination -->
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                    <div id="default-pagination" class="mb-4">
                      <h2 class="h3 mb-1">Default Pagination</h2>
                      <p>We use a large block of connected links for our pagination, making links hard to miss and
                        easily
                        scalable—all while providing large hit areas. Pagination is built with list HTML elements so
                        screen readers can announce
                        the number of available links. Use a wrapping <code class="highlighter-rouge">&lt;nav&gt;</code>
                        element to identify it as a navigation section to screen readers and other assistive
                        technologies.
                      </p>
                    </div>
                    <!-- Card -->
                    <div class="card mb-10">
                      <ul class="nav nav-line-bottom" id="pills-tab-default-pagination" role="tablist">
                        <li class="nav-item">
                          <a class="nav-link active" id="pills-default-pagination-design-tab" data-bs-toggle="pill"
                            href="#pills-default-pagination-design" role="tab"
                            aria-controls="pills-default-pagination-design" aria-selected="true">Design</a>
                        </li>
                        <li class="nav-item">
                          <a class="nav-link" id="pills-default-pagination-html-tab" data-bs-toggle="pill"
                            href="#pills-default-pagination-html" role="tab"
                            aria-controls="pills-default-pagination-html" aria-selected="false">HTML</a>
                        </li>
                      </ul>
                      <!-- Tab content -->
                      <div class="tab-content p-4" id="pills-tabContent-default-pagination">
                        <div class="tab-pane tab-example-design fade show active bg-light p-4 rounded-3"
                          id="pills-default-pagination-design" role="tabpanel"
                          aria-labelledby="pills-default-pagination-design-tab">
                          <nav aria-label="Page navigation example">
                            <ul class="pagination mb-0">
                              <li class="page-item"><a class="page-link" href="#">Previous</a>
                              </li>
                              <li class="page-item"><a class="page-link" href="#">1</a></li>
                              <li class="page-item"><a class="page-link" href="#">2</a></li>
                              <li class="page-item"><a class="page-link" href="#">3</a></li>
                              <li class="page-item"><a class="page-link" href="#">Next</a>
                              </li>
                            </ul>
                          </nav>
                        </div>
                        <div class="tab-pane tab-example-html fade " id="pills-default-pagination-html" role="tabpanel"
                          aria-labelledby="pills-default-pagination-html-tab">

                          <pre><code class="language-markup">  <span class="token comment">&lt;!-- default pagination --&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>nav</span> <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Page navigation example<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>pagination<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Previous<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>1<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>3<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Next<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>nav</span><span class="token punctuation">&gt;</span></span></code></pre>


                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Default pagination -->

                <!-- Working with icons -->
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                    <div id="working-with-icons" class="mb-4">
                      <h2 class="h3 mb-1">Working with icons</h2>
                      <p>Looking to use an icon or symbol in place of text for some pagination links? Be sure to provide
                        proper screen reader support with <code class="highlighter-rouge">aria</code> attributes.</p>
                    </div>
                    <!-- Card -->
                    <div class="card mb-10">
                      <ul class="nav nav-line-bottom" id="pills-tab-working-with-icons" role="tablist">
                        <li class="nav-item">
                          <a class="nav-link active" id="pills-working-with-icons-design-tab" data-bs-toggle="pill"
                            href="#pills-working-with-icons-design" role="tab"
                            aria-controls="pills-working-with-icons-design" aria-selected="true">Design</a>
                        </li>
                        <li class="nav-item">
                          <a class="nav-link" id="pills-working-with-icons-html-tab" data-bs-toggle="pill"
                            href="#pills-working-with-icons-html" role="tab"
                            aria-controls="pills-working-with-icons-html" aria-selected="false">HTML</a>
                        </li>
                      </ul>
                      <!-- Tab content -->
                      <div class="tab-content p-4" id="pills-tabContent-working-with-icons">
                        <div class="tab-pane tab-example-design fade show active bg-light p-4 rounded-3"
                          id="pills-working-with-icons-design" role="tabpanel"
                          aria-labelledby="pills-working-with-icons-design-tab">
                          <nav aria-label="Page navigation example">
                            <ul class="pagination mb-3">
                              <li class="page-item">
                                <a class="page-link" href="#" aria-label="Previous">
                                  <span aria-hidden="true">&laquo;</span>
                                </a>
                              </li>
                              <li class="page-item"><a class="page-link" href="#">1</a></li>
                              <li class="page-item"><a class="page-link" href="#">2</a></li>
                              <li class="page-item"><a class="page-link" href="#">3</a></li>
                              <li class="page-item">
                                <a class="page-link" href="#" aria-label="Next">
                                  <span aria-hidden="true">&raquo;</span>
                                </a>
                              </li>
                            </ul>
                          </nav>
                        </div>
                        <div class="tab-pane tab-example-html fade" id="pills-working-with-icons-html" role="tabpanel"
                          aria-labelledby="pills-working-with-icons-html-tab">

                          <pre><code class="language-markup"> <span class="token comment">&lt;!-- working with icons --&gt;</span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>nav</span> <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Page navigation example<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>pagination<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Previous<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">aria-hidden</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token entity" title="«">&amp;laquo;</span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>1<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>3<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Next<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">aria-hidden</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token entity" title="»">&amp;raquo;</span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>nav</span><span class="token punctuation">&gt;</span></span></code></pre>


                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Working with icons -->

                <!-- Disabled and active states -->
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                    <div id="disabled-and-active-states" class="mb-4">
                      <h2 class="h3 mb-1">Disabled and active states</h2>
                      <p>Pagination links are customizable for different circumstances. Use <code
                          class="highlighter-rouge">.disabled</code> for links that appear un-clickable and <code
                          class="highlighter-rouge">.active</code> to indicate
                        the current page.</p>
                    </div>
                    <!-- Card -->
                    <div class="card mb-10">
                      <ul class="nav nav-line-bottom" id="pills-tab-disabled-and-active-states" role="tablist">
                        <li class="nav-item">
                          <a class="nav-link active" id="pills-disabled-and-active-states-design-tab"
                            data-bs-toggle="pill" href="#pills-disabled-and-active-states-design" role="tab"
                            aria-controls="pills-disabled-and-active-states-design" aria-selected="true">Design</a>
                        </li>
                        <li class="nav-item">
                          <a class="nav-link" id="pills-disabled-and-active-states-html-tab" data-bs-toggle="pill"
                            href="#pills-disabled-and-active-states-html" role="tab"
                            aria-controls="pills-disabled-and-active-states-html" aria-selected="false">HTML</a>
                        </li>
                      </ul>
                      <!-- Tab content -->
                      <div class="tab-content p-4" id="pills-tabContent-disabled-and-active-states">
                        <div class="tab-pane tab-example-design fade show active bg-light p-4 rounded-3"
                          id="pills-disabled-and-active-states-design" role="tabpanel"
                          aria-labelledby="pills-disabled-and-active-states-design-tab">
                          <nav aria-label="...">
                            <ul class="pagination mb-0">
                              <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                              </li>
                              <li class="page-item"><a class="page-link" href="#">1</a></li>
                              <li class="page-item active" aria-current="page">
                                <a class="page-link" href="#">2 <span class="visually-hidden">(current)</span></a>
                              </li>
                              <li class="page-item"><a class="page-link" href="#">3</a></li>
                              <li class="page-item">
                                <a class="page-link" href="#">Next</a>
                              </li>
                            </ul>
                          </nav>
                        </div>
                        <div class="tab-pane tab-example-html fade" id="pills-disabled-and-active-states-html"
                          role="tabpanel" aria-labelledby="pills-disabled-and-active-states-html-tab">

                          <pre><code class="language-markup"> <span class="token comment">&lt;!-- disabled and active states --&gt;</span>
                                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>nav</span> <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>...<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>pagination<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item disabled<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">tabindex</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>-1<span class="token punctuation">"</span></span> <span class="token attr-name">aria-disabled</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Previous<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>1<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item active<span class="token punctuation">"</span></span> <span class="token attr-name">aria-current</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>2 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>visually-hidden<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>(current)<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>3<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Next<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>nav</span><span class="token punctuation">&gt;</span></span></code></pre>


                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Disabled and active states -->

                <!-- Sizing -->
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                    <div id="sizing" class="mb-4">
                      <h2 class="h3 mb-1">Sizing</h2>
                      <p>Fancy larger or smaller pagination? Add <code class="highlighter-rouge">.pagination-lg</code>
                        or
                        <code class="highlighter-rouge">.pagination-sm</code> for additional sizes.
                      </p>
                    </div>
                    <!-- Card -->
                    <div class="card mb-10">
                      <ul class="nav nav-line-bottom" id="pills-tab-sizing-large" role="tablist">
                        <li class="nav-item">
                          <a class="nav-link active" id="pills-sizing-large-design-tab" data-bs-toggle="pill"
                            href="#pills-sizing-large-design" role="tab" aria-controls="pills-sizing-large-design"
                            aria-selected="true">Design</a>
                        </li>
                        <li class="nav-item">
                          <a class="nav-link" id="pills-sizing-large-html-tab" data-bs-toggle="pill"
                            href="#pills-sizing-large-html" role="tab" aria-controls="pills-sizing-large-html"
                            aria-selected="false">HTML</a>
                        </li>
                      </ul>
                      <!-- Tab content -->
                      <div class="tab-content p-4" id="pills-tabContent-sizing-large">
                        <div class="tab-pane tab-example-design fade show active bg-light p-4 rounded-3"
                          id="pills-sizing-large-design" role="tabpanel"
                          aria-labelledby="pills-sizing-large-design-tab">
                          <nav aria-label="...">
                            <ul class="pagination pagination-lg mb-0">
                              <li class="page-item active" aria-current="page">
                                <span class="page-link">
                                  1
                                  <span class="visually-hidden">(current)</span>
                                </span>
                              </li>
                              <li class="page-item"><a class="page-link" href="#">2</a></li>
                              <li class="page-item"><a class="page-link" href="#">3</a></li>
                            </ul>
                          </nav>
                        </div>
                        <div class="tab-pane tab-example-html fade" id="pills-sizing-large-html" role="tabpanel"
                          aria-labelledby="pills-sizing-large-html-tab">

                          <pre><code class="language-markup"> <span class="token comment">&lt;!-- sizing --&gt;</span>
                                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>nav</span> <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>...<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>pagination pagination-lg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item active<span class="token punctuation">"</span></span> <span class="token attr-name">aria-current</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
          1
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>visually-hidden<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>(current)<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>3<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>nav</span><span class="token punctuation">&gt;</span></span></code></pre>


                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Sizing -->
                <!-- sizing-small -->
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                    <!-- Card -->
                    <div class="card mb-10">
                      <ul class="nav nav-line-bottom" id="pills-tab-sizing-small" role="tablist">
                        <li class="nav-item">
                          <a class="nav-link active" id="pills-sizing-small-design-tab" data-bs-toggle="pill"
                            href="#pills-sizing-small-design" role="tab" aria-controls="pills-sizing-small-design"
                            aria-selected="true">Design</a>
                        </li>
                        <li class="nav-item">
                          <a class="nav-link" id="pills-sizing-small-html-tab" data-bs-toggle="pill"
                            href="#pills-sizing-small-html" role="tab" aria-controls="pills-sizing-small-html"
                            aria-selected="false">HTML</a>
                        </li>
                      </ul>
                      <!-- Tab content -->
                      <div class="tab-content p-4" id="pills-tabContent-sizing-small">
                        <div class="tab-pane tab-example-design fade show active bg-light p-4 rounded-3"
                          id="pills-sizing-small-design" role="tabpanel"
                          aria-labelledby="pills-sizing-small-design-tab">
                          <nav aria-label="...">
                            <ul class="pagination pagination-sm mb-0">
                              <li class="page-item active" aria-current="page">
                                <span class="page-link">
                                  1
                                  <span class="visually-hidden">(current)</span>
                                </span>
                              </li>
                              <li class="page-item"><a class="page-link" href="#">2</a></li>
                              <li class="page-item"><a class="page-link" href="#">3</a></li>
                            </ul>
                          </nav>
                        </div>
                        <div class="tab-pane tab-example-html fade" id="pills-sizing-small-html" role="tabpanel"
                          aria-labelledby="pills-sizing-small-html-tab">

                          <pre><code class="language-markup"> <span class="token comment">&lt;!-- collapse --&gt;</span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>nav</span> <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>...<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>pagination pagination-sm<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item active<span class="token punctuation">"</span></span> <span class="token attr-name">aria-current</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
          1
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>visually-hidden<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>(current)<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>3<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>nav</span><span class="token punctuation">&gt;</span></span></code></pre>


                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- sizing-small -->

                <!-- Alignment -->
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                    <div id="alignment" class="mb-4">
                      <h2 class="h3 mb-1">Alignment</h2>
                      <p>Change the alignment of pagination components with <a href="#">flexbox
                          utilities</a>.</p>
                    </div>
                    <!-- Card -->
                    <div class="card mb-10">
                      <ul class="nav nav-line-bottom" id="pills-tab-alignment-center" role="tablist">
                        <li class="nav-item">
                          <a class="nav-link active" id="pills-alignment-center-design-tab" data-bs-toggle="pill"
                            href="#pills-alignment-center-design" role="tab"
                            aria-controls="pills-alignment-center-design" aria-selected="true">Design</a>
                        </li>
                        <li class="nav-item">
                          <a class="nav-link" id="pills-alignment-center-html-tab" data-bs-toggle="pill"
                            href="#pills-alignment-center-html" role="tab" aria-controls="pills-alignment-center-html"
                            aria-selected="false">HTML</a>
                        </li>
                      </ul>
                      <!-- Tab content -->
                      <div class="tab-content p-4" id="pills-tabContent-alignment-center">
                        <div class="tab-pane tab-example-design fade show active bg-light p-4 rounded-3"
                          id="pills-alignment-center-design" role="tabpanel"
                          aria-labelledby="pills-alignment-center-design-tab">
                          <nav aria-label="Page navigation example">
                            <ul class="pagination justify-content-center mb-0">
                              <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                              </li>
                              <li class="page-item"><a class="page-link" href="#">1</a></li>
                              <li class="page-item"><a class="page-link" href="#">2</a></li>
                              <li class="page-item"><a class="page-link" href="#">3</a></li>
                              <li class="page-item">
                                <a class="page-link" href="#">Next</a>
                              </li>
                            </ul>
                          </nav>
                        </div>
                        <div class="tab-pane tab-example-html fade" id="pills-alignment-center-html" role="tabpanel"
                          aria-labelledby="pills-alignment-center-html-tab">

                          <pre><code class="language-markup"> <span class="token comment">&lt;!-- center alignment --&gt;</span>
   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>nav</span> <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Page navigation example<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>pagination justify-content-center<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item disabled<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">tabindex</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>-1<span class="token punctuation">"</span></span> <span class="token attr-name">aria-disabled</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Previous<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>1<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>3<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Next<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>nav</span><span class="token punctuation">&gt;</span></span></code></pre>


                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Alignment -->
                <!-- Alignment right -->
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                    <!-- Card -->
                    <div class="card mb-10">
                      <ul class="nav nav-line-bottom" id="pills-tab-alignment-right" role="tablist">
                        <li class="nav-item">
                          <a class="nav-link active" id="pills-alignment-right-design-tab" data-bs-toggle="pill"
                            href="#pills-alignment-right-design" role="tab" aria-controls="pills-alignment-right-design"
                            aria-selected="true">Design</a>
                        </li>
                        <li class="nav-item">
                          <a class="nav-link" id="pills-alignment-right-html-tab" data-bs-toggle="pill"
                            href="#pills-alignment-right-html" role="tab" aria-controls="pills-alignment-right-html"
                            aria-selected="false">HTML</a>
                        </li>
                      </ul>
                      <!-- Tab content -->
                      <div class="tab-content p-4" id="pills-tabContent-alignment-right">
                        <div class="tab-pane tab-example-design fade show active bg-light p-4 rounded-3"
                          id="pills-alignment-right-design" role="tabpanel"
                          aria-labelledby="pills-alignment-right-design-tab">
                          <nav aria-label="Page navigation example">
                            <ul class="pagination justify-content-end mb-0">
                              <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                              </li>
                              <li class="page-item"><a class="page-link" href="#">1</a></li>
                              <li class="page-item"><a class="page-link" href="#">2</a></li>
                              <li class="page-item"><a class="page-link" href="#">3</a></li>
                              <li class="page-item">
                                <a class="page-link" href="#">Next</a>
                              </li>
                            </ul>
                          </nav>
                        </div>
                        <div class="tab-pane tab-example-html fade" id="pills-alignment-right-html" role="tabpanel"
                          aria-labelledby="pills-alignment-right-html-tab">

                          <pre><code class="language-markup"> <span class="token comment">&lt;!-- right alignment --&gt;</span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>nav</span> <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Page navigation example<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>pagination justify-content-end<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item disabled<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">tabindex</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>-1<span class="token punctuation">"</span></span> <span class="token attr-name">aria-disabled</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Previous<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>1<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>3<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-item<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>page-link<span class="token punctuation">"</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Next<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>nav</span><span class="token punctuation">&gt;</span></span></code></pre>


                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Alignment-right -->

              </div>
            </div>
            <div class="col-xl-2 col-lg-2 col-md-6 col-sm-12 col-12  d-none d-xl-block position-fixed end-0">
              <!-- Sidebar nav fixed -->
              <div class="sidebar-nav-fixed">
                <span class="px-4 mb-2 d-block text-uppercase ls-md h3 fs-6">Contents</span>
                <ul class="list-unstyled">
                  <li><a href="#intro" class="active">Introduction</a></li>
                  <li><a href="#default-pagination">Default Pagination</a></li>
                  <li><a href="#working-with-icons">Working with icons</a></li>
                  <li><a href="#disabled-and-active-states">Disabled and active states</a></li>
                  <li><a href="#sizing">Sizing</a></li>
                  <li><a href="#alignment">Alignment</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
  <!-- Scripts -->
  <script src="../../assets/libs/prismjs/prism.js"></script>
  <script src="../../assets/libs/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
  <script src="../../assets/libs/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
  <!-- Libs JS -->

<script src="../../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../../assets/libs/feather-icons/dist/feather.min.js"></script>
<script src="../../assets/libs/simplebar/dist/simplebar.min.js"></script>

<!-- Theme JS -->
<script src="../../assets/js/theme.min.js"></script>


</body>


<!-- Mirrored from dashui.codescandy.com/dashuipro/horizontal/components/pagination.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:31:07 GMT -->
</html>