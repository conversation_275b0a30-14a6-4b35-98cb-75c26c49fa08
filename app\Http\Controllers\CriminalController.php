<?php

namespace App\Http\Controllers;

use App\Models\Criminal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CriminalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Criminal::with(['arrests', 'biometrics']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->searchByName($search)
                  ->orWhere('criminal_number', 'like', "%{$search}%")
                  ->orWhere('national_id', 'like', "%{$search}%");
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by risk level
        if ($request->filled('risk_level')) {
            $query->where('risk_level', $request->get('risk_level'));
        }

        // Filter by district
        if ($request->filled('district')) {
            $query->where('district', $request->get('district'));
        }

        // Filter wanted criminals
        if ($request->boolean('wanted_only')) {
            $query->wanted();
        }

        $criminals = $query->latest()->paginate(20);

        return view('criminals.index', compact('criminals'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('criminals.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:100',
            'middle_name' => 'nullable|string|max:100',
            'last_name' => 'required|string|max:100',
            'alias' => 'nullable|string|max:100',
            'gender' => 'required|in:Male,Female,Other',
            'date_of_birth' => 'nullable|date|before:today',
            'place_of_birth' => 'nullable|string|max:200',
            'nationality' => 'nullable|string|max:100',
            'national_id' => 'nullable|string|max:20|unique:criminals,national_id',
            'height' => 'nullable|numeric|min:0|max:3',
            'weight' => 'nullable|numeric|min:0|max:500',
            'eye_color' => 'nullable|string|max:50',
            'hair_color' => 'nullable|string|max:50',
            'complexion' => 'nullable|string|max:50',
            'distinguishing_marks' => 'nullable|string',
            'scars_tattoos' => 'nullable|string',
            'phone_number' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:100',
            'current_address' => 'nullable|string',
            'permanent_address' => 'nullable|string',
            'district' => 'nullable|string|max:100',
            'traditional_authority' => 'nullable|string|max:100',
            'village' => 'nullable|string|max:100',
            'emergency_contact_name' => 'nullable|string|max:200',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'emergency_contact_relationship' => 'nullable|string|max:100',
            'risk_level' => 'required|in:Low,Medium,High,Critical',
            'is_wanted' => 'boolean',
            'is_repeat_offender' => 'boolean',
            'occupation' => 'nullable|string|max:200',
            'education_level' => 'nullable|string|max:100',
            'marital_status' => 'nullable|string|max:50',
            'known_associates' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        // Generate criminal number
        $validated['criminal_number'] = $this->generateCriminalNumber();
        $validated['created_by'] = Auth::id();
        $validated['updated_by'] = Auth::id();

        $criminal = Criminal::create($validated);

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Criminal profile created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Criminal $criminal)
    {
        $criminal->load([
            'arrests.arrestingOfficer',
            'biometrics.collector',
            'relationships',
            'medicalInfo',
            'cases'
        ]);

        return view('criminals.show', compact('criminal'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Criminal $criminal)
    {
        return view('criminals.edit', compact('criminal'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Criminal $criminal)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:100',
            'middle_name' => 'nullable|string|max:100',
            'last_name' => 'required|string|max:100',
            'alias' => 'nullable|string|max:100',
            'gender' => 'required|in:Male,Female,Other',
            'date_of_birth' => 'nullable|date|before:today',
            'place_of_birth' => 'nullable|string|max:200',
            'nationality' => 'nullable|string|max:100',
            'national_id' => 'nullable|string|max:20|unique:criminals,national_id,' . $criminal->id,
            'height' => 'nullable|numeric|min:0|max:3',
            'weight' => 'nullable|numeric|min:0|max:500',
            'eye_color' => 'nullable|string|max:50',
            'hair_color' => 'nullable|string|max:50',
            'complexion' => 'nullable|string|max:50',
            'distinguishing_marks' => 'nullable|string',
            'scars_tattoos' => 'nullable|string',
            'phone_number' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:100',
            'current_address' => 'nullable|string',
            'permanent_address' => 'nullable|string',
            'district' => 'nullable|string|max:100',
            'traditional_authority' => 'nullable|string|max:100',
            'village' => 'nullable|string|max:100',
            'emergency_contact_name' => 'nullable|string|max:200',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'emergency_contact_relationship' => 'nullable|string|max:100',
            'status' => 'required|in:Active,Inactive,Deceased,Deported',
            'risk_level' => 'required|in:Low,Medium,High,Critical',
            'is_wanted' => 'boolean',
            'is_repeat_offender' => 'boolean',
            'occupation' => 'nullable|string|max:200',
            'education_level' => 'nullable|string|max:100',
            'marital_status' => 'nullable|string|max:50',
            'known_associates' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        $validated['updated_by'] = Auth::id();

        $criminal->update($validated);

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Criminal profile updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Criminal $criminal)
    {
        $criminal->delete();

        return redirect()->route('criminals.index')
                        ->with('success', 'Criminal profile deleted successfully.');
    }

    /**
     * Generate a unique criminal number.
     */
    private function generateCriminalNumber(): string
    {
        do {
            $number = 'CRM' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (Criminal::where('criminal_number', $number)->exists());

        return $number;
    }

    /**
     * Display criminals list view.
     */
    public function list(Request $request)
    {
        return $this->index($request);
    }
}
