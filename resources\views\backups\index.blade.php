@extends('layouts.app')

@section('title', 'Backup Management - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Backup Management</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'Backup Management']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i data-feather="download" class="icon-xs me-2"></i>
                    Export
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#!" onclick="exportBackupList('excel')">
                        <i data-feather="file-text" class="icon-xs me-2"></i>Export List to Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#!" onclick="exportBackupList('pdf')">
                        <i data-feather="file" class="icon-xs me-2"></i>Export List to PDF
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#!" onclick="exportBackupReport()">
                        <i data-feather="bar-chart" class="icon-xs me-2"></i>Backup Report
                    </a></li>
                </ul>
            </div>
            <a href="{{ route('backups.schedule') }}" class="btn btn-outline-secondary">
                <i data-feather="clock" class="icon-xs me-2"></i>
                Schedule
            </a>
            <a href="{{ route('backups.restore') }}" class="btn btn-outline-warning">
                <i data-feather="rotate-ccw" class="icon-xs me-2"></i>
                Restore
            </a>
            <a href="{{ route('backups.create') }}" class="btn btn-primary">
                <i data-feather="plus" class="icon-xs me-2"></i>
                Create Backup
            </a>
        </div>
    </div>

    <!-- Backup Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">15</h4>
                    <p class="mb-0">Total Backups</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-success">12</h4>
                    <p class="mb-0">Successful</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning">2</h4>
                    <p class="mb-0">In Progress</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-danger">1</h4>
                    <p class="mb-0">Failed</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i data-feather="filter" class="icon-sm me-2"></i>
                    Backup Filters
                </h5>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#backupFilterCollapse">
                    <i data-feather="chevron-down" class="icon-xs"></i>
                </button>
            </div>
        </div>
        <div class="collapse" id="backupFilterCollapse">
            <div class="card-body">
                <form class="row g-3" id="backupFilterForm">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" class="form-control" name="search" placeholder="Backup name, description...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Type</label>
                        <select class="form-select" name="type">
                            <option value="">All Types</option>
                            <option value="full">Full Backup</option>
                            <option value="database">Database Only</option>
                            <option value="files">Files Only</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status">
                            <option value="">All Status</option>
                            <option value="completed">Completed</option>
                            <option value="in_progress">In Progress</option>
                            <option value="failed">Failed</option>
                            <option value="scheduled">Scheduled</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Date Range</label>
                        <select class="form-select" name="date_range">
                            <option value="">All Time</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="quarter">This Quarter</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Size Range</label>
                        <select class="form-select" name="size_range">
                            <option value="">All Sizes</option>
                            <option value="small">< 100 MB</option>
                            <option value="medium">100 MB - 1 GB</option>
                            <option value="large">1 GB - 10 GB</option>
                            <option value="xlarge">> 10 GB</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i data-feather="search" class="icon-xs"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Quick Filter Buttons -->
    <div class="d-flex gap-2 mb-4 flex-wrap">
        <button class="btn btn-sm btn-outline-primary active" onclick="filterBackups('all')">
            All Backups <span class="badge bg-primary ms-1">15</span>
        </button>
        <button class="btn btn-sm btn-outline-info" onclick="filterBackups('full')">
            Full <span class="badge bg-info ms-1">8</span>
        </button>
        <button class="btn btn-sm btn-outline-success" onclick="filterBackups('database')">
            Database <span class="badge bg-success ms-1">5</span>
        </button>
        <button class="btn btn-sm btn-outline-warning" onclick="filterBackups('files')">
            Files <span class="badge bg-warning ms-1">2</span>
        </button>
        <button class="btn btn-sm btn-outline-secondary" onclick="filterBackups('scheduled')">
            Scheduled <span class="badge bg-secondary ms-1">4</span>
        </button>
        <button class="btn btn-sm btn-outline-danger" onclick="filterBackups('failed')">
            Failed <span class="badge bg-danger ms-1">1</span>
        </button>
    </div>

    <!-- Backup List -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">System Backups</h4>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i data-feather="more-horizontal" class="icon-xs me-2"></i>
                            Bulk Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#!" onclick="bulkBackupAction('download')">
                                <i data-feather="download" class="icon-xs me-2"></i>Download Selected
                            </a></li>
                            <li><a class="dropdown-item" href="#!" onclick="bulkBackupAction('verify')">
                                <i data-feather="check-circle" class="icon-xs me-2"></i>Verify Selected
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#!" onclick="bulkBackupAction('delete')">
                                <i data-feather="trash-2" class="icon-xs me-2"></i>Delete Selected
                            </a></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-info dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i data-feather="columns" class="icon-xs me-2"></i>
                            Columns
                        </button>
                        <ul class="dropdown-menu">
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2" checked> Name
                            </label></li>
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2" checked> Type
                            </label></li>
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2" checked> Size
                            </label></li>
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2" checked> Created
                            </label></li>
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2"> Duration
                            </label></li>
                            <li><label class="dropdown-item">
                                <input type="checkbox" class="form-check-input me-2"> Compression
                            </label></li>
                        </ul>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshBackupList()">
                        <i data-feather="refresh-cw" class="icon-xs me-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllBackups">
                                </div>
                            </th>
                            <th>
                                <a href="#!" class="text-decoration-none text-dark d-flex align-items-center">
                                    Backup Name
                                    <i data-feather="chevron-up" class="icon-xs ms-1"></i>
                                </a>
                            </th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Created</th>
                            <th>Duration</th>
                            <th>Status</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($backups as $backup)
                        <tr class="{{ $backup->status === 'failed' ? 'table-danger' : ($backup->status === 'in_progress' ? 'table-warning' : '') }}">
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input backup-checkbox" type="checkbox" value="{{ $backup->id }}">
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @php
                                        $iconColors = [
                                            'full' => 'bg-primary',
                                            'database' => 'bg-info',
                                            'files' => 'bg-warning'
                                        ];
                                        $iconColor = $iconColors[$backup->type] ?? 'bg-secondary';
                                    @endphp
                                    <div class="avatar avatar-sm {{ $iconColor }} text-white me-3">
                                        <i data-feather="hard-drive" style="width: 16px; height: 16px;"></i>
                                    </div>
                                    <div>
                                        <strong>{{ $backup->name }}</strong>
                                        <br><small class="text-muted">{{ $backup->description }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                @php
                                    $badgeColors = [
                                        'full' => 'bg-primary',
                                        'database' => 'bg-info',
                                        'files' => 'bg-warning'
                                    ];
                                    $badgeColor = $badgeColors[$backup->type] ?? 'bg-secondary';
                                @endphp
                                <span class="badge {{ $badgeColor }}">{{ ucfirst($backup->type) }}</span>
                            </td>
                            <td>
                                <strong>{{ $backup->size }}</strong>
                                <br><small class="text-muted">{{ $backup->compression ? 'Compressed' : 'Uncompressed' }}</small>
                            </td>
                            <td>
                                {{ \Carbon\Carbon::parse($backup->created_at)->format('d/m/Y H:i A') }}
                                <br><small class="text-muted">{{ $backup->status === 'in_progress' ? 'In Progress' : 'Scheduled' }}</small>
                            </td>
                            <td>
                                @if($backup->status === 'completed')
                                    <span class="text-success">{{ $backup->duration }}</span>
                                    <br><small class="text-muted">Normal speed</small>
                                @elseif($backup->status === 'in_progress')
                                    <span class="text-warning">{{ $backup->duration }}</span>
                                    <br><small class="text-muted">Running...</small>
                                @else
                                    <span class="text-danger">{{ $backup->duration }}</span>
                                    <br><small class="text-muted">Failed</small>
                                @endif
                            </td>
                            <td>
                                @php
                                    $statusColors = [
                                        'completed' => 'bg-success',
                                        'in_progress' => 'bg-warning',
                                        'failed' => 'bg-danger',
                                        'scheduled' => 'bg-info'
                                    ];
                                    $statusColor = $statusColors[$backup->status] ?? 'bg-secondary';
                                @endphp
                                <span class="badge {{ $statusColor }}">{{ ucfirst(str_replace('_', ' ', $backup->status)) }}</span>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        @if($backup->status === 'completed')
                                        <li><a class="dropdown-item" href="#!" onclick="downloadBackup({{ $backup->id }})">
                                            <i data-feather="download" class="icon-xs me-2"></i>Download
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!" onclick="verifyBackup({{ $backup->id }})">
                                            <i data-feather="check-circle" class="icon-xs me-2"></i>Verify
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!" onclick="restoreBackup({{ $backup->id }})">
                                            <i data-feather="rotate-ccw" class="icon-xs me-2"></i>Restore
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        @endif
                                        @if($backup->status === 'in_progress')
                                        <li><a class="dropdown-item text-warning" href="#!" onclick="cancelBackup({{ $backup->id }})">
                                            <i data-feather="x-circle" class="icon-xs me-2"></i>Cancel
                                        </a></li>
                                        @endif
                                        <li><a class="dropdown-item text-danger" href="#!" onclick="deleteBackup({{ $backup->id }})">
                                            <i data-feather="trash-2" class="icon-xs me-2"></i>Delete
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i data-feather="hard-drive" class="icon-lg mb-2"></i>
                                    <p>No backups found.</p>
                                    <a href="{{ route('backups.create') }}" class="btn btn-primary btn-sm">
                                        <i data-feather="plus" class="icon-xs me-2"></i>
                                        Create First Backup
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Pagination -->
        @if($backups->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    Showing <strong>{{ $backups->firstItem() }}</strong> to <strong>{{ $backups->lastItem() }}</strong>
                    of <strong>{{ $backups->total() }}</strong> backups
                    @php
                        $totalSize = $backups->sum(function($backup) {
                            return floatval(str_replace(' GB', '', str_replace(' MB', '', $backup->size)));
                        });
                    @endphp
                    <span class="text-muted">| Total size: <strong>{{ number_format($totalSize, 1) }} GB</strong></span>
                </div>
                <!-- Laravel Default Pagination -->
                {{ $backups->links() }}
            </div>
        </div>
        @else
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    Showing <strong>{{ $backups->count() }}</strong> of <strong>{{ $backups->count() }}</strong> backups
                    @if($backups->count() > 0)
                    @php
                        $totalSize = $backups->sum(function($backup) {
                            return floatval(str_replace(' GB', '', str_replace(' MB', '', $backup->size)));
                        });
                    @endphp
                    <span class="text-muted">| Total size: <strong>{{ number_format($totalSize, 1) }} GB</strong></span>
                    @endif
                </div>
            </div>
        </div>
        @endif
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Select All functionality
    document.getElementById('selectAllBackups').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.backup-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
});

function exportBackupList(format) {
    alert(`Exporting backup list to ${format.toUpperCase()} format...`);
}

function exportBackupReport() {
    alert('Generating comprehensive backup report...');
}

function filterBackups(type) {
    // Remove active class from all buttons
    document.querySelectorAll('[onclick^="filterBackups"]').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Add active class to clicked button
    event.target.classList.add('active');
    
    alert(`Filtering backups by: ${type}`);
}

function bulkBackupAction(action) {
    const selectedBackups = document.querySelectorAll('.backup-checkbox:checked');
    if (selectedBackups.length === 0) {
        alert('Please select backups to perform bulk action.');
        return;
    }
    
    const actionText = action.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
    if (confirm(`Are you sure you want to ${actionText} ${selectedBackups.length} selected backup(s)?`)) {
        alert(`${actionText} action performed on ${selectedBackups.length} backup(s).`);
    }
}

function refreshBackupList() {
    alert('Refreshing backup list...');
}

function downloadBackup(id) {
    alert(`Downloading backup ${id}...`);
}

function verifyBackup(id) {
    alert(`Verifying backup ${id} integrity...`);
}

function restoreBackup(id) {
    if (confirm('Are you sure you want to restore this backup? This action cannot be undone.')) {
        alert(`Restoring backup ${id}...`);
    }
}

function cancelBackup(id) {
    if (confirm('Are you sure you want to cancel this backup operation?')) {
        alert(`Cancelling backup ${id}...`);
    }
}

function deleteBackup(id) {
    if (confirm('Are you sure you want to delete this backup? This action cannot be undone.')) {
        alert(`Deleting backup ${id}...`);
    }
}
</script>
@endpush
