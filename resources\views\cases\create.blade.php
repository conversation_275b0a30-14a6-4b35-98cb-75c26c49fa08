@extends('layouts.app')

@section('title', 'Create New Case  - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Create New Case</h1>
            <x-breadcrumb :items="[
                ['title' => 'Case Management'],
                ['title' => 'Active Cases', 'url' => route('cases.active')],
                ['title' => 'Create New Case']
            ]" />
        </div>
        <div>
            <a href="{{ route('cases.active') }}" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Cases
            </a>
        </div>
    </div>

    <form method="POST" action="#" enctype="multipart/form-data">
        @csrf
        
        <!-- Case Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="folder" class="icon-sm me-2"></i>
                    Case Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="case_title" class="form-label">Case Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="case_title" name="case_title" required
                               placeholder="e.g., Armed Robbery at Shoprite Mall">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="case_type" class="form-label">Case Type <span class="text-danger">*</span></label>
                        <select class="form-select" id="case_type" name="case_type" required>
                            <option value="">Select Case Type</option>
                            <option value="Theft">Theft</option>
                            <option value="Robbery">Robbery</option>
                            <option value="Assault">Assault</option>
                            <option value="Murder">Murder</option>
                            <option value="Drug Offense">Drug Offense</option>
                            <option value="Fraud">Fraud</option>
                            <option value="Domestic Violence">Domestic Violence</option>
                            <option value="Burglary">Burglary</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                        <select class="form-select" id="priority" name="priority" required>
                            <option value="">Select Priority</option>
                            <option value="Low">Low</option>
                            <option value="Medium">Medium</option>
                            <option value="High">High</option>
                            <option value="Critical">Critical</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="case_description" class="form-label">Case Description</label>
                        <textarea class="form-control" id="case_description" name="case_description" rows="4"
                                  placeholder="Detailed description of the case..."></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Incident Details -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="map-pin" class="icon-sm me-2"></i>
                    Incident Details
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="incident_date" class="form-label">Incident Date</label>
                        <input type="date" class="form-control" id="incident_date" name="incident_date">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="incident_time" class="form-label">Incident Time</label>
                        <input type="time" class="form-control" id="incident_time" name="incident_time">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="incident_location" class="form-label">Incident Location</label>
                        <input type="text" class="form-control" id="incident_location" name="incident_location"
                               placeholder="e.g., Shoprite Mall, Lilongwe">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="district" class="form-label">District</label>
                        <select class="form-select" id="district" name="district">
                            <option value="">Select District</option>
                            <option value="Lilongwe">Lilongwe</option>
                            <option value="Blantyre">Blantyre</option>
                            <option value="Mzuzu">Mzuzu</option>
                            <option value="Zomba">Zomba</option>
                            <option value="Kasungu">Kasungu</option>
                            <option value="Mangochi">Mangochi</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="traditional_authority" class="form-label">Traditional Authority</label>
                        <input type="text" class="form-control" id="traditional_authority" name="traditional_authority"
                               placeholder="e.g., Malengachanzi">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="village" class="form-label">Village</label>
                        <input type="text" class="form-control" id="village" name="village"
                               placeholder="e.g., Makuta">
                    </div>
                </div>
            </div>
        </div>

        <!-- Reporting Officer -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="user-check" class="icon-sm me-2"></i>
                    Reporting Officer
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="reporting_officer" class="form-label">Reporting Officer</label>
                        <select class="form-select" id="reporting_officer" name="reporting_officer">
                            <option value="">Select Officer</option>
                            <option value="1">Inspector Banda</option>
                            <option value="2">Sergeant Mwale</option>
                            <option value="3">Constable Phiri</option>
                            <option value="4">Inspector Kumwenda</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="report_date" class="form-label">Report Date</label>
                        <input type="date" class="form-control" id="report_date" name="report_date" 
                               value="{{ date('Y-m-d') }}">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="report_number" class="form-label">Police Report Number</label>
                        <input type="text" class="form-control" id="report_number" name="report_number"
                               placeholder="e.g., PR/2024/001">
                    </div>
                </div>
            </div>
        </div>

        <!-- Suspects/Criminals -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i data-feather="users" class="icon-sm me-2"></i>
                    Suspects/Criminals
                </h4>
                <button type="button" class="btn btn-sm btn-outline-primary" id="addSuspect">
                    <i data-feather="plus" class="icon-xs me-2"></i>
                    Add Suspect
                </button>
            </div>
            <div class="card-body">
                <div id="suspectsContainer">
                    <div class="row suspect-row">
                        <div class="col-md-5 mb-3">
                            <label class="form-label">Criminal</label>
                            <select class="form-select" name="suspects[]">
                                <option value="">Select Criminal</option>
                                <option value="1">Chisomo Phiri (CRM20240001)</option>
                                <option value="2">Kondwani Banda (CRM20240002)</option>
                                <option value="3">Chikondi Mwale (CRM20240003)</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Role in Case</label>
                            <select class="form-select" name="suspect_roles[]">
                                <option value="">Select Role</option>
                                <option value="Primary Suspect">Primary Suspect</option>
                                <option value="Accomplice">Accomplice</option>
                                <option value="Witness">Witness</option>
                                <option value="Victim">Victim</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" name="suspect_status[]">
                                <option value="">Select Status</option>
                                <option value="At Large">At Large</option>
                                <option value="In Custody">In Custody</option>
                                <option value="Released on Bail">Released on Bail</option>
                                <option value="Convicted">Convicted</option>
                            </select>
                        </div>
                        <div class="col-md-1 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-outline-danger w-100 remove-suspect">
                                <i data-feather="trash-2" class="icon-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Initial Evidence -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="archive" class="icon-sm me-2"></i>
                    Initial Evidence & Notes
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="initial_evidence" class="form-label">Initial Evidence Description</label>
                        <textarea class="form-control" id="initial_evidence" name="initial_evidence" rows="3"
                                  placeholder="Describe any initial evidence collected..."></textarea>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="case_notes" class="form-label">Case Notes</label>
                        <textarea class="form-control" id="case_notes" name="case_notes" rows="4"
                                  placeholder="Additional notes, observations, or special instructions..."></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-end gap-2 mb-4">
            <a href="{{ route('cases.active') }}" class="btn btn-outline-secondary">
                <i data-feather="x" class="icon-xs me-2"></i>
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i data-feather="save" class="icon-xs me-2"></i>
                Create Case
            </button>
        </div>
    </form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Add suspect functionality
    document.getElementById('addSuspect').addEventListener('click', function() {
        const container = document.getElementById('suspectsContainer');
        const firstRow = container.querySelector('.suspect-row');
        const newRow = firstRow.cloneNode(true);
        
        // Clear values in new row
        newRow.querySelectorAll('select').forEach(select => select.value = '');
        
        container.appendChild(newRow);
        feather.replace();
    });
    
    // Remove suspect functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-suspect')) {
            const container = document.getElementById('suspectsContainer');
            if (container.children.length > 1) {
                e.target.closest('.suspect-row').remove();
            }
        }
    });
});
</script>
@endpush
