<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EvidenceResource\Pages;
use App\Filament\Resources\EvidenceResource\RelationManagers;
use App\Models\Evidence;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EvidenceResource extends Resource
{
    protected static ?string $model = Evidence::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('evidence_number')
                    ->required()
                    ->maxLength(30),
                Forms\Components\TextInput::make('slug')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(200),
                Forms\Components\Textarea::make('description')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('type')
                    ->required(),
                Forms\Components\TextInput::make('category')
                    ->required(),
                Forms\Components\TextInput::make('case_id')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('criminal_id')
                    ->numeric()
                    ->default(null),
                Forms\Components\DateTimePicker::make('collected_at')
                    ->required(),
                Forms\Components\TextInput::make('collected_by')
                    ->required()
                    ->maxLength(200),
                Forms\Components\Textarea::make('collection_location')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('collection_method')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('collection_notes')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('quantity')
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\TextInput::make('weight')
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\TextInput::make('dimensions')
                    ->maxLength(200)
                    ->default(null),
                Forms\Components\TextInput::make('color')
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\TextInput::make('material')
                    ->maxLength(200)
                    ->default(null),
                Forms\Components\TextInput::make('condition')
                    ->maxLength(200)
                    ->default(null),
                Forms\Components\TextInput::make('storage_location')
                    ->required()
                    ->maxLength(200),
                Forms\Components\TextInput::make('storage_container')
                    ->maxLength(200)
                    ->default(null),
                Forms\Components\TextInput::make('storage_conditions')
                    ->required(),
                Forms\Components\Textarea::make('storage_notes')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('current_custodian_id')
                    ->required()
                    ->numeric(),
                Forms\Components\DateTimePicker::make('custody_received_at')
                    ->required(),
                Forms\Components\Textarea::make('custody_notes')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\TextInput::make('disposition')
                    ->required(),
                Forms\Components\DatePicker::make('disposition_date'),
                Forms\Components\Textarea::make('disposition_notes')
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('is_admissible')
                    ->required(),
                Forms\Components\Textarea::make('admissibility_notes')
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('requires_special_handling')
                    ->required(),
                Forms\Components\Textarea::make('special_handling_instructions')
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('requires_analysis')
                    ->required(),
                Forms\Components\TextInput::make('analysis_type')
                    ->maxLength(200)
                    ->default(null),
                Forms\Components\TextInput::make('analysis_status')
                    ->required(),
                Forms\Components\DateTimePicker::make('analysis_requested_at'),
                Forms\Components\DateTimePicker::make('analysis_completed_at'),
                Forms\Components\Textarea::make('analysis_results')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('analyzed_by')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('estimated_value')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('serial_number')
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\TextInput::make('brand_model')
                    ->maxLength(200)
                    ->default(null),
                Forms\Components\Textarea::make('additional_notes')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('created_by')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('updated_by')
                    ->numeric()
                    ->default(null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('evidence_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('slug')
                    ->searchable(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type'),
                Tables\Columns\TextColumn::make('category'),
                Tables\Columns\TextColumn::make('case_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('criminal_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('collected_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('collected_by')
                    ->searchable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->searchable(),
                Tables\Columns\TextColumn::make('weight')
                    ->searchable(),
                Tables\Columns\TextColumn::make('dimensions')
                    ->searchable(),
                Tables\Columns\TextColumn::make('color')
                    ->searchable(),
                Tables\Columns\TextColumn::make('material')
                    ->searchable(),
                Tables\Columns\TextColumn::make('condition')
                    ->searchable(),
                Tables\Columns\TextColumn::make('storage_location')
                    ->searchable(),
                Tables\Columns\TextColumn::make('storage_container')
                    ->searchable(),
                Tables\Columns\TextColumn::make('storage_conditions'),
                Tables\Columns\TextColumn::make('current_custodian_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('custody_received_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('disposition'),
                Tables\Columns\TextColumn::make('disposition_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_admissible')
                    ->boolean(),
                Tables\Columns\IconColumn::make('requires_special_handling')
                    ->boolean(),
                Tables\Columns\IconColumn::make('requires_analysis')
                    ->boolean(),
                Tables\Columns\TextColumn::make('analysis_type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('analysis_status'),
                Tables\Columns\TextColumn::make('analysis_requested_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('analysis_completed_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('analyzed_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('estimated_value')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('serial_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('brand_model')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_by')
                    ->numeric()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEvidence::route('/'),
            'create' => Pages\CreateEvidence::route('/create'),
            'edit' => Pages\EditEvidence::route('/{record}/edit'),
        ];
    }
}
