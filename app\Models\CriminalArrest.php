<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class CriminalArrest extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'criminal_id',
        'arrest_date',
        'arrest_time',
        'arrest_location',
        'arrest_report_number',
        'arresting_officer_id',
        'arrest_reason',
        'charges',
        'other_offenses',
        'status',
        'notes',
    ];

    protected $casts = [
        'arrest_date' => 'date',
        'arrest_time' => 'datetime:H:i',
    ];

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['arrest_date', 'charges', 'status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the criminal that owns the arrest.
     */
    public function criminal()
    {
        return $this->belongsTo(Criminal::class);
    }

    /**
     * Get the arresting officer.
     */
    public function arrestingOfficer()
    {
        return $this->belongsTo(User::class, 'arresting_officer_id');
    }

    /**
     * Scope for active arrests.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    /**
     * Scope for recent arrests.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('arrest_date', '>=', now()->subDays($days));
    }
}
