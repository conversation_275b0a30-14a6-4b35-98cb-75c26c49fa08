# Evidence Management System Task

## Overview
Implement a comprehensive Evidence and Convicts' Belongings Management System that manages evidence and personal belongings collected during arrests, ensuring secure storage, tracking, and compliance with Malawian legal standards.

## Requirements (from System-Specification-Document.md)

### Evidence Management Module

#### Evidence List
- **Item Description**: e.g., knife, phone
- **Category**: Physical, Digital, Documentary
- **Subcategory**: e.g., DNA, Fingerprints, Video
- **Collection Date**: DD/MM/YYYY format
- **Collector's Name**: e.g., Officer <PERSON><PERSON><PERSON><PERSON>
- **Storage Location**: e.g., Lilongwe Evidence Room, Shelf A1
- **Status**: e.g., Analyzed, Pending Analysis

#### Evidence Details
- View and edit individual evidence details
- Comprehensive evidence information management
- Integration with case files and criminal profiles

#### Chain of Custody
- **Transfer Dates**: DD/MM/YYYY HH:MM format
- **Recipient's Name**: Officer or department receiving evidence
- **Purpose of Transfer**: e.g., forensic analysis
- **Digital Signatures**: For electronic transfers
- Complete audit trail of evidence handling

#### Analysis Results
- Store forensic reports (e.g., DNA match, ballistic results)
- Link analysis results to specific evidence items
- Integration with forensic analysis software

### Personal Belongings Inventory Module

#### Inventory List
- **Item Description**: e.g., wallet, clothing
- **Category**: e.g., Clothing, Jewelry, Documents
- **Condition**: e.g., Good, Damaged
- **Valuation**: in MWK (Malawian Kwacha), optional
- **Collection Date**: DD/MM/YYYY format
- **Storage Location**: e.g., Blantyre Property Room, Bin B2
- **Status**: e.g., Stored, Returned

#### Item Details
- View and edit individual item details
- Comprehensive item information management
- Photo documentation of items

#### Owner Information
- **Name**: e.g., Chisomo Phiri
- **Contact Info**: e.g., +265999123456
- **Verification Details**: ID verification for item return

### Custodial Items Workflow

#### Intake Process
- Record personal belongings upon arrest
- Generate unique item identification numbers
- Photo documentation of items
- Initial condition assessment

#### Storage Process
- Assign storage location and track item status
- Barcode/QR code generation for tracking
- Secure storage with access logging
- Regular inventory audits

#### Release Process
- Verify owner identity before item return
- Document release with signatures
- Update item status to "Returned"
- Generate release receipt

#### Disposal Process
- Handle unclaimed items according to policy
- Auction or destruction procedures
- Legal compliance for disposal
- Documentation of disposal actions

## Technical Implementation

### Database Schema

#### Evidence Table
```sql
CREATE TABLE evidence (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    evidence_id VARCHAR(50) UNIQUE NOT NULL,
    case_id BIGINT,
    item_description TEXT NOT NULL,
    category ENUM('Physical', 'Digital', 'Documentary') NOT NULL,
    subcategory VARCHAR(100),
    collection_date DATE NOT NULL,
    collector_id BIGINT,
    storage_location VARCHAR(200),
    status ENUM('Collected', 'Analyzed', 'Stored', 'Disposed') DEFAULT 'Collected',
    hash_value VARCHAR(255), -- For digital evidence integrity
    file_path VARCHAR(500), -- For digital evidence files
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (case_id) REFERENCES cases(id),
    FOREIGN KEY (collector_id) REFERENCES users(id)
);
```

#### Chain of Custody Table
```sql
CREATE TABLE chain_of_custody (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    evidence_id BIGINT NOT NULL,
    transfer_date DATETIME NOT NULL,
    from_person_id BIGINT,
    to_person_id BIGINT NOT NULL,
    purpose VARCHAR(200),
    location VARCHAR(200),
    digital_signature TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (evidence_id) REFERENCES evidence(id) ON DELETE CASCADE,
    FOREIGN KEY (from_person_id) REFERENCES users(id),
    FOREIGN KEY (to_person_id) REFERENCES users(id)
);
```

#### Personal Belongings Table
```sql
CREATE TABLE personal_belongings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    item_id VARCHAR(50) UNIQUE NOT NULL,
    criminal_id BIGINT,
    arrest_id BIGINT,
    item_description TEXT NOT NULL,
    category ENUM('Clothing', 'Jewelry', 'Documents', 'Electronics', 'Other') NOT NULL,
    condition_status ENUM('Good', 'Damaged', 'Poor') DEFAULT 'Good',
    valuation DECIMAL(10,2), -- In MWK
    collection_date DATE NOT NULL,
    storage_location VARCHAR(200),
    status ENUM('Stored', 'Returned', 'Disposed') DEFAULT 'Stored',
    owner_name VARCHAR(200),
    owner_contact VARCHAR(50),
    release_date DATE NULL,
    released_to VARCHAR(200),
    disposal_date DATE NULL,
    disposal_method VARCHAR(100),
    photos JSON, -- Array of photo file paths
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (criminal_id) REFERENCES criminals(id),
    FOREIGN KEY (arrest_id) REFERENCES criminal_arrests(id)
);
```

#### Forensic Analysis Table
```sql
CREATE TABLE forensic_analysis (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    evidence_id BIGINT NOT NULL,
    analysis_type ENUM('DNA', 'Fingerprint', 'Ballistic', 'Digital', 'Chemical', 'Other') NOT NULL,
    analysis_date DATE NOT NULL,
    analyst_name VARCHAR(200),
    lab_name VARCHAR(200),
    results TEXT,
    report_file_path VARCHAR(500),
    match_found BOOLEAN DEFAULT FALSE,
    match_details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (evidence_id) REFERENCES evidence(id) ON DELETE CASCADE
);
```

### Laravel Models

#### Evidence Model
```php
class Evidence extends Model
{
    protected $fillable = [
        'evidence_id', 'case_id', 'item_description', 'category', 'subcategory',
        'collection_date', 'collector_id', 'storage_location', 'status',
        'hash_value', 'file_path', 'metadata'
    ];

    protected $casts = [
        'collection_date' => 'date',
        'metadata' => 'array'
    ];

    // Relationships
    public function case() {
        return $this->belongsTo(CriminalCase::class, 'case_id');
    }

    public function collector() {
        return $this->belongsTo(User::class, 'collector_id');
    }

    public function chainOfCustody() {
        return $this->hasMany(ChainOfCustody::class);
    }

    public function forensicAnalysis() {
        return $this->hasMany(ForensicAnalysis::class);
    }

    // Scopes
    public function scopeByCategory($query, $category) {
        return $query->where('category', $category);
    }

    public function scopePendingAnalysis($query) {
        return $query->where('status', 'Collected');
    }
}
```

#### PersonalBelonging Model
```php
class PersonalBelonging extends Model
{
    protected $fillable = [
        'item_id', 'criminal_id', 'arrest_id', 'item_description', 'category',
        'condition_status', 'valuation', 'collection_date', 'storage_location',
        'status', 'owner_name', 'owner_contact', 'release_date', 'released_to',
        'disposal_date', 'disposal_method', 'photos'
    ];

    protected $casts = [
        'collection_date' => 'date',
        'release_date' => 'date',
        'disposal_date' => 'date',
        'photos' => 'array',
        'valuation' => 'decimal:2'
    ];

    // Relationships
    public function criminal() {
        return $this->belongsTo(Criminal::class);
    }

    public function arrest() {
        return $this->belongsTo(CriminalArrest::class, 'arrest_id');
    }

    // Scopes
    public function scopeStored($query) {
        return $query->where('status', 'Stored');
    }

    public function scopeUnclaimed($query, $days = 90) {
        return $query->where('status', 'Stored')
                    ->where('collection_date', '<', now()->subDays($days));
    }
}
```