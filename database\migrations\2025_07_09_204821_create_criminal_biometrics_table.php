<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('criminal_biometrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('criminal_id')->constrained('criminals')->onDelete('cascade');

            // Biometric Information
            $table->enum('biometric_type', ['Fingerprint', 'DNA', 'Voice', 'Face', 'Photo']);
            $table->string('file_path', 500)->nullable();
            $table->string('file_hash', 255)->nullable();
            $table->json('metadata')->nullable();

            // Collection Information
            $table->date('collected_date')->nullable();
            $table->foreignId('collected_by')->nullable()->constrained('users')->onDelete('set null');
            $table->boolean('verified')->default(false);

            // Additional Information
            $table->text('description')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['criminal_id']);
            $table->index(['biometric_type']);
            $table->index(['collected_date']);
            $table->index(['verified']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('criminal_biometrics');
    }
};
