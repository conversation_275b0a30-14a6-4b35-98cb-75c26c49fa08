<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user if it doesn't exist
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => bcrypt('password'),
                'badge_number' => 'ADM001',
                'phone' => '+265 999 000 001',
                'department' => 'System Administration',
                'status' => 'active',
                'last_login' => now()->subHours(2),
                'is_online' => true,
                'email_verified_at' => now(),
            ]
        );

        if (!$admin->hasRole('Super Administrator')) {
            $admin->assignRole('Super Administrator');
        }

        // Create sample police officers
        $officers = [
            [
                'name' => 'Inspector <PERSON>ndwan<PERSON>',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'badge_number' => 'PB001',
                'phone' => '+265 999 123 456',
                'department' => 'Criminal Investigation',
                'status' => 'active',
                'last_login' => now()->subHours(1),
                'is_online' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Sergeant Chikondi Mwale',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'badge_number' => 'PB002',
                'phone' => '+265 999 234 567',
                'department' => 'Traffic Police',
                'status' => 'active',
                'last_login' => now()->subHours(3),
                'is_online' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Constable Thokozani Phiri',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'badge_number' => 'PB003',
                'phone' => '+265 999 345 678',
                'department' => 'Community Policing',
                'status' => 'active',
                'last_login' => now()->subDay(),
                'is_online' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Constable Mwawi Kumwenda',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'badge_number' => 'PB004',
                'phone' => '+265 999 456 789',
                'department' => 'Administration',
                'status' => 'suspended',
                'last_login' => now()->subDays(5),
                'is_online' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Sergeant John Nyirenda',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'badge_number' => 'PB005',
                'phone' => '+265 999 567 890',
                'department' => 'Forensics',
                'status' => 'inactive',
                'last_login' => now()->subWeek(),
                'is_online' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Constable Lucy Mbewe',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'badge_number' => 'PB006',
                'phone' => '+265 999 678 901',
                'department' => 'Community Policing',
                'status' => 'pending',
                'last_login' => null,
                'is_online' => false,
                'email_verified_at' => null,
            ],
            [
                'name' => 'Inspector Grace Tembo',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'badge_number' => 'PB007',
                'phone' => '+265 999 789 012',
                'department' => 'Criminal Investigation',
                'status' => 'active',
                'last_login' => now()->subHours(4),
                'is_online' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Constable James Banda',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'badge_number' => 'PB008',
                'phone' => '+265 999 890 123',
                'department' => 'Traffic Police',
                'status' => 'active',
                'last_login' => now()->subHours(6),
                'is_online' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Sergeant Mary Phiri',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'badge_number' => 'PB009',
                'phone' => '+265 999 901 234',
                'department' => 'Forensics',
                'status' => 'active',
                'last_login' => now()->subHours(8),
                'is_online' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Constable Peter Mwale',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'badge_number' => 'PB010',
                'phone' => '+265 999 012 345',
                'department' => 'Community Policing',
                'status' => 'inactive',
                'last_login' => now()->subDays(10),
                'is_online' => false,
                'email_verified_at' => now(),
            ],
        ];

        foreach ($officers as $officerData) {
            $officer = User::firstOrCreate(
                ['email' => $officerData['email']],
                $officerData
            );

            // Assign roles based on rank if not already assigned
            if (str_contains($officer->name, 'Inspector') && !$officer->hasRole('Inspector')) {
                $officer->assignRole('Inspector');
            } elseif (str_contains($officer->name, 'Sergeant') && !$officer->hasRole('Officer')) {
                $officer->assignRole('Officer');
            } elseif (!$officer->hasRole('Officer')) {
                $officer->assignRole('Officer');
            }
        }
    }
}
